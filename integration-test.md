## Off-chain Changes

You can run the integration test by running the following command:
```bash
python3 integration_test.py
```
This will attempt to see whether the fuzzer can hit `bug()` in all projects in tests/ folder.


## On-chain Changes
Ensure you have a local proxy running at `http://localhost:5003`.

Following command shall find bugs:
```bash
./target/release/cli -o -t ******************************************,****************************************** -c POLYGON --onchain-block-number 35690977  -f -i -p --onchain-local-proxy-addr http://localhost:5003
./target/release/cli -o -t ******************************************,******************************************,****************************************** -c BSC --onchain-block-number 23695904 -f -i --onchain-local-proxy-addr http://localhost:5003
```

Following command shall not find bugs:
```bash

```