# This file is automatically @generated by Car<PERSON>.
# It is not intended for manual editing.
version = 3

[[package]]
name = "Inflector"
version = "0.11.4"
source = "registry+https://github.com/rust-lang/crates.io-index"
checksum = "fe438c63458706e03479442743baae6c88256498e6431708f6dfc520a26515d3"
dependencies = [
 "lazy_static",
 "regex",
]

[[package]]
name = "addr2line"
version = "0.19.0"
source = "registry+https://github.com/rust-lang/crates.io-index"
checksum = "a76fd60b23679b7d19bd066031410fb7e458ccc5e958eb5c325888ce4baedc97"
dependencies = [
 "gimli 0.27.3",
]

[[package]]
name = "addr2line"
version = "0.21.0"
source = "registry+https://github.com/rust-lang/crates.io-index"
checksum = "8a30b2e23b9e17a9f90641c7ab1549cd9b44f296d3ccbf309d2863cfe398a0cb"
dependencies = [
 "gimli 0.28.1",
]

[[package]]
name = "adler"
version = "1.0.2"
source = "registry+https://github.com/rust-lang/crates.io-index"
checksum = "f26201604c87b1e01bd3d98f8d5d9a8fcbb815e8cedb41ffccbeb4bf593a35fe"

[[package]]
name = "aead"
version = "0.5.2"
source = "registry+https://github.com/rust-lang/crates.io-index"
checksum = "d122413f284cf2d62fb1b7db97e02edb8cda96d769b16e443a4f6195e35662b0"
dependencies = [
 "crypto-common",
 "generic-array 0.14.7",
]

[[package]]
name = "aes"
version = "0.8.4"
source = "registry+https://github.com/rust-lang/crates.io-index"
checksum = "b169f7a6d4742236a0a00c541b845991d0ac43e546831af1249753ab4c3aa3a0"
dependencies = [
 "cfg-if 1.0.0",
 "cipher",
 "cpufeatures",
]

[[package]]
name = "aes-gcm"
version = "0.10.3"
source = "registry+https://github.com/rust-lang/crates.io-index"
checksum = "831010a0f742e1209b3bcea8fab6a8e149051ba6099432c8cb2cc117dec3ead1"
dependencies = [
 "aead",
 "aes",
 "cipher",
 "ctr",
 "ghash",
 "subtle",
]

[[package]]
name = "ahash"
version = "0.7.8"
source = "registry+https://github.com/rust-lang/crates.io-index"
checksum = "891477e0c6a8957309ee5c45a6368af3ae14bb510732d2684ffa19af310920f9"
dependencies = [
 "getrandom 0.2.12",
 "once_cell",
 "version_check",
]

[[package]]
name = "ahash"
version = "0.8.11"
source = "registry+https://github.com/rust-lang/crates.io-index"
checksum = "e89da841a80418a9b391ebaea17f5c112ffaaa96f621d2c285b5174da76b9011"
dependencies = [
 "cfg-if 1.0.0",
 "getrandom 0.2.12",
 "once_cell",
 "version_check",
 "zerocopy",
]

[[package]]
name = "aho-corasick"
version = "0.7.20"
source = "registry+https://github.com/rust-lang/crates.io-index"
checksum = "cc936419f96fa211c1b9166887b38e5e40b19958e5b895be7c1f93adec7071ac"
dependencies = [
 "memchr",
]

[[package]]
name = "aho-corasick"
version = "1.1.3"
source = "registry+https://github.com/rust-lang/crates.io-index"
checksum = "8e60d3430d3a69478ad0993f19238d2df97c507009a52b3c10addcd7f6bcb916"
dependencies = [
 "memchr",
]

[[package]]
name = "aliasable"
version = "0.1.3"
source = "registry+https://github.com/rust-lang/crates.io-index"
checksum = "250f629c0161ad8107cf89319e990051fae62832fd343083bea452d93e2205fd"

[[package]]
name = "alloc-no-stdlib"
version = "2.0.4"
source = "registry+https://github.com/rust-lang/crates.io-index"
checksum = "cc7bb162ec39d46ab1ca8c77bf72e890535becd1751bb45f64c597edb4c8c6b3"

[[package]]
name = "alloc-stdlib"
version = "0.2.2"
source = "registry+https://github.com/rust-lang/crates.io-index"
checksum = "94fb8275041c72129eb51b7d0322c29b8387a0386127718b096429201a5d6ece"
dependencies = [
 "alloc-no-stdlib",
]

[[package]]
name = "allocator-api2"
version = "0.2.16"
source = "registry+https://github.com/rust-lang/crates.io-index"
checksum = "0942ffc6dcaadf03badf6e6a2d0228460359d5e34b57ccdc720b7382dfbd5ec5"

[[package]]
name = "alloy-chains"
version = "0.1.15"
source = "registry+https://github.com/rust-lang/crates.io-index"
checksum = "e96c81b05c893348760f232c4cc6a6a77fd91cfb09885d4eaad25cd03bd7732e"
dependencies = [
 "num_enum 0.7.2",
 "serde",
 "strum 0.26.2",
]

[[package]]
name = "alloy-dyn-abi"
version = "0.6.4"
source = "registry+https://github.com/rust-lang/crates.io-index"
checksum = "2919acdad13336bc5dc26b636cdd6892c2f27fb0d4a58320a00c2713cf6a4e9a"
dependencies = [
 "alloy-json-abi",
 "alloy-primitives",
 "alloy-sol-type-parser",
 "alloy-sol-types",
 "arbitrary",
 "const-hex",
 "derive_arbitrary",
 "derive_more",
 "itoa",
 "proptest",
 "serde",
 "serde_json",
 "winnow 0.6.5",
]

[[package]]
name = "alloy-eips"
version = "0.1.0"
source = "git+https://github.com/alloy-rs/alloy?rev=9ac2c90#9ac2c90d58a9994d4b61c879e33c6af2739a2b4f"
dependencies = [
 "alloy-primitives",
 "alloy-rlp",
 "serde",
 "thiserror",
]

[[package]]
name = "alloy-genesis"
version = "0.1.0"
source = "git+https://github.com/alloy-rs/alloy?rev=9ac2c90#9ac2c90d58a9994d4b61c879e33c6af2739a2b4f"
dependencies = [
 "alloy-primitives",
 "alloy-rpc-types",
 "serde",
]

[[package]]
name = "alloy-json-abi"
version = "0.6.4"
source = "registry+https://github.com/rust-lang/crates.io-index"
checksum = "24ed0f2a6c3a1c947b4508522a53a190dba8f94dcd4e3e1a5af945a498e78f2f"
dependencies = [
 "alloy-primitives",
 "alloy-sol-type-parser",
 "serde",
 "serde_json",
]

[[package]]
name = "alloy-json-rpc"
version = "0.1.0"
source = "git+https://github.com/alloy-rs/alloy?rev=9ac2c90#9ac2c90d58a9994d4b61c879e33c6af2739a2b4f"
dependencies = [
 "alloy-primitives",
 "serde",
 "serde_json",
 "thiserror",
]

[[package]]
name = "alloy-network"
version = "0.1.0"
source = "git+https://github.com/alloy-rs/alloy?rev=9ac2c90#9ac2c90d58a9994d4b61c879e33c6af2739a2b4f"
dependencies = [
 "alloy-eips",
 "alloy-json-rpc",
 "alloy-primitives",
 "alloy-rlp",
 "serde",
]

[[package]]
name = "alloy-primitives"
version = "0.6.4"
source = "registry+https://github.com/rust-lang/crates.io-index"
checksum = "600d34d8de81e23b6d909c094e23b3d357e01ca36b78a8c5424c501eedbe86f0"
dependencies = [
 "alloy-rlp",
 "arbitrary",
 "bytes",
 "cfg-if 1.0.0",
 "const-hex",
 "derive_arbitrary",
 "derive_more",
 "ethereum_ssz",
 "getrandom 0.2.12",
 "hex-literal 0.4.1",
 "itoa",
 "k256 0.13.3",
 "keccak-asm",
 "proptest",
 "proptest-derive 0.4.0",
 "rand 0.8.5",
 "ruint",
 "serde",
 "tiny-keccak",
]

[[package]]
name = "alloy-providers"
version = "0.1.0"
source = "git+https://github.com/alloy-rs/alloy?rev=9ac2c90#9ac2c90d58a9994d4b61c879e33c6af2739a2b4f"
dependencies = [
 "alloy-network",
 "alloy-primitives",
 "alloy-rpc-client",
 "alloy-rpc-trace-types",
 "alloy-rpc-types",
 "alloy-transport",
 "alloy-transport-http",
 "async-stream",
 "async-trait",
 "auto_impl",
 "futures",
 "lru 0.12.3",
 "reqwest",
 "serde",
 "thiserror",
 "tokio",
 "tracing",
]

[[package]]
name = "alloy-pubsub"
version = "0.1.0"
source = "git+https://github.com/alloy-rs/alloy?rev=9ac2c90#9ac2c90d58a9994d4b61c879e33c6af2739a2b4f"
dependencies = [
 "alloy-json-rpc",
 "alloy-primitives",
 "alloy-transport",
 "bimap",
 "futures",
 "serde",
 "serde_json",
 "tokio",
 "tokio-stream",
 "tower",
 "tracing",
]

[[package]]
name = "alloy-rlp"
version = "0.3.4"
source = "registry+https://github.com/rust-lang/crates.io-index"
checksum = "8d58d9f5da7b40e9bfff0b7e7816700be4019db97d4b6359fe7f94a9e22e42ac"
dependencies = [
 "alloy-rlp-derive",
 "arrayvec 0.7.4",
 "bytes",
]

[[package]]
name = "alloy-rlp-derive"
version = "0.3.4"
source = "registry+https://github.com/rust-lang/crates.io-index"
checksum = "1a047897373be4bbb0224c1afdabca92648dc57a9c9ef6e7b0be3aff7a859c83"
dependencies = [
 "proc-macro2 1.0.79",
 "quote 1.0.35",
 "syn 2.0.57",
]

[[package]]
name = "alloy-rpc-client"
version = "0.1.0"
source = "git+https://github.com/alloy-rs/alloy?rev=9ac2c90#9ac2c90d58a9994d4b61c879e33c6af2739a2b4f"
dependencies = [
 "alloy-json-rpc",
 "alloy-transport",
 "alloy-transport-http",
 "futures",
 "pin-project",
 "reqwest",
 "serde",
 "serde_json",
 "tokio",
 "tokio-stream",
 "tower",
 "tracing",
 "url",
]

[[package]]
name = "alloy-rpc-trace-types"
version = "0.1.0"
source = "git+https://github.com/alloy-rs/alloy?rev=9ac2c90#9ac2c90d58a9994d4b61c879e33c6af2739a2b4f"
dependencies = [
 "alloy-primitives",
 "alloy-rpc-types",
 "serde",
 "serde_json",
]

[[package]]
name = "alloy-rpc-types"
version = "0.1.0"
source = "git+https://github.com/alloy-rs/alloy?rev=9ac2c90#9ac2c90d58a9994d4b61c879e33c6af2739a2b4f"
dependencies = [
 "alloy-primitives",
 "alloy-rlp",
 "itertools 0.12.1",
 "serde",
 "serde_json",
 "thiserror",
]

[[package]]
name = "alloy-signer"
version = "0.1.0"
source = "git+https://github.com/alloy-rs/alloy?rev=9ac2c90#9ac2c90d58a9994d4b61c879e33c6af2739a2b4f"
dependencies = [
 "alloy-network",
 "alloy-primitives",
 "async-trait",
 "auto_impl",
 "coins-bip32",
 "coins-bip39",
 "elliptic-curve 0.13.8",
 "eth-keystore",
 "k256 0.13.3",
 "rand 0.8.5",
 "thiserror",
]

[[package]]
name = "alloy-sol-macro"
version = "0.6.4"
source = "registry+https://github.com/rust-lang/crates.io-index"
checksum = "e86ec0a47740b20bc5613b8712d0d321d031c4efc58e9645af96085d5cccfc27"
dependencies = [
 "alloy-json-abi",
 "const-hex",
 "dunce",
 "heck 0.4.1",
 "indexmap 2.2.6",
 "proc-macro-error",
 "proc-macro2 1.0.79",
 "quote 1.0.35",
 "serde_json",
 "syn 2.0.57",
 "syn-solidity",
 "tiny-keccak",
]

[[package]]
name = "alloy-sol-type-parser"
version = "0.6.4"
source = "registry+https://github.com/rust-lang/crates.io-index"
checksum = "0045cc89524e1451ccf33e8581355b6027ac7c6e494bb02959d4213ad0d8e91d"
dependencies = [
 "winnow 0.6.5",
]

[[package]]
name = "alloy-sol-types"
version = "0.6.4"
source = "registry+https://github.com/rust-lang/crates.io-index"
checksum = "ad09ec5853fa700d12d778ad224dcdec636af424d29fad84fb9a2f16a5b0ef09"
dependencies = [
 "alloy-json-abi",
 "alloy-primitives",
 "alloy-sol-macro",
 "const-hex",
 "serde",
]

[[package]]
name = "alloy-transport"
version = "0.1.0"
source = "git+https://github.com/alloy-rs/alloy?rev=9ac2c90#9ac2c90d58a9994d4b61c879e33c6af2739a2b4f"
dependencies = [
 "alloy-json-rpc",
 "base64 0.22.0",
 "futures-util",
 "serde",
 "serde_json",
 "thiserror",
 "tokio",
 "tower",
 "url",
 "wasm-bindgen-futures",
]

[[package]]
name = "alloy-transport-http"
version = "0.1.0"
source = "git+https://github.com/alloy-rs/alloy?rev=9ac2c90#9ac2c90d58a9994d4b61c879e33c6af2739a2b4f"
dependencies = [
 "alloy-json-rpc",
 "alloy-transport",
 "reqwest",
 "serde_json",
 "tower",
 "url",
]

[[package]]
name = "alloy-transport-ipc"
version = "0.1.0"
source = "git+https://github.com/alloy-rs/alloy?rev=9ac2c90#9ac2c90d58a9994d4b61c879e33c6af2739a2b4f"
dependencies = [
 "alloy-json-rpc",
 "alloy-pubsub",
 "alloy-transport",
 "bytes",
 "futures",
 "interprocess",
 "pin-project",
 "serde_json",
 "tokio",
 "tokio-util 0.7.10",
 "tracing",
]

[[package]]
name = "alloy-transport-ws"
version = "0.1.0"
source = "git+https://github.com/alloy-rs/alloy?rev=9ac2c90#9ac2c90d58a9994d4b61c879e33c6af2739a2b4f"
dependencies = [
 "alloy-pubsub",
 "alloy-transport",
 "futures",
 "http",
 "serde_json",
 "tokio",
 "tokio-tungstenite",
 "tracing",
 "ws_stream_wasm",
]

[[package]]
name = "android-tzdata"
version = "0.1.1"
source = "registry+https://github.com/rust-lang/crates.io-index"
checksum = "e999941b234f3131b00bc13c22d06e8c5ff726d1b6318ac7eb276997bbb4fef0"

[[package]]
name = "android_system_properties"
version = "0.1.5"
source = "registry+https://github.com/rust-lang/crates.io-index"
checksum = "819e7219dbd41043ac279b19830f2efc897156490d7fd6ea916720117ee66311"
dependencies = [
 "libc",
]

[[package]]
name = "anemo"
version = "0.0.0"
source = "git+https://github.com/mystenlabs/anemo.git?rev=0f0ae8d8f222820a20b586088ea7a2941478a159#0f0ae8d8f222820a20b586088ea7a2941478a159"
dependencies = [
 "anyhow",
 "async-trait",
 "bincode",
 "bytes",
 "ed25519",
 "futures",
 "hex",
 "http",
 "matchit 0.5.0",
 "pin-project-lite",
 "pkcs8 0.9.0",
 "quinn",
 "quinn-proto",
 "rand 0.8.5",
 "rcgen",
 "ring 0.16.20",
 "rustls 0.21.10",
 "serde",
 "serde_json",
 "socket2 0.5.6",
 "tap",
 "thiserror",
 "tokio",
 "tokio-util 0.7.10",
 "tower",
 "tracing",
 "webpki",
 "x509-parser",
]

[[package]]
name = "anemo-build"
version = "0.0.0"
source = "git+https://github.com/mystenlabs/anemo.git?rev=0f0ae8d8f222820a20b586088ea7a2941478a159#0f0ae8d8f222820a20b586088ea7a2941478a159"
dependencies = [
 "prettyplease 0.1.25",
 "proc-macro2 1.0.79",
 "quote 1.0.35",
 "syn 1.0.109",
]

[[package]]
name = "anemo-cli"
version = "0.0.0"
source = "git+https://github.com/mystenlabs/anemo.git?rev=0f0ae8d8f222820a20b586088ea7a2941478a159#0f0ae8d8f222820a20b586088ea7a2941478a159"
dependencies = [
 "anemo",
 "anemo-tower",
 "bytes",
 "clap 4.5.4",
 "dashmap",
 "rand 0.8.5",
 "tokio",
 "tower",
 "tracing",
]

[[package]]
name = "anemo-tower"
version = "0.0.0"
source = "git+https://github.com/mystenlabs/anemo.git?rev=0f0ae8d8f222820a20b586088ea7a2941478a159#0f0ae8d8f222820a20b586088ea7a2941478a159"
dependencies = [
 "anemo",
 "bytes",
 "dashmap",
 "futures",
 "governor",
 "nonzero_ext",
 "pin-project-lite",
 "tokio",
 "tower",
 "tracing",
 "uuid 1.8.0",
]

[[package]]
name = "anes"
version = "0.1.6"
source = "registry+https://github.com/rust-lang/crates.io-index"
checksum = "4b46cbb362ab8752921c97e041f5e366ee6297bd428a31275b9fcf1e380f7299"

[[package]]
name = "ansi_term"
version = "0.12.1"
source = "registry+https://github.com/rust-lang/crates.io-index"
checksum = "d52a9bb7ec0cf484c551830a7ce27bd20d67eac647e1befb56b0be4ee39a55d2"
dependencies = [
 "winapi",
]

[[package]]
name = "anstream"
version = "0.3.2"
source = "registry+https://github.com/rust-lang/crates.io-index"
checksum = "0ca84f3628370c59db74ee214b3263d58f9aadd9b4fe7e711fd87dc452b7f163"
dependencies = [
 "anstyle",
 "anstyle-parse",
 "anstyle-query",
 "anstyle-wincon 1.0.2",
 "colorchoice",
 "is-terminal",
 "utf8parse",
]

[[package]]
name = "anstream"
version = "0.6.13"
source = "registry+https://github.com/rust-lang/crates.io-index"
checksum = "d96bd03f33fe50a863e394ee9718a706f988b9079b20c3784fb726e7678b62fb"
dependencies = [
 "anstyle",
 "anstyle-parse",
 "anstyle-query",
 "anstyle-wincon 3.0.2",
 "colorchoice",
 "utf8parse",
]

[[package]]
name = "anstyle"
version = "1.0.6"
source = "registry+https://github.com/rust-lang/crates.io-index"
checksum = "8901269c6307e8d93993578286ac0edf7f195079ffff5ebdeea6a59ffb7e36bc"

[[package]]
name = "anstyle-parse"
version = "0.2.3"
source = "registry+https://github.com/rust-lang/crates.io-index"
checksum = "c75ac65da39e5fe5ab759307499ddad880d724eed2f6ce5b5e8a26f4f387928c"
dependencies = [
 "utf8parse",
]

[[package]]
name = "anstyle-query"
version = "1.0.2"
source = "registry+https://github.com/rust-lang/crates.io-index"
checksum = "e28923312444cdd728e4738b3f9c9cac739500909bb3d3c94b43551b16517648"
dependencies = [
 "windows-sys 0.52.0",
]

[[package]]
name = "anstyle-wincon"
version = "1.0.2"
source = "registry+https://github.com/rust-lang/crates.io-index"
checksum = "c677ab05e09154296dd37acecd46420c17b9713e8366facafa8fc0885167cf4c"
dependencies = [
 "anstyle",
 "windows-sys 0.48.0",
]

[[package]]
name = "anstyle-wincon"
version = "3.0.2"
source = "registry+https://github.com/rust-lang/crates.io-index"
checksum = "1cd54b81ec8d6180e24654d0b371ad22fc3dd083b6ff8ba325b72e00c87660a7"
dependencies = [
 "anstyle",
 "windows-sys 0.52.0",
]

[[package]]
name = "anyhow"
version = "1.0.81"
source = "registry+https://github.com/rust-lang/crates.io-index"
checksum = "0952808a6c2afd1aa8947271f3a60f1a6763c7b912d210184c5149b5cf147247"
dependencies = [
 "backtrace",
]

[[package]]
name = "arbitrary"
version = "1.3.2"
source = "registry+https://github.com/rust-lang/crates.io-index"
checksum = "7d5a26814d8dcb93b0e5a0ff3c6d80a8843bafb21b39e8e18a6f05471870e110"
dependencies = [
 "derive_arbitrary",
]

[[package]]
name = "arc-swap"
version = "1.7.1"
source = "registry+https://github.com/rust-lang/crates.io-index"
checksum = "69f7f8c3906b62b754cd5326047894316021dcfe5a194c8ea52bdd94934a3457"
dependencies = [
 "serde",
]

[[package]]
name = "ark-bls12-381"
version = "0.4.0"
source = "registry+https://github.com/rust-lang/crates.io-index"
checksum = "c775f0d12169cba7aae4caeb547bb6a50781c7449a8aa53793827c9ec4abf488"
dependencies = [
 "ark-ec",
 "ark-ff 0.4.2",
 "ark-serialize 0.4.2",
 "ark-std 0.4.0",
]

[[package]]
name = "ark-bn254"
version = "0.4.0"
source = "registry+https://github.com/rust-lang/crates.io-index"
checksum = "a22f4561524cd949590d78d7d4c5df8f592430d221f7f3c9497bbafd8972120f"
dependencies = [
 "ark-ec",
 "ark-ff 0.4.2",
 "ark-std 0.4.0",
]

[[package]]
name = "ark-crypto-primitives"
version = "0.4.0"
source = "registry+https://github.com/rust-lang/crates.io-index"
checksum = "1f3a13b34da09176a8baba701233fdffbaa7c1b1192ce031a3da4e55ce1f1a56"
dependencies = [
 "ark-ec",
 "ark-ff 0.4.2",
 "ark-r1cs-std",
 "ark-relations",
 "ark-serialize 0.4.2",
 "ark-snark",
 "ark-std 0.4.0",
 "blake2",
 "derivative",
 "digest 0.10.7",
 "rayon",
 "sha2 0.10.8",
 "tracing",
]

[[package]]
name = "ark-ec"
version = "0.4.2"
source = "registry+https://github.com/rust-lang/crates.io-index"
checksum = "defd9a439d56ac24968cca0571f598a61bc8c55f71d50a89cda591cb750670ba"
dependencies = [
 "ark-ff 0.4.2",
 "ark-poly",
 "ark-serialize 0.4.2",
 "ark-std 0.4.0",
 "derivative",
 "hashbrown 0.13.2",
 "itertools 0.10.5",
 "num-traits",
 "rayon",
 "zeroize",
]

[[package]]
name = "ark-ff"
version = "0.3.0"
source = "registry+https://github.com/rust-lang/crates.io-index"
checksum = "6b3235cc41ee7a12aaaf2c575a2ad7b46713a8a50bda2fc3b003a04845c05dd6"
dependencies = [
 "ark-ff-asm 0.3.0",
 "ark-ff-macros 0.3.0",
 "ark-serialize 0.3.0",
 "ark-std 0.3.0",
 "derivative",
 "num-bigint 0.4.4",
 "num-traits",
 "paste",
 "rustc_version 0.3.3",
 "zeroize",
]

[[package]]
name = "ark-ff"
version = "0.4.2"
source = "registry+https://github.com/rust-lang/crates.io-index"
checksum = "ec847af850f44ad29048935519032c33da8aa03340876d351dfab5660d2966ba"
dependencies = [
 "ark-ff-asm 0.4.2",
 "ark-ff-macros 0.4.2",
 "ark-serialize 0.4.2",
 "ark-std 0.4.0",
 "derivative",
 "digest 0.10.7",
 "itertools 0.10.5",
 "num-bigint 0.4.4",
 "num-traits",
 "paste",
 "rayon",
 "rustc_version 0.4.0",
 "zeroize",
]

[[package]]
name = "ark-ff-asm"
version = "0.3.0"
source = "registry+https://github.com/rust-lang/crates.io-index"
checksum = "db02d390bf6643fb404d3d22d31aee1c4bc4459600aef9113833d17e786c6e44"
dependencies = [
 "quote 1.0.35",
 "syn 1.0.109",
]

[[package]]
name = "ark-ff-asm"
version = "0.4.2"
source = "registry+https://github.com/rust-lang/crates.io-index"
checksum = "3ed4aa4fe255d0bc6d79373f7e31d2ea147bcf486cba1be5ba7ea85abdb92348"
dependencies = [
 "quote 1.0.35",
 "syn 1.0.109",
]

[[package]]
name = "ark-ff-macros"
version = "0.3.0"
source = "registry+https://github.com/rust-lang/crates.io-index"
checksum = "db2fd794a08ccb318058009eefdf15bcaaaaf6f8161eb3345f907222bac38b20"
dependencies = [
 "num-bigint 0.4.4",
 "num-traits",
 "quote 1.0.35",
 "syn 1.0.109",
]

[[package]]
name = "ark-ff-macros"
version = "0.4.2"
source = "registry+https://github.com/rust-lang/crates.io-index"
checksum = "7abe79b0e4288889c4574159ab790824d0033b9fdcb2a112a3182fac2e514565"
dependencies = [
 "num-bigint 0.4.4",
 "num-traits",
 "proc-macro2 1.0.79",
 "quote 1.0.35",
 "syn 1.0.109",
]

[[package]]
name = "ark-groth16"
version = "0.4.0"
source = "registry+https://github.com/rust-lang/crates.io-index"
checksum = "20ceafa83848c3e390f1cbf124bc3193b3e639b3f02009e0e290809a501b95fc"
dependencies = [
 "ark-crypto-primitives",
 "ark-ec",
 "ark-ff 0.4.2",
 "ark-poly",
 "ark-relations",
 "ark-serialize 0.4.2",
 "ark-std 0.4.0",
 "rayon",
]

[[package]]
name = "ark-poly"
version = "0.4.2"
source = "registry+https://github.com/rust-lang/crates.io-index"
checksum = "d320bfc44ee185d899ccbadfa8bc31aab923ce1558716e1997a1e74057fe86bf"
dependencies = [
 "ark-ff 0.4.2",
 "ark-serialize 0.4.2",
 "ark-std 0.4.0",
 "derivative",
 "hashbrown 0.13.2",
 "rayon",
]

[[package]]
name = "ark-r1cs-std"
version = "0.4.0"
source = "registry+https://github.com/rust-lang/crates.io-index"
checksum = "de1d1472e5cb020cb3405ce2567c91c8d43f21b674aef37b0202f5c3304761db"
dependencies = [
 "ark-ec",
 "ark-ff 0.4.2",
 "ark-relations",
 "ark-std 0.4.0",
 "derivative",
 "num-bigint 0.4.4",
 "num-integer",
 "num-traits",
 "tracing",
]

[[package]]
name = "ark-relations"
version = "0.4.0"
source = "registry+https://github.com/rust-lang/crates.io-index"
checksum = "00796b6efc05a3f48225e59cb6a2cda78881e7c390872d5786aaf112f31fb4f0"
dependencies = [
 "ark-ff 0.4.2",
 "ark-std 0.4.0",
 "tracing",
 "tracing-subscriber 0.2.25",
]

[[package]]
name = "ark-secp256r1"
version = "0.4.0"
source = "registry+https://github.com/rust-lang/crates.io-index"
checksum = "3975a01b0a6e3eae0f72ec7ca8598a6620fc72fa5981f6f5cca33b7cd788f633"
dependencies = [
 "ark-ec",
 "ark-ff 0.4.2",
 "ark-std 0.4.0",
]

[[package]]
name = "ark-serialize"
version = "0.3.0"
source = "registry+https://github.com/rust-lang/crates.io-index"
checksum = "1d6c2b318ee6e10f8c2853e73a83adc0ccb88995aa978d8a3408d492ab2ee671"
dependencies = [
 "ark-std 0.3.0",
 "digest 0.9.0",
]

[[package]]
name = "ark-serialize"
version = "0.4.2"
source = "registry+https://github.com/rust-lang/crates.io-index"
checksum = "adb7b85a02b83d2f22f89bd5cac66c9c89474240cb6207cb1efc16d098e822a5"
dependencies = [
 "ark-serialize-derive",
 "ark-std 0.4.0",
 "digest 0.10.7",
 "num-bigint 0.4.4",
]

[[package]]
name = "ark-serialize-derive"
version = "0.4.2"
source = "registry+https://github.com/rust-lang/crates.io-index"
checksum = "ae3281bc6d0fd7e549af32b52511e1302185bd688fd3359fa36423346ff682ea"
dependencies = [
 "proc-macro2 1.0.79",
 "quote 1.0.35",
 "syn 1.0.109",
]

[[package]]
name = "ark-snark"
version = "0.4.0"
source = "registry+https://github.com/rust-lang/crates.io-index"
checksum = "84d3cc6833a335bb8a600241889ead68ee89a3cf8448081fb7694c0fe503da63"
dependencies = [
 "ark-ff 0.4.2",
 "ark-relations",
 "ark-serialize 0.4.2",
 "ark-std 0.4.0",
]

[[package]]
name = "ark-std"
version = "0.3.0"
source = "registry+https://github.com/rust-lang/crates.io-index"
checksum = "1df2c09229cbc5a028b1d70e00fdb2acee28b1055dfb5ca73eea49c5a25c4e7c"
dependencies = [
 "num-traits",
 "rand 0.8.5",
]

[[package]]
name = "ark-std"
version = "0.4.0"
source = "registry+https://github.com/rust-lang/crates.io-index"
checksum = "94893f1e0c6eeab764ade8dc4c0db24caf4fe7cbbaafc0eba0a9030f447b5185"
dependencies = [
 "num-traits",
 "rand 0.8.5",
 "rayon",
]

[[package]]
name = "arrayref"
version = "0.3.7"
source = "registry+https://github.com/rust-lang/crates.io-index"
checksum = "6b4930d2cb77ce62f89ee5d5289b4ac049559b1c45539271f5ed4fdc7db34545"

[[package]]
name = "arrayvec"
version = "0.5.2"
source = "registry+https://github.com/rust-lang/crates.io-index"
checksum = "23b62fc65de8e4e7f52534fb52b0f3ed04746ae267519eef2a83941e8085068b"

[[package]]
name = "arrayvec"
version = "0.7.4"
source = "registry+https://github.com/rust-lang/crates.io-index"
checksum = "96d30a06541fbafbc7f82ed10c06164cfbd2c401138f6addd8404629c4b16711"

[[package]]
name = "as-slice"
version = "0.1.5"
source = "registry+https://github.com/rust-lang/crates.io-index"
checksum = "45403b49e3954a4b8428a0ac21a4b7afadccf92bfd96273f1a58cd4812496ae0"
dependencies = [
 "generic-array 0.12.4",
 "generic-array 0.13.3",
 "generic-array 0.14.7",
 "stable_deref_trait",
]

[[package]]
name = "ascii-canvas"
version = "3.0.0"
source = "registry+https://github.com/rust-lang/crates.io-index"
checksum = "8824ecca2e851cec16968d54a01dd372ef8f95b244fb84b84e70128be347c3c6"
dependencies = [
 "term",
]

[[package]]
name = "asn1-rs"
version = "0.5.2"
source = "registry+https://github.com/rust-lang/crates.io-index"
checksum = "7f6fd5ddaf0351dff5b8da21b2fb4ff8e08ddd02857f0bf69c47639106c0fff0"
dependencies = [
 "asn1-rs-derive",
 "asn1-rs-impl",
 "displaydoc",
 "nom 7.1.3",
 "num-traits",
 "rusticata-macros",
 "thiserror",
 "time 0.3.34",
]

[[package]]
name = "asn1-rs-derive"
version = "0.4.0"
source = "registry+https://github.com/rust-lang/crates.io-index"
checksum = "726535892e8eae7e70657b4c8ea93d26b8553afb1ce617caee529ef96d7dee6c"
dependencies = [
 "proc-macro2 1.0.79",
 "quote 1.0.35",
 "syn 1.0.109",
 "synstructure",
]

[[package]]
name = "asn1-rs-impl"
version = "0.1.0"
source = "registry+https://github.com/rust-lang/crates.io-index"
checksum = "2777730b2039ac0f95f093556e61b6d26cebed5393ca6f152717777cec3a42ed"
dependencies = [
 "proc-macro2 1.0.79",
 "quote 1.0.35",
 "syn 1.0.109",
]

[[package]]
name = "assert_cmd"
version = "2.0.14"
source = "registry+https://github.com/rust-lang/crates.io-index"
checksum = "ed72493ac66d5804837f480ab3766c72bdfab91a65e565fc54fa9e42db0073a8"
dependencies = [
 "anstyle",
 "bstr",
 "doc-comment",
 "predicates 3.1.0",
 "predicates-core",
 "predicates-tree",
 "wait-timeout",
]

[[package]]
name = "async-channel"
version = "2.2.0"
source = "registry+https://github.com/rust-lang/crates.io-index"
checksum = "f28243a43d821d11341ab73c80bed182dc015c514b951616cf79bd4af39af0c3"
dependencies = [
 "concurrent-queue",
 "event-listener 5.2.0",
 "event-listener-strategy 0.5.0",
 "futures-core",
 "pin-project-lite",
]

[[package]]
name = "async-compression"
version = "0.3.15"
source = "registry+https://github.com/rust-lang/crates.io-index"
checksum = "942c7cd7ae39e91bde4820d74132e9862e62c2f386c3aa90ccf55949f5bad63a"
dependencies = [
 "brotli",
 "flate2",
 "futures-core",
 "memchr",
 "pin-project-lite",
 "tokio",
]

[[package]]
name = "async-lock"
version = "2.8.0"
source = "registry+https://github.com/rust-lang/crates.io-index"
checksum = "287272293e9d8c41773cec55e365490fe034813a2f172f502d6ddcf75b2f582b"
dependencies = [
 "event-listener 2.5.3",
]

[[package]]
name = "async-lock"
version = "3.3.0"
source = "registry+https://github.com/rust-lang/crates.io-index"
checksum = "d034b430882f8381900d3fe6f0aaa3ad94f2cb4ac519b429692a1bc2dda4ae7b"
dependencies = [
 "event-listener 4.0.3",
 "event-listener-strategy 0.4.0",
 "pin-project-lite",
]

[[package]]
name = "async-recursion"
version = "1.1.0"
source = "registry+https://github.com/rust-lang/crates.io-index"
checksum = "30c5ef0ede93efbf733c1a727f3b6b5a1060bbedd5600183e66f6e4be4af0ec5"
dependencies = [
 "proc-macro2 1.0.79",
 "quote 1.0.35",
 "syn 2.0.57",
]

[[package]]
name = "async-stream"
version = "0.3.5"
source = "registry+https://github.com/rust-lang/crates.io-index"
checksum = "cd56dd203fef61ac097dd65721a419ddccb106b2d2b70ba60a6b529f03961a51"
dependencies = [
 "async-stream-impl",
 "futures-core",
 "pin-project-lite",
]

[[package]]
name = "async-stream-impl"
version = "0.3.5"
source = "registry+https://github.com/rust-lang/crates.io-index"
checksum = "16e62a023e7c117e27523144c5d2459f4397fcc3cab0085af8e2224f643a0193"
dependencies = [
 "proc-macro2 1.0.79",
 "quote 1.0.35",
 "syn 2.0.57",
]

[[package]]
name = "async-task"
version = "4.3.0"
source = "git+https://github.com/mystenmark/async-task?rev=4e45b26e11126b191701b9b2ce5e2346b8d7682f#4e45b26e11126b191701b9b2ce5e2346b8d7682f"

[[package]]
name = "async-task"
version = "4.7.0"
source = "registry+https://github.com/rust-lang/crates.io-index"
checksum = "fbb36e985947064623dbd357f727af08ffd077f93d696782f3c56365fa2e2799"

[[package]]
name = "async-trait"
version = "0.1.79"
source = "registry+https://github.com/rust-lang/crates.io-index"
checksum = "a507401cad91ec6a857ed5513a2073c82a9b9048762b885bb98655b306964681"
dependencies = [
 "proc-macro2 1.0.79",
 "quote 1.0.35",
 "syn 2.0.57",
]

[[package]]
name = "async_io_stream"
version = "0.3.3"
source = "registry+https://github.com/rust-lang/crates.io-index"
checksum = "b6d7b9decdf35d8908a7e3ef02f64c5e9b1695e230154c0e8de3969142d9b94c"
dependencies = [
 "futures",
 "pharos",
 "rustc_version 0.4.0",
]

[[package]]
name = "async_once"
version = "0.2.6"
source = "registry+https://github.com/rust-lang/crates.io-index"
checksum = "2ce4f10ea3abcd6617873bae9f91d1c5332b4a778bd9ce34d0cd517474c1de82"

[[package]]
name = "atomic"
version = "0.6.0"
source = "registry+https://github.com/rust-lang/crates.io-index"
checksum = "8d818003e740b63afc82337e3160717f4f63078720a810b7b903e70a5d1d2994"
dependencies = [
 "bytemuck",
]

[[package]]
name = "atomic-waker"
version = "1.1.2"
source = "registry+https://github.com/rust-lang/crates.io-index"
checksum = "1505bd5d3d116872e7271a6d4e16d81d0c8570876c8de68093a09ac269d8aac0"

[[package]]
name = "atomicwrites"
version = "0.3.1"
source = "registry+https://github.com/rust-lang/crates.io-index"
checksum = "eb8f2cd6962fa53c0e2a9d3f97eaa7dbd1e3cbbeeb4745403515b42ae07b3ff6"
dependencies = [
 "tempfile",
 "winapi",
]

[[package]]
name = "atty"
version = "0.2.14"
source = "registry+https://github.com/rust-lang/crates.io-index"
checksum = "d9b39be18770d11421cdb1b9947a45dd3f37e93092cbf377614828a319d5fee8"
dependencies = [
 "hermit-abi 0.1.19",
 "libc",
 "winapi",
]

[[package]]
name = "aurora-engine-modexp"
version = "1.0.0"
source = "registry+https://github.com/rust-lang/crates.io-index"
checksum = "bfacad86e9e138fca0670949eb8ed4ffdf73a55bded8887efe0863cd1a3a6f70"
dependencies = [
 "hex",
 "num 0.4.1",
]

[[package]]
name = "auto_impl"
version = "1.2.0"
source = "registry+https://github.com/rust-lang/crates.io-index"
checksum = "3c87f3f15e7794432337fc718554eaa4dc8f04c9677a950ffe366f20a162ae42"
dependencies = [
 "proc-macro2 1.0.79",
 "quote 1.0.35",
 "syn 2.0.57",
]

[[package]]
name = "auto_ops"
version = "0.3.0"
source = "registry+https://github.com/rust-lang/crates.io-index"
checksum = "7460f7dd8e100147b82a63afca1a20eb6c231ee36b90ba7272e14951cb58af59"

[[package]]
name = "autocfg"
version = "1.2.0"
source = "registry+https://github.com/rust-lang/crates.io-index"
checksum = "f1fdabc7756949593fe60f30ec81974b613357de856987752631dea1e3394c80"

[[package]]
name = "autotools"
version = "0.2.6"
source = "registry+https://github.com/rust-lang/crates.io-index"
checksum = "aef8da1805e028a172334c3b680f93e71126f2327622faef2ec3d893c0a4ad77"
dependencies = [
 "cc",
]

[[package]]
name = "aws-config"
version = "0.55.3"
source = "registry+https://github.com/rust-lang/crates.io-index"
checksum = "bcdcf0d683fe9c23d32cf5b53c9918ea0a500375a9fb20109802552658e576c9"
dependencies = [
 "aws-credential-types",
 "aws-http",
 "aws-sdk-sso",
 "aws-sdk-sts",
 "aws-smithy-async",
 "aws-smithy-client",
 "aws-smithy-http",
 "aws-smithy-http-tower",
 "aws-smithy-json",
 "aws-smithy-types",
 "aws-types",
 "bytes",
 "fastrand 1.9.0",
 "hex",
 "http",
 "hyper",
 "ring 0.16.20",
 "time 0.3.34",
 "tokio",
 "tower",
 "tracing",
 "zeroize",
]

[[package]]
name = "aws-credential-types"
version = "0.55.3"
source = "registry+https://github.com/rust-lang/crates.io-index"
checksum = "1fcdb2f7acbc076ff5ad05e7864bdb191ca70a6fd07668dc3a1a8bcd051de5ae"
dependencies = [
 "aws-smithy-async",
 "aws-smithy-types",
 "fastrand 1.9.0",
 "tokio",
 "tracing",
 "zeroize",
]

[[package]]
name = "aws-endpoint"
version = "0.55.3"
source = "registry+https://github.com/rust-lang/crates.io-index"
checksum = "8cce1c41a6cfaa726adee9ebb9a56fcd2bbfd8be49fd8a04c5e20fd968330b04"
dependencies = [
 "aws-smithy-http",
 "aws-smithy-types",
 "aws-types",
 "http",
 "regex",
 "tracing",
]

[[package]]
name = "aws-http"
version = "0.55.3"
source = "registry+https://github.com/rust-lang/crates.io-index"
checksum = "aadbc44e7a8f3e71c8b374e03ecd972869eb91dd2bc89ed018954a52ba84bc44"
dependencies = [
 "aws-credential-types",
 "aws-smithy-http",
 "aws-smithy-types",
 "aws-types",
 "bytes",
 "http",
 "http-body",
 "lazy_static",
 "percent-encoding",
 "pin-project-lite",
 "tracing",
]

[[package]]
name = "aws-sdk-dynamodb"
version = "0.28.0"
source = "registry+https://github.com/rust-lang/crates.io-index"
checksum = "80e69ffb50ec065a564769aa61158be806b66a7d77301a3459d1c663a421eaab"
dependencies = [
 "aws-credential-types",
 "aws-endpoint",
 "aws-http",
 "aws-sig-auth",
 "aws-smithy-async",
 "aws-smithy-client",
 "aws-smithy-http",
 "aws-smithy-http-tower",
 "aws-smithy-json",
 "aws-smithy-types",
 "aws-types",
 "bytes",
 "fastrand 1.9.0",
 "http",
 "regex",
 "tokio-stream",
 "tower",
 "tracing",
]

[[package]]
name = "aws-sdk-s3"
version = "0.28.0"
source = "registry+https://github.com/rust-lang/crates.io-index"
checksum = "fba197193cbb4bcb6aad8d99796b2291f36fa89562ded5d4501363055b0de89f"
dependencies = [
 "aws-credential-types",
 "aws-endpoint",
 "aws-http",
 "aws-sig-auth",
 "aws-sigv4",
 "aws-smithy-async",
 "aws-smithy-checksums",
 "aws-smithy-client",
 "aws-smithy-eventstream",
 "aws-smithy-http",
 "aws-smithy-http-tower",
 "aws-smithy-json",
 "aws-smithy-types",
 "aws-smithy-xml",
 "aws-types",
 "bytes",
 "http",
 "http-body",
 "once_cell",
 "percent-encoding",
 "regex",
 "tokio-stream",
 "tower",
 "tracing",
 "url",
]

[[package]]
name = "aws-sdk-sso"
version = "0.28.0"
source = "registry+https://github.com/rust-lang/crates.io-index"
checksum = "c8b812340d86d4a766b2ca73f740dfd47a97c2dff0c06c8517a16d88241957e4"
dependencies = [
 "aws-credential-types",
 "aws-endpoint",
 "aws-http",
 "aws-sig-auth",
 "aws-smithy-async",
 "aws-smithy-client",
 "aws-smithy-http",
 "aws-smithy-http-tower",
 "aws-smithy-json",
 "aws-smithy-types",
 "aws-types",
 "bytes",
 "http",
 "regex",
 "tokio-stream",
 "tower",
 "tracing",
]

[[package]]
name = "aws-sdk-sts"
version = "0.28.0"
source = "registry+https://github.com/rust-lang/crates.io-index"
checksum = "265fac131fbfc188e5c3d96652ea90ecc676a934e3174eaaee523c6cec040b3b"
dependencies = [
 "aws-credential-types",
 "aws-endpoint",
 "aws-http",
 "aws-sig-auth",
 "aws-smithy-async",
 "aws-smithy-client",
 "aws-smithy-http",
 "aws-smithy-http-tower",
 "aws-smithy-json",
 "aws-smithy-query",
 "aws-smithy-types",
 "aws-smithy-xml",
 "aws-types",
 "bytes",
 "http",
 "regex",
 "tower",
 "tracing",
]

[[package]]
name = "aws-sig-auth"
version = "0.55.3"
source = "registry+https://github.com/rust-lang/crates.io-index"
checksum = "3b94acb10af0c879ecd5c7bdf51cda6679a0a4f4643ce630905a77673bfa3c61"
dependencies = [
 "aws-credential-types",
 "aws-sigv4",
 "aws-smithy-eventstream",
 "aws-smithy-http",
 "aws-types",
 "http",
 "tracing",
]

[[package]]
name = "aws-sigv4"
version = "0.55.3"
source = "registry+https://github.com/rust-lang/crates.io-index"
checksum = "9d2ce6f507be68e968a33485ced670111d1cbad161ddbbab1e313c03d37d8f4c"
dependencies = [
 "aws-smithy-eventstream",
 "aws-smithy-http",
 "bytes",
 "form_urlencoded",
 "hex",
 "hmac 0.12.1",
 "http",
 "once_cell",
 "percent-encoding",
 "regex",
 "sha2 0.10.8",
 "time 0.3.34",
 "tracing",
]

[[package]]
name = "aws-smithy-async"
version = "0.55.3"
source = "registry+https://github.com/rust-lang/crates.io-index"
checksum = "13bda3996044c202d75b91afeb11a9afae9db9a721c6a7a427410018e286b880"
dependencies = [
 "futures-util",
 "pin-project-lite",
 "tokio",
 "tokio-stream",
]

[[package]]
name = "aws-smithy-checksums"
version = "0.55.3"
source = "registry+https://github.com/rust-lang/crates.io-index"
checksum = "07ed8b96d95402f3f6b8b57eb4e0e45ee365f78b1a924faf20ff6e97abf1eae6"
dependencies = [
 "aws-smithy-http",
 "aws-smithy-types",
 "bytes",
 "crc32c",
 "crc32fast",
 "hex",
 "http",
 "http-body",
 "md-5 0.10.6",
 "pin-project-lite",
 "sha1",
 "sha2 0.10.8",
 "tracing",
]

[[package]]
name = "aws-smithy-client"
version = "0.55.3"
source = "registry+https://github.com/rust-lang/crates.io-index"
checksum = "0a86aa6e21e86c4252ad6a0e3e74da9617295d8d6e374d552be7d3059c41cedd"
dependencies = [
 "aws-smithy-async",
 "aws-smithy-http",
 "aws-smithy-http-tower",
 "aws-smithy-types",
 "bytes",
 "fastrand 1.9.0",
 "http",
 "http-body",
 "hyper",
 "hyper-rustls 0.23.2",
 "lazy_static",
 "pin-project-lite",
 "rustls 0.20.9",
 "tokio",
 "tower",
 "tracing",
]

[[package]]
name = "aws-smithy-eventstream"
version = "0.55.3"
source = "registry+https://github.com/rust-lang/crates.io-index"
checksum = "460c8da5110835e3d9a717c61f5556b20d03c32a1dec57f8fc559b360f733bb8"
dependencies = [
 "aws-smithy-types",
 "bytes",
 "crc32fast",
]

[[package]]
name = "aws-smithy-http"
version = "0.55.3"
source = "registry+https://github.com/rust-lang/crates.io-index"
checksum = "2b3b693869133551f135e1f2c77cb0b8277d9e3e17feaf2213f735857c4f0d28"
dependencies = [
 "aws-smithy-eventstream",
 "aws-smithy-types",
 "bytes",
 "bytes-utils",
 "futures-core",
 "http",
 "http-body",
 "hyper",
 "once_cell",
 "percent-encoding",
 "pin-project-lite",
 "pin-utils",
 "tokio",
 "tokio-util 0.7.10",
 "tracing",
]

[[package]]
name = "aws-smithy-http-tower"
version = "0.55.3"
source = "registry+https://github.com/rust-lang/crates.io-index"
checksum = "3ae4f6c5798a247fac98a867698197d9ac22643596dc3777f0c76b91917616b9"
dependencies = [
 "aws-smithy-http",
 "aws-smithy-types",
 "bytes",
 "http",
 "http-body",
 "pin-project-lite",
 "tower",
 "tracing",
]

[[package]]
name = "aws-smithy-json"
version = "0.55.3"
source = "registry+https://github.com/rust-lang/crates.io-index"
checksum = "23f9f42fbfa96d095194a632fbac19f60077748eba536eb0b9fecc28659807f8"
dependencies = [
 "aws-smithy-types",
]

[[package]]
name = "aws-smithy-query"
version = "0.55.3"
source = "registry+https://github.com/rust-lang/crates.io-index"
checksum = "98819eb0b04020a1c791903533b638534ae6c12e2aceda3e6e6fba015608d51d"
dependencies = [
 "aws-smithy-types",
 "urlencoding",
]

[[package]]
name = "aws-smithy-types"
version = "0.55.3"
source = "registry+https://github.com/rust-lang/crates.io-index"
checksum = "16a3d0bf4f324f4ef9793b86a1701d9700fbcdbd12a846da45eed104c634c6e8"
dependencies = [
 "base64-simd",
 "itoa",
 "num-integer",
 "ryu",
 "time 0.3.34",
]

[[package]]
name = "aws-smithy-xml"
version = "0.55.3"
source = "registry+https://github.com/rust-lang/crates.io-index"
checksum = "b1b9d12875731bd07e767be7baad95700c3137b56730ec9ddeedb52a5e5ca63b"
dependencies = [
 "xmlparser",
]

[[package]]
name = "aws-types"
version = "0.55.3"
source = "registry+https://github.com/rust-lang/crates.io-index"
checksum = "6dd209616cc8d7bfb82f87811a5c655dc97537f592689b18743bddf5dc5c4829"
dependencies = [
 "aws-credential-types",
 "aws-smithy-async",
 "aws-smithy-client",
 "aws-smithy-http",
 "aws-smithy-types",
 "http",
 "rustc_version 0.4.0",
 "tracing",
]

[[package]]
name = "axum"
version = "0.6.20"
source = "registry+https://github.com/rust-lang/crates.io-index"
checksum = "3b829e4e32b91e643de6eafe82b1d90675f5874230191a4ffbc1b336dec4d6bf"
dependencies = [
 "async-trait",
 "axum-core",
 "bitflags 1.3.2",
 "bytes",
 "futures-util",
 "headers",
 "http",
 "http-body",
 "hyper",
 "itoa",
 "matchit 0.7.3",
 "memchr",
 "mime",
 "percent-encoding",
 "pin-project-lite",
 "rustversion",
 "serde",
 "serde_json",
 "serde_path_to_error",
 "serde_urlencoded",
 "sync_wrapper",
 "tokio",
 "tower",
 "tower-layer",
 "tower-service",
]

[[package]]
name = "axum-core"
version = "0.3.4"
source = "registry+https://github.com/rust-lang/crates.io-index"
checksum = "759fa577a247914fd3f7f76d62972792636412fbfd634cd452f6a385a74d2d2c"
dependencies = [
 "async-trait",
 "bytes",
 "futures-util",
 "http",
 "http-body",
 "mime",
 "rustversion",
 "tower-layer",
 "tower-service",
]

[[package]]
name = "axum-extra"
version = "0.4.2"
source = "registry+https://github.com/rust-lang/crates.io-index"
checksum = "f9a320103719de37b7b4da4c8eb629d4573f6bcfd3dfe80d3208806895ccf81d"
dependencies = [
 "axum",
 "bytes",
 "futures-util",
 "http",
 "mime",
 "pin-project-lite",
 "tokio",
 "tower",
 "tower-http",
 "tower-layer",
 "tower-service",
]

[[package]]
name = "axum-server"
version = "0.4.7"
source = "registry+https://github.com/rust-lang/crates.io-index"
checksum = "bace45b270e36e3c27a190c65883de6dfc9f1d18c829907c127464815dc67b24"
dependencies = [
 "arc-swap",
 "bytes",
 "futures-util",
 "http",
 "http-body",
 "hyper",
 "pin-project-lite",
 "rustls 0.20.9",
 "rustls-pemfile",
 "tokio",
 "tokio-rustls 0.23.4",
 "tower-service",
]

[[package]]
name = "backoff"
version = "0.4.0"
source = "registry+https://github.com/rust-lang/crates.io-index"
checksum = "b62ddb9cb1ec0a098ad4bbf9344d0713fa193ae1a80af55febcff2627b6a00c1"
dependencies = [
 "futures-core",
 "getrandom 0.2.12",
 "instant",
 "pin-project-lite",
 "rand 0.8.5",
 "tokio",
]

[[package]]
name = "backtrace"
version = "0.3.71"
source = "registry+https://github.com/rust-lang/crates.io-index"
checksum = "26b05800d2e817c8b3b4b54abd461726265fa9789ae34330622f2db9ee696f9d"
dependencies = [
 "addr2line 0.21.0",
 "cc",
 "cfg-if 1.0.0",
 "libc",
 "miniz_oxide 0.7.2",
 "object 0.32.2",
 "rustc-demangle",
]

[[package]]
name = "base-x"
version = "0.2.11"
source = "registry+https://github.com/rust-lang/crates.io-index"
checksum = "4cbbc9d0964165b47557570cce6c952866c2678457aca742aafc9fb771d30270"

[[package]]
name = "base16ct"
version = "0.1.1"
source = "registry+https://github.com/rust-lang/crates.io-index"
checksum = "349a06037c7bf932dd7e7d1f653678b2038b9ad46a74102f1fc7bd7872678cce"

[[package]]
name = "base16ct"
version = "0.2.0"
source = "registry+https://github.com/rust-lang/crates.io-index"
checksum = "4c7f02d4ea65f2c1853089ffd8d2787bdbc63de2f0d29dedbcf8ccdfa0ccd4cf"

[[package]]
name = "base64"
version = "0.13.1"
source = "registry+https://github.com/rust-lang/crates.io-index"
checksum = "9e1b586273c5702936fe7b7d6896644d8be71e6314cfe09d3167c95f712589e8"

[[package]]
name = "base64"
version = "0.21.7"
source = "registry+https://github.com/rust-lang/crates.io-index"
checksum = "9d297deb1925b89f2ccc13d7635fa0714f12c87adce1c75356b39ca9b7178567"

[[package]]
name = "base64"
version = "0.22.0"
source = "registry+https://github.com/rust-lang/crates.io-index"
checksum = "9475866fec1451be56a3c2400fd081ff546538961565ccb5b7142cbd22bc7a51"

[[package]]
name = "base64-simd"
version = "0.8.0"
source = "registry+https://github.com/rust-lang/crates.io-index"
checksum = "339abbe78e73178762e23bea9dfd08e697eb3f3301cd4be981c0f78ba5859195"
dependencies = [
 "outref",
 "vsimd",
]

[[package]]
name = "base64-url"
version = "2.0.2"
source = "registry+https://github.com/rust-lang/crates.io-index"
checksum = "fb9fb9fb058cc3063b5fc88d9a21eefa2735871498a04e1650da76ed511c8569"
dependencies = [
 "base64 0.21.7",
]

[[package]]
name = "base64ct"
version = "1.6.0"
source = "registry+https://github.com/rust-lang/crates.io-index"
checksum = "8c3c1a368f70d6cf7302d78f8f7093da241fb8e8807c05cc9e51a125895a6d5b"

[[package]]
name = "bcs"
version = "0.1.6"
source = "registry+https://github.com/rust-lang/crates.io-index"
checksum = "85b6598a2f5d564fb7855dc6b06fd1c38cff5a72bd8b863a4d021938497b440a"
dependencies = [
 "serde",
 "thiserror",
]

[[package]]
name = "bech32"
version = "0.9.1"
source = "registry+https://github.com/rust-lang/crates.io-index"
checksum = "d86b93f97252c47b41663388e6d155714a9d0c398b99f1005cbc5f978b29f445"

[[package]]
name = "beef"
version = "0.5.2"
source = "registry+https://github.com/rust-lang/crates.io-index"
checksum = "3a8241f3ebb85c056b509d4327ad0358fbbba6ffb340bf388f26350aeda225b1"
dependencies = [
 "serde",
]

[[package]]
name = "better_any"
version = "0.1.1"
source = "registry+https://github.com/rust-lang/crates.io-index"
checksum = "b359aebd937c17c725e19efcb661200883f04c49c53e7132224dac26da39d4a0"
dependencies = [
 "better_typeid_derive",
]

[[package]]
name = "better_typeid_derive"
version = "0.1.1"
source = "registry+https://github.com/rust-lang/crates.io-index"
checksum = "3deeecb812ca5300b7d3f66f730cc2ebd3511c3d36c691dd79c165d5b19a26e3"
dependencies = [
 "proc-macro2 1.0.79",
 "quote 1.0.35",
 "syn 1.0.109",
]

[[package]]
name = "bimap"
version = "0.6.3"
source = "registry+https://github.com/rust-lang/crates.io-index"
checksum = "230c5f1ca6a325a32553f8640d31ac9b49f2411e901e427570154868b46da4f7"

[[package]]
name = "bincode"
version = "1.3.3"
source = "registry+https://github.com/rust-lang/crates.io-index"
checksum = "b1f45e9417d87227c7a56d22e471c6206462cba514c7590c09aff4cf6d1ddcad"
dependencies = [
 "serde",
]

[[package]]
name = "bindgen"
version = "0.65.1"
source = "registry+https://github.com/rust-lang/crates.io-index"
checksum = "cfdf7b466f9a4903edc73f95d6d2bcd5baf8ae620638762244d3f60143643cc5"
dependencies = [
 "bitflags 1.3.2",
 "cexpr",
 "clang-sys",
 "lazy_static",
 "lazycell",
 "peeking_take_while",
 "prettyplease 0.2.17",
 "proc-macro2 1.0.79",
 "quote 1.0.35",
 "regex",
 "rustc-hash",
 "shlex",
 "syn 2.0.57",
]

[[package]]
name = "bindgen"
version = "0.66.1"
source = "registry+https://github.com/rust-lang/crates.io-index"
checksum = "f2b84e06fc203107bfbad243f4aba2af864eb7db3b1cf46ea0a023b0b433d2a7"
dependencies = [
 "bitflags 2.5.0",
 "cexpr",
 "clang-sys",
 "lazy_static",
 "lazycell",
 "peeking_take_while",
 "proc-macro2 1.0.79",
 "quote 1.0.35",
 "regex",
 "rustc-hash",
 "shlex",
 "syn 2.0.57",
]

[[package]]
name = "bip32"
version = "0.4.0"
source = "registry+https://github.com/rust-lang/crates.io-index"
checksum = "b30ed1d6f8437a487a266c8293aeb95b61a23261273e3e02912cdb8b68bf798b"
dependencies = [
 "bs58 0.4.0",
 "hmac 0.12.1",
 "k256 0.11.6",
 "once_cell",
 "pbkdf2 0.11.0",
 "rand_core 0.6.4",
 "ripemd",
 "sha2 0.10.8",
 "subtle",
 "zeroize",
]

[[package]]
name = "bit-set"
version = "0.5.3"
source = "registry+https://github.com/rust-lang/crates.io-index"
checksum = "0700ddab506f33b20a03b13996eccd309a48e5ff77d0d95926aa0210fb4e95f1"
dependencies = [
 "bit-vec",
]

[[package]]
name = "bit-vec"
version = "0.6.3"
source = "registry+https://github.com/rust-lang/crates.io-index"
checksum = "349f9b6a179ed607305526ca489b34ad0a41aed5f7980fa90eb03160b69598fb"

[[package]]
name = "bitcoin-private"
version = "0.1.0"
source = "registry+https://github.com/rust-lang/crates.io-index"
checksum = "73290177011694f38ec25e165d0387ab7ea749a4b81cd4c80dae5988229f7a57"

[[package]]
name = "bitcoin_hashes"
version = "0.12.0"
source = "registry+https://github.com/rust-lang/crates.io-index"
checksum = "5d7066118b13d4b20b23645932dfb3a81ce7e29f95726c2036fa33cd7b092501"
dependencies = [
 "bitcoin-private",
]

[[package]]
name = "bitflags"
version = "1.3.2"
source = "registry+https://github.com/rust-lang/crates.io-index"
checksum = "bef38d45163c2f1dde094a7dfd33ccf595c92905c8f8f4fdc18d06fb1037718a"

[[package]]
name = "bitflags"
version = "2.5.0"
source = "registry+https://github.com/rust-lang/crates.io-index"
checksum = "cf4b9d6a944f767f8e5e0db018570623c85f3d925ac718db4e06d0187adb21c1"
dependencies = [
 "arbitrary",
 "serde",
]

[[package]]
name = "bitmaps"
version = "2.1.0"
source = "registry+https://github.com/rust-lang/crates.io-index"
checksum = "031043d04099746d8db04daf1fa424b2bc8bd69d92b25962dcde24da39ab64a2"
dependencies = [
 "typenum",
]

[[package]]
name = "bitvec"
version = "0.20.4"
source = "registry+https://github.com/rust-lang/crates.io-index"
checksum = "7774144344a4faa177370406a7ff5f1da24303817368584c6206c8303eb07848"
dependencies = [
 "funty 1.1.0",
 "radium 0.6.2",
 "tap",
 "wyz 0.2.0",
]

[[package]]
name = "bitvec"
version = "1.0.1"
source = "registry+https://github.com/rust-lang/crates.io-index"
checksum = "1bc2832c24239b0141d5674bb9174f9d68a8b5b3f2753311927c172ca46f7e9c"
dependencies = [
 "funty 2.0.0",
 "radium 0.7.0",
 "serde",
 "tap",
 "wyz 0.5.1",
]

[[package]]
name = "blake2"
version = "0.10.6"
source = "registry+https://github.com/rust-lang/crates.io-index"
checksum = "46502ad458c9a52b69d4d4d32775c788b7a1b85e8bc9d482d92250fc0e3f8efe"
dependencies = [
 "digest 0.10.7",
]

[[package]]
name = "blake3"
version = "1.5.1"
source = "registry+https://github.com/rust-lang/crates.io-index"
checksum = "30cca6d3674597c30ddf2c587bf8d9d65c9a84d2326d941cc79c9842dfe0ef52"
dependencies = [
 "arrayref",
 "arrayvec 0.7.4",
 "cc",
 "cfg-if 1.0.0",
 "constant_time_eq 0.3.0",
]

[[package]]
name = "block-buffer"
version = "0.9.0"
source = "registry+https://github.com/rust-lang/crates.io-index"
checksum = "4152116fd6e9dadb291ae18fc1ec3575ed6d84c29642d97890f4b4a3417297e4"
dependencies = [
 "block-padding 0.2.1",
 "generic-array 0.14.7",
]

[[package]]
name = "block-buffer"
version = "0.10.4"
source = "registry+https://github.com/rust-lang/crates.io-index"
checksum = "3078c7629b62d3f0439517fa394996acacc5cbc91c5a20d8c658e77abd503a71"
dependencies = [
 "generic-array 0.14.7",
]

[[package]]
name = "block-padding"
version = "0.2.1"
source = "registry+https://github.com/rust-lang/crates.io-index"
checksum = "8d696c370c750c948ada61c69a0ee2cbbb9c50b1019ddb86d9317157a99c2cae"

[[package]]
name = "block-padding"
version = "0.3.3"
source = "registry+https://github.com/rust-lang/crates.io-index"
checksum = "a8894febbff9f758034a5b8e12d87918f56dfc64a8e1fe757d65e29041538d93"
dependencies = [
 "generic-array 0.14.7",
]

[[package]]
name = "blocking"
version = "1.5.1"
source = "registry+https://github.com/rust-lang/crates.io-index"
checksum = "6a37913e8dc4ddcc604f0c6d3bf2887c995153af3611de9e23c352b44c1b9118"
dependencies = [
 "async-channel",
 "async-lock 3.3.0",
 "async-task 4.7.0",
 "fastrand 2.0.2",
 "futures-io",
 "futures-lite 2.3.0",
 "piper",
 "tracing",
]

[[package]]
name = "blst"
version = "0.3.11"
source = "registry+https://github.com/rust-lang/crates.io-index"
checksum = "c94087b935a822949d3291a9989ad2b2051ea141eda0fd4e478a75f6aa3e604b"
dependencies = [
 "cc",
 "glob",
 "threadpool",
 "zeroize",
]

[[package]]
name = "brotli"
version = "3.5.0"
source = "registry+https://github.com/rust-lang/crates.io-index"
checksum = "d640d25bc63c50fb1f0b545ffd80207d2e10a4c965530809b40ba3386825c391"
dependencies = [
 "alloc-no-stdlib",
 "alloc-stdlib",
 "brotli-decompressor",
]

[[package]]
name = "brotli-decompressor"
version = "2.5.1"
source = "registry+https://github.com/rust-lang/crates.io-index"
checksum = "4e2e4afe60d7dd600fdd3de8d0f08c2b7ec039712e3b6137ff98b7004e82de4f"
dependencies = [
 "alloc-no-stdlib",
 "alloc-stdlib",
]

[[package]]
name = "bs58"
version = "0.4.0"
source = "registry+https://github.com/rust-lang/crates.io-index"
checksum = "771fe0050b883fcc3ea2359b1a96bcfbc090b7116eae7c3c512c7a083fdf23d3"
dependencies = [
 "sha2 0.9.9",
]

[[package]]
name = "bs58"
version = "0.5.1"
source = "registry+https://github.com/rust-lang/crates.io-index"
checksum = "bf88ba1141d185c399bee5288d850d63b8369520c1eafc32a0430b5b6c287bf4"
dependencies = [
 "sha2 0.10.8",
 "tinyvec",
]

[[package]]
name = "bstr"
version = "1.9.1"
source = "registry+https://github.com/rust-lang/crates.io-index"
checksum = "05efc5cfd9110c8416e471df0e96702d58690178e206e61b7173706673c93706"
dependencies = [
 "memchr",
 "regex-automata 0.4.6",
 "serde",
]

[[package]]
name = "build_const"
version = "0.2.2"
source = "registry+https://github.com/rust-lang/crates.io-index"
checksum = "b4ae4235e6dac0694637c763029ecea1a2ec9e4e06ec2729bd21ba4d9c863eb7"

[[package]]
name = "build_id"
version = "0.2.1"
source = "registry+https://github.com/rust-lang/crates.io-index"
checksum = "c6deb6795d8b4d2269c3fcf87a87bff9f4cd45a99e259806603ee8007077daf3"
dependencies = [
 "byteorder",
 "once_cell",
 "palaver",
 "twox-hash",
 "uuid 0.8.2",
]

[[package]]
name = "bulletproofs"
version = "4.0.0"
source = "registry+https://github.com/rust-lang/crates.io-index"
checksum = "40e698f1df446cc6246afd823afbe2d121134d089c9102c1dd26d1264991ba32"
dependencies = [
 "byteorder",
 "clear_on_drop",
 "curve25519-dalek-ng",
 "digest 0.9.0",
 "merlin",
 "rand 0.8.5",
 "rand_core 0.6.4",
 "serde",
 "serde_derive",
 "sha3 0.9.1",
 "subtle-ng",
 "thiserror",
]

[[package]]
name = "bumpalo"
version = "3.15.4"
source = "registry+https://github.com/rust-lang/crates.io-index"
checksum = "7ff69b9dd49fd426c69a0db9fc04dd934cdb6645ff000864d98f7e2af8830eaa"

[[package]]
name = "byte-slice-cast"
version = "1.2.2"
source = "registry+https://github.com/rust-lang/crates.io-index"
checksum = "c3ac9f8b63eca6fd385229b3675f6cc0dc5c8a5c8a54a59d4f52ffd670d87b0c"

[[package]]
name = "bytecode-interpreter-crypto"
version = "0.1.0"
source = "git+https://github.com/fuzzland/ityfuzz-sui-fork.git#a88e9595de3c04a540c059d5f4b6d148419c508c"
dependencies = [
 "anyhow",
 "curve25519-dalek-fiat",
 "ed25519-dalek-fiat",
 "sha2 0.9.9",
 "sha3 0.9.1",
]

[[package]]
name = "bytecount"
version = "0.6.7"
source = "registry+https://github.com/rust-lang/crates.io-index"
checksum = "e1e5f035d16fc623ae5f74981db80a439803888314e3a555fd6f04acd51a3205"

[[package]]
name = "bytemuck"
version = "1.15.0"
source = "registry+https://github.com/rust-lang/crates.io-index"
checksum = "5d6d68c57235a3a081186990eca2867354726650f42f7516ca50c28d6281fd15"

[[package]]
name = "byteorder"
version = "1.5.0"
source = "registry+https://github.com/rust-lang/crates.io-index"
checksum = "1fd0f2584146f6f2ef48085050886acf353beff7305ebd1ae69500e27c67f64b"

[[package]]
name = "bytes"
version = "1.6.0"
source = "registry+https://github.com/rust-lang/crates.io-index"
checksum = "514de17de45fdb8dc022b1a7975556c53c86f9f0aa5f534b98977b171857c2c9"
dependencies = [
 "serde",
]

[[package]]
name = "bytes-utils"
version = "0.1.4"
source = "registry+https://github.com/rust-lang/crates.io-index"
checksum = "7dafe3a8757b027e2be6e4e5601ed563c55989fcf1546e933c66c8eb3a058d35"
dependencies = [
 "bytes",
 "either",
]

[[package]]
name = "bzip2"
version = "0.4.4"
source = "registry+https://github.com/rust-lang/crates.io-index"
checksum = "bdb116a6ef3f6c3698828873ad02c3014b3c85cadb88496095628e3ef1e347f8"
dependencies = [
 "bzip2-sys",
 "libc",
]

[[package]]
name = "bzip2-sys"
version = "0.1.11+1.0.8"
source = "registry+https://github.com/rust-lang/crates.io-index"
checksum = "736a955f3fa7875102d57c82b8cac37ec45224a07fd32d58f9f7a186b6cd4cdc"
dependencies = [
 "cc",
 "libc",
 "pkg-config",
]

[[package]]
name = "c-kzg"
version = "1.0.0"
source = "registry+https://github.com/rust-lang/crates.io-index"
checksum = "3130f3d8717cc02e668a896af24984d5d5d4e8bf12e278e982e0f1bd88a0f9af"
dependencies = [
 "blst",
 "cc",
 "glob",
 "hex",
 "libc",
 "serde",
]

[[package]]
name = "c2rust-bitfields"
version = "0.18.0"
source = "registry+https://github.com/rust-lang/crates.io-index"
checksum = "b43c3f07ab0ef604fa6f595aa46ec2f8a22172c975e186f6f5bf9829a3b72c41"
dependencies = [
 "c2rust-bitfields-derive",
]

[[package]]
name = "c2rust-bitfields-derive"
version = "0.18.0"
source = "registry+https://github.com/rust-lang/crates.io-index"
checksum = "d3cbc102e2597c9744c8bd8c15915d554300601c91a079430d309816b0912545"
dependencies = [
 "proc-macro2 1.0.79",
 "quote 1.0.35",
 "syn 1.0.109",
]

[[package]]
name = "cached"
version = "0.43.0"
source = "registry+https://github.com/rust-lang/crates.io-index"
checksum = "bc2fafddf188d13788e7099295a59b99e99b2148ab2195cae454e754cc099925"
dependencies = [
 "async-trait",
 "async_once",
 "cached_proc_macro",
 "cached_proc_macro_types",
 "futures",
 "hashbrown 0.13.2",
 "instant",
 "lazy_static",
 "once_cell",
 "thiserror",
 "tokio",
]

[[package]]
name = "cached_proc_macro"
version = "0.16.0"
source = "registry+https://github.com/rust-lang/crates.io-index"
checksum = "e10ca87c81aaa3a949dbbe2b5e6c2c45dbc94ba4897e45ea31ff9ec5087be3dc"
dependencies = [
 "cached_proc_macro_types",
 "darling 0.14.4",
 "proc-macro2 1.0.79",
 "quote 1.0.35",
 "syn 1.0.109",
]

[[package]]
name = "cached_proc_macro_types"
version = "0.1.1"
source = "registry+https://github.com/rust-lang/crates.io-index"
checksum = "ade8366b8bd5ba243f0a58f036cc0ca8a2f069cff1a2351ef1cac6b083e16fc0"

[[package]]
name = "camino"
version = "1.1.6"
source = "registry+https://github.com/rust-lang/crates.io-index"
checksum = "c59e92b5a388f549b863a7bea62612c09f24c8393560709a54558a9abdfb3b9c"
dependencies = [
 "serde",
]

[[package]]
name = "cargo-platform"
version = "0.1.8"
source = "registry+https://github.com/rust-lang/crates.io-index"
checksum = "24b1f0365a6c6bb4020cd05806fd0d33c44d38046b8bd7f0e40814b9763cabfc"
dependencies = [
 "serde",
]

[[package]]
name = "cargo_metadata"
version = "0.15.4"
source = "registry+https://github.com/rust-lang/crates.io-index"
checksum = "eee4243f1f26fc7a42710e7439c149e2b10b05472f88090acce52632f231a73a"
dependencies = [
 "camino",
 "cargo-platform",
 "semver 1.0.22",
 "serde",
 "serde_json",
 "thiserror",
]

[[package]]
name = "cargo_metadata"
version = "0.18.1"
source = "registry+https://github.com/rust-lang/crates.io-index"
checksum = "2d886547e41f740c616ae73108f6eb70afe6d940c7bc697cb30f13daec073037"
dependencies = [
 "camino",
 "cargo-platform",
 "semver 1.0.22",
 "serde",
 "serde_json",
 "thiserror",
]

[[package]]
name = "cassowary"
version = "0.3.0"
source = "registry+https://github.com/rust-lang/crates.io-index"
checksum = "df8670b8c7b9dae1793364eafadf7239c40d669904660c5960d74cfd80b46a53"

[[package]]
name = "cast"
version = "0.3.0"
source = "registry+https://github.com/rust-lang/crates.io-index"
checksum = "37b2a672a2cb129a2e41c10b1224bb368f9f37a2b16b612598138befd7b37eb5"

[[package]]
name = "cbc"
version = "0.1.2"
source = "registry+https://github.com/rust-lang/crates.io-index"
checksum = "26b52a9543ae338f279b96b0b9fed9c8093744685043739079ce85cd58f289a6"
dependencies = [
 "cipher",
]

[[package]]
name = "cc"
version = "1.0.90"
source = "registry+https://github.com/rust-lang/crates.io-index"
checksum = "8cd6604a82acf3039f1144f54b8eb34e91ffba622051189e71b781822d5ee1f5"
dependencies = [
 "jobserver",
 "libc",
]

[[package]]
name = "cexpr"
version = "0.6.0"
source = "registry+https://github.com/rust-lang/crates.io-index"
checksum = "6fac387a98bb7c37292057cffc56d62ecb629900026402633ae9160df93a8766"
dependencies = [
 "nom 7.1.3",
]

[[package]]
name = "cfg-expr"
version = "0.13.0"
source = "registry+https://github.com/rust-lang/crates.io-index"
checksum = "a327683d7499ecc47369531a679fe38acdd300e09bf8c852d08b1e10558622bd"
dependencies = [
 "smallvec",
 "target-lexicon",
]

[[package]]
name = "cfg-expr"
version = "0.15.7"
source = "registry+https://github.com/rust-lang/crates.io-index"
checksum = "fa50868b64a9a6fda9d593ce778849ea8715cd2a3d2cc17ffdb4a2f2f2f1961d"
dependencies = [
 "smallvec",
 "target-lexicon",
]

[[package]]
name = "cfg-if"
version = "0.1.10"
source = "registry+https://github.com/rust-lang/crates.io-index"
checksum = "4785bdd1c96b2a846b2bd7cc02e86b6b3dbf14e7e53446c4f54c92a361040822"

[[package]]
name = "cfg-if"
version = "1.0.0"
source = "registry+https://github.com/rust-lang/crates.io-index"
checksum = "baf1de4339761588bc0619e3cbc0120ee582ebb74b53b4efbf79117bd2da40fd"

[[package]]
name = "chrono"
version = "0.4.37"
source = "registry+https://github.com/rust-lang/crates.io-index"
checksum = "8a0d04d43504c61aa6c7531f1871dd0d418d91130162063b789da00fd7057a5e"
dependencies = [
 "android-tzdata",
 "iana-time-zone",
 "js-sys",
 "num-traits",
 "serde",
 "wasm-bindgen",
 "windows-targets 0.52.4",
]

[[package]]
name = "chrono-tz"
version = "0.6.3"
source = "registry+https://github.com/rust-lang/crates.io-index"
checksum = "29c39203181991a7dd4343b8005bd804e7a9a37afb8ac070e43771e8c820bbde"
dependencies = [
 "chrono",
 "chrono-tz-build 0.0.3",
 "phf",
]

[[package]]
name = "chrono-tz"
version = "0.8.6"
source = "registry+https://github.com/rust-lang/crates.io-index"
checksum = "d59ae0466b83e838b81a54256c39d5d7c20b9d7daa10510a242d9b75abd5936e"
dependencies = [
 "chrono",
 "chrono-tz-build 0.2.1",
 "phf",
]

[[package]]
name = "chrono-tz-build"
version = "0.0.3"
source = "registry+https://github.com/rust-lang/crates.io-index"
checksum = "6f509c3a87b33437b05e2458750a0700e5bdd6956176773e6c7d6dd15a283a0c"
dependencies = [
 "parse-zoneinfo",
 "phf",
 "phf_codegen",
]

[[package]]
name = "chrono-tz-build"
version = "0.2.1"
source = "registry+https://github.com/rust-lang/crates.io-index"
checksum = "433e39f13c9a060046954e0592a8d0a4bcb1040125cbf91cb8ee58964cfb350f"
dependencies = [
 "parse-zoneinfo",
 "phf",
 "phf_codegen",
]

[[package]]
name = "ciborium"
version = "0.2.2"
source = "registry+https://github.com/rust-lang/crates.io-index"
checksum = "42e69ffd6f0917f5c029256a24d0161db17cea3997d185db0d35926308770f0e"
dependencies = [
 "ciborium-io",
 "ciborium-ll",
 "serde",
]

[[package]]
name = "ciborium-io"
version = "0.2.2"
source = "registry+https://github.com/rust-lang/crates.io-index"
checksum = "05afea1e0a06c9be33d539b876f1ce3692f4afea2cb41f740e7743225ed1c757"

[[package]]
name = "ciborium-ll"
version = "0.2.2"
source = "registry+https://github.com/rust-lang/crates.io-index"
checksum = "57663b653d948a338bfb3eeba9bb2fd5fcfaecb9e199e87e1eda4d9e8b240fd9"
dependencies = [
 "ciborium-io",
 "half 2.4.0",
]

[[package]]
name = "cipher"
version = "0.4.4"
source = "registry+https://github.com/rust-lang/crates.io-index"
checksum = "773f3b9af64447d2ce9850330c473515014aa235e6a783b02db81ff39e4a3dad"
dependencies = [
 "crypto-common",
 "inout",
]

[[package]]
name = "clang-sys"
version = "1.7.0"
source = "registry+https://github.com/rust-lang/crates.io-index"
checksum = "67523a3b4be3ce1989d607a828d036249522dd9c1c8de7f4dd2dae43a37369d1"
dependencies = [
 "glob",
 "libc",
 "libloading 0.8.3",
]

[[package]]
name = "clap"
version = "2.34.0"
source = "registry+https://github.com/rust-lang/crates.io-index"
checksum = "a0610544180c38b88101fecf2dd634b174a62eef6946f84dfc6a7127512b381c"
dependencies = [
 "ansi_term",
 "atty",
 "bitflags 1.3.2",
 "strsim 0.8.0",
 "textwrap 0.11.0",
 "unicode-width",
 "vec_map",
]

[[package]]
name = "clap"
version = "3.2.25"
source = "registry+https://github.com/rust-lang/crates.io-index"
checksum = "4ea181bf566f71cb9a5d17a59e1871af638180a18fb0035c92ae62b705207123"
dependencies = [
 "atty",
 "bitflags 1.3.2",
 "clap_derive 3.2.25",
 "clap_lex 0.2.4",
 "indexmap 1.9.3",
 "once_cell",
 "strsim 0.10.0",
 "termcolor",
 "textwrap 0.16.1",
]

[[package]]
name = "clap"
version = "4.5.4"
source = "registry+https://github.com/rust-lang/crates.io-index"
checksum = "90bc066a67923782aa8515dbaea16946c5bcc5addbd668bb80af688e53e548a0"
dependencies = [
 "clap_builder",
 "clap_derive 4.5.4",
]

[[package]]
name = "clap_builder"
version = "4.5.2"
source = "registry+https://github.com/rust-lang/crates.io-index"
checksum = "ae129e2e766ae0ec03484e609954119f123cc1fe650337e155d03b022f24f7b4"
dependencies = [
 "anstream 0.6.13",
 "anstyle",
 "clap_lex 0.7.0",
 "strsim 0.11.0",
 "terminal_size",
 "unicase",
 "unicode-width",
]

[[package]]
name = "clap_derive"
version = "3.2.25"
source = "registry+https://github.com/rust-lang/crates.io-index"
checksum = "ae6371b8bdc8b7d3959e9cf7b22d4435ef3e79e138688421ec654acf8c81b008"
dependencies = [
 "heck 0.4.1",
 "proc-macro-error",
 "proc-macro2 1.0.79",
 "quote 1.0.35",
 "syn 1.0.109",
]

[[package]]
name = "clap_derive"
version = "4.5.4"
source = "registry+https://github.com/rust-lang/crates.io-index"
checksum = "528131438037fd55894f62d6e9f068b8f45ac57ffa77517819645d10aed04f64"
dependencies = [
 "heck 0.5.0",
 "proc-macro2 1.0.79",
 "quote 1.0.35",
 "syn 2.0.57",
]

[[package]]
name = "clap_lex"
version = "0.2.4"
source = "registry+https://github.com/rust-lang/crates.io-index"
checksum = "2850f2f5a82cbf437dd5af4d49848fbdfc27c157c3d010345776f952765261c5"
dependencies = [
 "os_str_bytes",
]

[[package]]
name = "clap_lex"
version = "0.5.1"
source = "registry+https://github.com/rust-lang/crates.io-index"
checksum = "cd7cc57abe963c6d3b9d8be5b06ba7c8957a930305ca90304f24ef040aa6f961"

[[package]]
name = "clap_lex"
version = "0.7.0"
source = "registry+https://github.com/rust-lang/crates.io-index"
checksum = "98cc8fbded0c607b7ba9dd60cd98df59af97e84d24e49c8557331cfc26d301ce"

[[package]]
name = "clear_on_drop"
version = "0.2.5"
source = "registry+https://github.com/rust-lang/crates.io-index"
checksum = "38508a63f4979f0048febc9966fadbd48e5dab31fd0ec6a3f151bbf4a74f7423"
dependencies = [
 "cc",
]

[[package]]
name = "clipboard-win"
version = "4.5.0"
source = "registry+https://github.com/rust-lang/crates.io-index"
checksum = "7191c27c2357d9b7ef96baac1773290d4ca63b24205b82a3fd8a0637afcf0362"
dependencies = [
 "error-code",
 "str-buf",
 "winapi",
]

[[package]]
name = "cmake"
version = "0.1.50"
source = "registry+https://github.com/rust-lang/crates.io-index"
checksum = "a31c789563b815f77f4250caee12365734369f942439b7defd71e18a48197130"
dependencies = [
 "cc",
]

[[package]]
name = "cobs"
version = "0.2.3"
source = "registry+https://github.com/rust-lang/crates.io-index"
checksum = "67ba02a97a2bd10f4b59b25c7973101c79642302776489e030cd13cdab09ed15"

[[package]]
name = "codespan"
version = "0.11.1"
source = "registry+https://github.com/rust-lang/crates.io-index"
checksum = "3362992a0d9f1dd7c3d0e89e0ab2bb540b7a95fea8cd798090e758fda2899b5e"
dependencies = [
 "codespan-reporting",
 "serde",
]

[[package]]
name = "codespan-reporting"
version = "0.11.1"
source = "registry+https://github.com/rust-lang/crates.io-index"
checksum = "3538270d33cc669650c4b093848450d380def10c331d38c768e34cac80576e6e"
dependencies = [
 "serde",
 "termcolor",
 "unicode-width",
]

[[package]]
name = "coins-bip32"
version = "0.8.7"
source = "registry+https://github.com/rust-lang/crates.io-index"
checksum = "3b6be4a5df2098cd811f3194f64ddb96c267606bffd9689ac7b0160097b01ad3"
dependencies = [
 "bs58 0.5.1",
 "coins-core",
 "digest 0.10.7",
 "hmac 0.12.1",
 "k256 0.13.3",
 "serde",
 "sha2 0.10.8",
 "thiserror",
]

[[package]]
name = "coins-bip39"
version = "0.8.7"
source = "registry+https://github.com/rust-lang/crates.io-index"
checksum = "3db8fba409ce3dc04f7d804074039eb68b960b0829161f8e06c95fea3f122528"
dependencies = [
 "bitvec 1.0.1",
 "coins-bip32",
 "hmac 0.12.1",
 "once_cell",
 "pbkdf2 0.12.2",
 "rand 0.8.5",
 "sha2 0.10.8",
 "thiserror",
]

[[package]]
name = "coins-core"
version = "0.8.7"
source = "registry+https://github.com/rust-lang/crates.io-index"
checksum = "5286a0843c21f8367f7be734f89df9b822e0321d8bcce8d6e735aadff7d74979"
dependencies = [
 "base64 0.21.7",
 "bech32",
 "bs58 0.5.1",
 "digest 0.10.7",
 "generic-array 0.14.7",
 "hex",
 "ripemd",
 "serde",
 "serde_derive",
 "sha2 0.10.8",
 "sha3 0.10.8",
 "thiserror",
]

[[package]]
name = "coins-ledger"
version = "0.10.1"
source = "registry+https://github.com/rust-lang/crates.io-index"
checksum = "9e076e6e5d9708f0b90afe2dbe5a8ba406b5c794347661e6e44618388c7e3a31"
dependencies = [
 "async-trait",
 "byteorder",
 "cfg-if 1.0.0",
 "getrandom 0.2.12",
 "hex",
 "hidapi-rusb",
 "js-sys",
 "log",
 "nix 0.26.4",
 "once_cell",
 "thiserror",
 "tokio",
 "tracing",
 "wasm-bindgen",
 "wasm-bindgen-futures",
]

[[package]]
name = "collectable"
version = "0.0.2"
source = "registry+https://github.com/rust-lang/crates.io-index"
checksum = "08abddbaad209601e53c7dd4308d8c04c06f17bb7df006434e586a22b83be45a"

[[package]]
name = "colorchoice"
version = "1.0.0"
source = "registry+https://github.com/rust-lang/crates.io-index"
checksum = "acbf1af155f9b9ef647e42cdc158db4b64a1b61f743629225fde6f3e0be2a7c7"

[[package]]
name = "colored"
version = "2.1.0"
source = "registry+https://github.com/rust-lang/crates.io-index"
checksum = "cbf2150cce219b664a8a70df7a1f933836724b503f8a413af9365b4dcc4d90b8"
dependencies = [
 "lazy_static",
 "windows-sys 0.48.0",
]

[[package]]
name = "colored-diff"
version = "0.2.3"
source = "registry+https://github.com/rust-lang/crates.io-index"
checksum = "410208eb08c3f3ad44b95b51c4fc0d5993cbcc9dd39cfadb4214b9115a97dcb5"
dependencies = [
 "ansi_term",
 "dissimilar",
 "itertools 0.10.5",
]

[[package]]
name = "combine"
version = "4.6.6"
source = "registry+https://github.com/rust-lang/crates.io-index"
checksum = "35ed6e9d84f0b51a7f52daf1c7d71dd136fd7a3f41a8462b8cdb8c78d920fad4"
dependencies = [
 "bytes",
 "memchr",
]

[[package]]
name = "comfy-table"
version = "6.2.0"
source = "registry+https://github.com/rust-lang/crates.io-index"
checksum = "7e959d788268e3bf9d35ace83e81b124190378e4c91c9067524675e33394b8ba"
dependencies = [
 "crossterm 0.26.1",
 "strum 0.24.1",
 "strum_macros 0.24.3",
 "unicode-width",
]

[[package]]
name = "comfy-table"
version = "7.1.0"
source = "registry+https://github.com/rust-lang/crates.io-index"
checksum = "7c64043d6c7b7a4c58e39e7efccfdea7b93d885a795d0c054a69dbbf4dd52686"
dependencies = [
 "crossterm 0.27.0",
 "strum 0.25.0",
 "strum_macros 0.25.3",
 "unicode-width",
]

[[package]]
name = "concurrent-queue"
version = "2.4.0"
source = "registry+https://github.com/rust-lang/crates.io-index"
checksum = "d16048cd947b08fa32c24458a22f5dc5e835264f689f4f5653210c69fd107363"
dependencies = [
 "crossbeam-utils",
]

[[package]]
name = "console"
version = "0.15.8"
source = "registry+https://github.com/rust-lang/crates.io-index"
checksum = "0e1f83fc076bd6dd27517eacdf25fef6c4dfe5f1d7448bafaaf3a26f13b5e4eb"
dependencies = [
 "encode_unicode 0.3.6",
 "lazy_static",
 "libc",
 "unicode-width",
 "windows-sys 0.52.0",
]

[[package]]
name = "console-api"
version = "0.4.0"
source = "registry+https://github.com/rust-lang/crates.io-index"
checksum = "e57ff02e8ad8e06ab9731d5dc72dc23bef9200778eae1a89d555d8c42e5d4a86"
dependencies = [
 "prost",
 "prost-types",
 "tonic 0.8.3",
 "tracing-core",
]

[[package]]
name = "console-api"
version = "0.5.0"
source = "registry+https://github.com/rust-lang/crates.io-index"
checksum = "c2895653b4d9f1538a83970077cb01dfc77a4810524e51a110944688e916b18e"
dependencies = [
 "prost",
 "prost-types",
 "tonic 0.9.2",
 "tracing-core",
]

[[package]]
name = "console-subscriber"
version = "0.1.10"
source = "registry+https://github.com/rust-lang/crates.io-index"
checksum = "d4cf42660ac07fcebed809cfe561dd8730bcd35b075215e6479c516bcd0d11cb"
dependencies = [
 "console-api 0.5.0",
 "crossbeam-channel",
 "crossbeam-utils",
 "futures",
 "hdrhistogram",
 "humantime",
 "prost-types",
 "serde",
 "serde_json",
 "thread_local",
 "tokio",
 "tokio-stream",
 "tonic 0.9.2",
 "tracing",
 "tracing-core",
 "tracing-subscriber 0.3.18",
]

[[package]]
name = "const-hex"
version = "1.11.3"
source = "registry+https://github.com/rust-lang/crates.io-index"
checksum = "5ba00838774b4ab0233e355d26710fbfc8327a05c017f6dc4873f876d1f79f78"
dependencies = [
 "cfg-if 1.0.0",
 "cpufeatures",
 "hex",
 "proptest",
 "serde",
]

[[package]]
name = "const-oid"
version = "0.9.6"
source = "registry+https://github.com/rust-lang/crates.io-index"
checksum = "c2459377285ad874054d797f3ccebf984978aa39129f6eafde5cdc8315b612f8"

[[package]]
name = "const-str"
version = "0.5.7"
source = "registry+https://github.com/rust-lang/crates.io-index"
checksum = "3618cccc083bb987a415d85c02ca6c9994ea5b44731ec28b9ecf09658655fba9"

[[package]]
name = "constant_time_eq"
version = "0.1.5"
source = "registry+https://github.com/rust-lang/crates.io-index"
checksum = "245097e9a4535ee1e3e3931fcfcd55a796a44c643e8596ff6566d68f09b87bbc"

[[package]]
name = "constant_time_eq"
version = "0.2.6"
source = "registry+https://github.com/rust-lang/crates.io-index"
checksum = "21a53c0a4d288377e7415b53dcfc3c04da5cdc2cc95c8d5ac178b58f0b861ad6"

[[package]]
name = "constant_time_eq"
version = "0.3.0"
source = "registry+https://github.com/rust-lang/crates.io-index"
checksum = "f7144d30dcf0fafbce74250a3963025d8d52177934239851c917d29f1df280c2"

[[package]]
name = "convert_case"
version = "0.4.0"
source = "registry+https://github.com/rust-lang/crates.io-index"
checksum = "6245d59a3e82a7fc217c5828a6692dbc6dfb63a0c8c90495621f7b9d79704a0e"

[[package]]
name = "convert_case"
version = "0.6.0"
source = "registry+https://github.com/rust-lang/crates.io-index"
checksum = "ec182b0ca2f35d8fc196cf3404988fd8b8c739a4d270ff118a398feb0cbec1ca"
dependencies = [
 "unicode-segmentation",
]

[[package]]
name = "core-foundation"
version = "0.9.4"
source = "registry+https://github.com/rust-lang/crates.io-index"
checksum = "91e195e091a93c46f7102ec7818a2aa394e1e1771c3ab4825963fa03e45afb8f"
dependencies = [
 "core-foundation-sys",
 "libc",
]

[[package]]
name = "core-foundation-sys"
version = "0.8.6"
source = "registry+https://github.com/rust-lang/crates.io-index"
checksum = "06ea2b9bc92be3c2baa9334a323ebca2d6f074ff852cd1d7b11064035cd3868f"

[[package]]
name = "core2"
version = "0.4.0"
source = "registry+https://github.com/rust-lang/crates.io-index"
checksum = "b49ba7ef1ad6107f8824dbe97de947cbaac53c44e7f9756a1fba0d37c1eec505"
dependencies = [
 "memchr",
]

[[package]]
name = "cpp_demangle"
version = "0.4.3"
source = "registry+https://github.com/rust-lang/crates.io-index"
checksum = "7e8227005286ec39567949b33df9896bcadfa6051bccca2488129f108ca23119"
dependencies = [
 "cfg-if 1.0.0",
]

[[package]]
name = "cpufeatures"
version = "0.2.12"
source = "registry+https://github.com/rust-lang/crates.io-index"
checksum = "53fe5e26ff1b7aef8bca9c6080520cfb8d9333c7568e1829cef191a9723e5504"
dependencies = [
 "libc",
]

[[package]]
name = "crc32c"
version = "0.6.5"
source = "registry+https://github.com/rust-lang/crates.io-index"
checksum = "89254598aa9b9fa608de44b3ae54c810f0f06d755e24c50177f1f8f31ff50ce2"
dependencies = [
 "rustc_version 0.4.0",
]

[[package]]
name = "crc32fast"
version = "1.4.0"
source = "registry+https://github.com/rust-lang/crates.io-index"
checksum = "b3855a8a784b474f333699ef2bbca9db2c4a1f6d9088a90a2d25b1eb53111eaa"
dependencies = [
 "cfg-if 1.0.0",
]

[[package]]
name = "criterion"
version = "0.4.0"
source = "registry+https://github.com/rust-lang/crates.io-index"
checksum = "e7c76e09c1aae2bc52b3d2f29e13c6572553b30c4aa1b8a49fd70de6412654cb"
dependencies = [
 "anes",
 "atty",
 "cast",
 "ciborium",
 "clap 3.2.25",
 "criterion-plot",
 "futures",
 "itertools 0.10.5",
 "lazy_static",
 "num-traits",
 "oorandom",
 "plotters",
 "rayon",
 "regex",
 "serde",
 "serde_derive",
 "serde_json",
 "tinytemplate",
 "tokio",
 "walkdir",
]

[[package]]
name = "criterion-plot"
version = "0.5.0"
source = "registry+https://github.com/rust-lang/crates.io-index"
checksum = "6b50826342786a51a89e2da3a28f1c32b06e387201bc2d19791f622c673706b1"
dependencies = [
 "cast",
 "itertools 0.10.5",
]

[[package]]
name = "crossbeam-channel"
version = "0.5.12"
source = "registry+https://github.com/rust-lang/crates.io-index"
checksum = "ab3db02a9c5b5121e1e42fbdb1aeb65f5e02624cc58c43f2884c6ccac0b82f95"
dependencies = [
 "crossbeam-utils",
]

[[package]]
name = "crossbeam-deque"
version = "0.8.5"
source = "registry+https://github.com/rust-lang/crates.io-index"
checksum = "613f8cc01fe9cf1a3eb3d7f488fd2fa8388403e97039e2f73692932e291a770d"
dependencies = [
 "crossbeam-epoch",
 "crossbeam-utils",
]

[[package]]
name = "crossbeam-epoch"
version = "0.9.18"
source = "registry+https://github.com/rust-lang/crates.io-index"
checksum = "5b82ac4a3c2ca9c3460964f020e1402edd5753411d7737aa39c3714ad1b5420e"
dependencies = [
 "crossbeam-utils",
]

[[package]]
name = "crossbeam-utils"
version = "0.8.19"
source = "registry+https://github.com/rust-lang/crates.io-index"
checksum = "248e3bacc7dc6baa3b21e405ee045c3047101a49145e7e9eca583ab4c2ca5345"

[[package]]
name = "crossterm"
version = "0.21.0"
source = "registry+https://github.com/rust-lang/crates.io-index"
checksum = "486d44227f71a1ef39554c0dc47e44b9f4139927c75043312690c3f476d1d788"
dependencies = [
 "bitflags 1.3.2",
 "crossterm_winapi 0.8.0",
 "libc",
 "mio 0.7.14",
 "parking_lot 0.11.2",
 "signal-hook",
 "signal-hook-mio",
 "winapi",
]

[[package]]
name = "crossterm"
version = "0.22.1"
source = "registry+https://github.com/rust-lang/crates.io-index"
checksum = "c85525306c4291d1b73ce93c8acf9c339f9b213aef6c1d85c3830cbf1c16325c"
dependencies = [
 "bitflags 1.3.2",
 "crossterm_winapi 0.9.1",
 "libc",
 "mio 0.7.14",
 "parking_lot 0.11.2",
 "signal-hook",
 "signal-hook-mio",
 "winapi",
]

[[package]]
name = "crossterm"
version = "0.25.0"
source = "registry+https://github.com/rust-lang/crates.io-index"
checksum = "e64e6c0fbe2c17357405f7c758c1ef960fce08bdfb2c03d88d2a18d7e09c4b67"
dependencies = [
 "bitflags 1.3.2",
 "crossterm_winapi 0.9.1",
 "libc",
 "mio 0.8.11",
 "parking_lot 0.12.1",
 "signal-hook",
 "signal-hook-mio",
 "winapi",
]

[[package]]
name = "crossterm"
version = "0.26.1"
source = "registry+https://github.com/rust-lang/crates.io-index"
checksum = "a84cda67535339806297f1b331d6dd6320470d2a0fe65381e79ee9e156dd3d13"
dependencies = [
 "bitflags 1.3.2",
 "crossterm_winapi 0.9.1",
 "libc",
 "mio 0.8.11",
 "parking_lot 0.12.1",
 "signal-hook",
 "signal-hook-mio",
 "winapi",
]

[[package]]
name = "crossterm"
version = "0.27.0"
source = "registry+https://github.com/rust-lang/crates.io-index"
checksum = "f476fe445d41c9e991fd07515a6f463074b782242ccf4a5b7b1d1012e70824df"
dependencies = [
 "bitflags 2.5.0",
 "crossterm_winapi 0.9.1",
 "libc",
 "mio 0.8.11",
 "parking_lot 0.12.1",
 "signal-hook",
 "signal-hook-mio",
 "winapi",
]

[[package]]
name = "crossterm_winapi"
version = "0.8.0"
source = "registry+https://github.com/rust-lang/crates.io-index"
checksum = "3a6966607622438301997d3dac0d2f6e9a90c68bb6bc1785ea98456ab93c0507"
dependencies = [
 "winapi",
]

[[package]]
name = "crossterm_winapi"
version = "0.9.1"
source = "registry+https://github.com/rust-lang/crates.io-index"
checksum = "acdd7c62a3665c7f6830a51635d9ac9b23ed385797f70a83bb8bafe9c572ab2b"
dependencies = [
 "winapi",
]

[[package]]
name = "crunchy"
version = "0.2.2"
source = "registry+https://github.com/rust-lang/crates.io-index"
checksum = "7a81dae078cea95a014a339291cec439d2f232ebe854a9d672b796c6afafa9b7"

[[package]]
name = "crypto-bigint"
version = "0.4.9"
source = "registry+https://github.com/rust-lang/crates.io-index"
checksum = "ef2b4b23cddf68b89b8f8069890e8c270d54e2d5fe1b143820234805e4cb17ef"
dependencies = [
 "generic-array 0.14.7",
 "rand_core 0.6.4",
 "subtle",
 "zeroize",
]

[[package]]
name = "crypto-bigint"
version = "0.5.5"
source = "registry+https://github.com/rust-lang/crates.io-index"
checksum = "0dc92fb57ca44df6db8059111ab3af99a63d5d0f8375d9972e319a379c6bab76"
dependencies = [
 "generic-array 0.14.7",
 "rand_core 0.6.4",
 "subtle",
 "zeroize",
]

[[package]]
name = "crypto-common"
version = "0.1.6"
source = "registry+https://github.com/rust-lang/crates.io-index"
checksum = "1bfb12502f3fc46cca1bb51ac28df9d618d813cdc3d2f25b9fe775a34af26bb3"
dependencies = [
 "generic-array 0.14.7",
 "rand_core 0.6.4",
 "typenum",
]

[[package]]
name = "crypto-mac"
version = "0.11.1"
source = "registry+https://github.com/rust-lang/crates.io-index"
checksum = "b1d1a86f49236c215f271d40892d5fc950490551400b02ef360692c29815c714"
dependencies = [
 "generic-array 0.14.7",
 "subtle",
]

[[package]]
name = "csv"
version = "1.3.0"
source = "registry+https://github.com/rust-lang/crates.io-index"
checksum = "ac574ff4d437a7b5ad237ef331c17ccca63c46479e5b5453eb8e10bb99a759fe"
dependencies = [
 "csv-core",
 "itoa",
 "ryu",
 "serde",
]

[[package]]
name = "csv-core"
version = "0.1.11"
source = "registry+https://github.com/rust-lang/crates.io-index"
checksum = "5efa2b3d7902f4b634a20cae3c9c4e6209dc4779feb6863329607560143efa70"
dependencies = [
 "memchr",
]

[[package]]
name = "ctor"
version = "0.1.26"
source = "registry+https://github.com/rust-lang/crates.io-index"
checksum = "6d2301688392eb071b0bf1a37be05c469d3cc4dbbd95df672fe28ab021e6a096"
dependencies = [
 "quote 1.0.35",
 "syn 1.0.109",
]

[[package]]
name = "ctor"
version = "0.2.7"
source = "registry+https://github.com/rust-lang/crates.io-index"
checksum = "ad291aa74992b9b7a7e88c38acbbf6ad7e107f1d90ee8775b7bc1fc3394f485c"
dependencies = [
 "quote 1.0.35",
 "syn 2.0.57",
]

[[package]]
name = "ctr"
version = "0.9.2"
source = "registry+https://github.com/rust-lang/crates.io-index"
checksum = "0369ee1ad671834580515889b80f2ea915f23b8be8d0daa4bbaf2ac5c7590835"
dependencies = [
 "cipher",
]

[[package]]
name = "curve25519-dalek-fiat"
version = "0.1.0"
source = "registry+https://github.com/rust-lang/crates.io-index"
checksum = "44339b9ecede7f72a0d3b012bf9bb5a616dc8bfde23ce544e42da075c87198f0"
dependencies = [
 "byteorder",
 "digest 0.9.0",
 "fiat-crypto",
 "rand_core 0.6.4",
 "subtle",
 "zeroize",
]

[[package]]
name = "curve25519-dalek-ng"
version = "4.1.1"
source = "registry+https://github.com/rust-lang/crates.io-index"
checksum = "1c359b7249347e46fb28804470d071c921156ad62b3eef5d34e2ba867533dec8"
dependencies = [
 "byteorder",
 "digest 0.9.0",
 "rand_core 0.6.4",
 "serde",
 "subtle-ng",
 "zeroize",
]

[[package]]
name = "darling"
version = "0.14.4"
source = "registry+https://github.com/rust-lang/crates.io-index"
checksum = "7b750cb3417fd1b327431a470f388520309479ab0bf5e323505daf0290cd3850"
dependencies = [
 "darling_core 0.14.4",
 "darling_macro 0.14.4",
]

[[package]]
name = "darling"
version = "0.20.8"
source = "registry+https://github.com/rust-lang/crates.io-index"
checksum = "54e36fcd13ed84ffdfda6f5be89b31287cbb80c439841fe69e04841435464391"
dependencies = [
 "darling_core 0.20.8",
 "darling_macro 0.20.8",
]

[[package]]
name = "darling_core"
version = "0.14.4"
source = "registry+https://github.com/rust-lang/crates.io-index"
checksum = "109c1ca6e6b7f82cc233a97004ea8ed7ca123a9af07a8230878fcfda9b158bf0"
dependencies = [
 "fnv",
 "ident_case",
 "proc-macro2 1.0.79",
 "quote 1.0.35",
 "strsim 0.10.0",
 "syn 1.0.109",
]

[[package]]
name = "darling_core"
version = "0.20.8"
source = "registry+https://github.com/rust-lang/crates.io-index"
checksum = "9c2cf1c23a687a1feeb728783b993c4e1ad83d99f351801977dd809b48d0a70f"
dependencies = [
 "fnv",
 "ident_case",
 "proc-macro2 1.0.79",
 "quote 1.0.35",
 "strsim 0.10.0",
 "syn 2.0.57",
]

[[package]]
name = "darling_macro"
version = "0.14.4"
source = "registry+https://github.com/rust-lang/crates.io-index"
checksum = "a4aab4dbc9f7611d8b55048a3a16d2d010c2c8334e46304b40ac1cc14bf3b48e"
dependencies = [
 "darling_core 0.14.4",
 "quote 1.0.35",
 "syn 1.0.109",
]

[[package]]
name = "darling_macro"
version = "0.20.8"
source = "registry+https://github.com/rust-lang/crates.io-index"
checksum = "a668eda54683121533a393014d8692171709ff57a7d61f187b6e782719f8933f"
dependencies = [
 "darling_core 0.20.8",
 "quote 1.0.35",
 "syn 2.0.57",
]

[[package]]
name = "dashmap"
version = "5.5.3"
source = "registry+https://github.com/rust-lang/crates.io-index"
checksum = "978747c1d849a7d2ee5e8adc0159961c48fb7e5db2f06af6723b80123bb53856"
dependencies = [
 "cfg-if 1.0.0",
 "hashbrown 0.14.3",
 "lock_api",
 "once_cell",
 "parking_lot_core 0.9.9",
]

[[package]]
name = "data-encoding"
version = "2.5.0"
source = "registry+https://github.com/rust-lang/crates.io-index"
checksum = "7e962a19be5cfc3f3bf6dd8f61eb50107f356ad6270fbb3ed41476571db78be5"

[[package]]
name = "data-encoding-macro"
version = "0.1.14"
source = "registry+https://github.com/rust-lang/crates.io-index"
checksum = "20c01c06f5f429efdf2bae21eb67c28b3df3cf85b7dd2d8ef09c0838dac5d33e"
dependencies = [
 "data-encoding",
 "data-encoding-macro-internal",
]

[[package]]
name = "data-encoding-macro-internal"
version = "0.1.12"
source = "registry+https://github.com/rust-lang/crates.io-index"
checksum = "0047d07f2c89b17dd631c80450d69841a6b5d7fb17278cbc43d7e4cfcf2576f3"
dependencies = [
 "data-encoding",
 "syn 1.0.109",
]

[[package]]
name = "datatest-stable"
version = "0.1.3"
source = "registry+https://github.com/rust-lang/crates.io-index"
checksum = "4eaf86e44e9f0a21f6e42d8e7f83c9ee049f081745eeed1c6f47a613c76e5977"
dependencies = [
 "libtest-mimic",
 "regex",
 "walkdir",
]

[[package]]
name = "debug-ignore"
version = "1.0.5"
source = "registry+https://github.com/rust-lang/crates.io-index"
checksum = "ffe7ed1d93f4553003e20b629abe9085e1e81b1429520f897f8f8860bc6dfc21"

[[package]]
name = "debugid"
version = "0.8.0"
source = "registry+https://github.com/rust-lang/crates.io-index"
checksum = "bef552e6f588e446098f6ba40d89ac146c8c7b64aade83c051ee00bb5d2bc18d"
dependencies = [
 "serde",
 "uuid 1.8.0",
]

[[package]]
name = "der"
version = "0.6.1"
source = "registry+https://github.com/rust-lang/crates.io-index"
checksum = "f1a467a65c5e759bce6e65eaf91cc29f466cdc57cb65777bd646872a8a1fd4de"
dependencies = [
 "const-oid",
 "pem-rfc7468 0.6.0",
 "zeroize",
]

[[package]]
name = "der"
version = "0.7.8"
source = "registry+https://github.com/rust-lang/crates.io-index"
checksum = "fffa369a668c8af7dbf8b5e56c9f744fbd399949ed171606040001947de40b1c"
dependencies = [
 "const-oid",
 "pem-rfc7468 0.7.0",
 "zeroize",
]

[[package]]
name = "der-parser"
version = "8.2.0"
source = "registry+https://github.com/rust-lang/crates.io-index"
checksum = "dbd676fbbab537128ef0278adb5576cf363cff6aa22a7b24effe97347cfab61e"
dependencies = [
 "asn1-rs",
 "displaydoc",
 "nom 7.1.3",
 "num-bigint 0.4.4",
 "num-traits",
 "rusticata-macros",
]

[[package]]
name = "deranged"
version = "0.3.11"
source = "registry+https://github.com/rust-lang/crates.io-index"
checksum = "b42b6fa04a440b495c8b04d0e71b707c585f83cb9cb28cf8cd0d976c315e31b4"
dependencies = [
 "powerfmt",
 "serde",
]

[[package]]
name = "derivative"
version = "2.2.0"
source = "registry+https://github.com/rust-lang/crates.io-index"
checksum = "fcc3dd5e9e9c0b295d6e1e4d811fb6f157d5ffd784b8d202fc62eac8035a770b"
dependencies = [
 "proc-macro2 1.0.79",
 "quote 1.0.35",
 "syn 1.0.109",
]

[[package]]
name = "derive-syn-parse"
version = "0.1.5"
source = "registry+https://github.com/rust-lang/crates.io-index"
checksum = "e79116f119dd1dba1abf1f3405f03b9b0e79a27a3883864bfebded8a3dc768cd"
dependencies = [
 "proc-macro2 1.0.79",
 "quote 1.0.35",
 "syn 1.0.109",
]

[[package]]
name = "derive_arbitrary"
version = "1.3.2"
source = "registry+https://github.com/rust-lang/crates.io-index"
checksum = "67e77553c4162a157adbf834ebae5b415acbecbeafc7a74b0e886657506a7611"
dependencies = [
 "proc-macro2 1.0.79",
 "quote 1.0.35",
 "syn 2.0.57",
]

[[package]]
name = "derive_builder"
version = "0.12.0"
source = "registry+https://github.com/rust-lang/crates.io-index"
checksum = "8d67778784b508018359cbc8696edb3db78160bab2c2a28ba7f56ef6932997f8"
dependencies = [
 "derive_builder_macro 0.12.0",
]

[[package]]
name = "derive_builder"
version = "0.20.0"
source = "registry+https://github.com/rust-lang/crates.io-index"
checksum = "0350b5cb0331628a5916d6c5c0b72e97393b8b6b03b47a9284f4e7f5a405ffd7"
dependencies = [
 "derive_builder_macro 0.20.0",
]

[[package]]
name = "derive_builder_core"
version = "0.12.0"
source = "registry+https://github.com/rust-lang/crates.io-index"
checksum = "c11bdc11a0c47bc7d37d582b5285da6849c96681023680b906673c5707af7b0f"
dependencies = [
 "darling 0.14.4",
 "proc-macro2 1.0.79",
 "quote 1.0.35",
 "syn 1.0.109",
]

[[package]]
name = "derive_builder_core"
version = "0.20.0"
source = "registry+https://github.com/rust-lang/crates.io-index"
checksum = "d48cda787f839151732d396ac69e3473923d54312c070ee21e9effcaa8ca0b1d"
dependencies = [
 "darling 0.20.8",
 "proc-macro2 1.0.79",
 "quote 1.0.35",
 "syn 2.0.57",
]

[[package]]
name = "derive_builder_macro"
version = "0.12.0"
source = "registry+https://github.com/rust-lang/crates.io-index"
checksum = "ebcda35c7a396850a55ffeac740804b40ffec779b98fffbb1738f4033f0ee79e"
dependencies = [
 "derive_builder_core 0.12.0",
 "syn 1.0.109",
]

[[package]]
name = "derive_builder_macro"
version = "0.20.0"
source = "registry+https://github.com/rust-lang/crates.io-index"
checksum = "206868b8242f27cecce124c19fd88157fbd0dd334df2587f36417bafbc85097b"
dependencies = [
 "derive_builder_core 0.20.0",
 "syn 2.0.57",
]

[[package]]
name = "derive_more"
version = "0.99.17"
source = "registry+https://github.com/rust-lang/crates.io-index"
checksum = "4fb810d30a7c1953f91334de7244731fc3f3c10d7fe163338a35b9f640960321"
dependencies = [
 "convert_case 0.4.0",
 "proc-macro2 1.0.79",
 "quote 1.0.35",
 "rustc_version 0.4.0",
 "syn 1.0.109",
]

[[package]]
name = "determinator"
version = "0.10.0"
source = "registry+https://github.com/rust-lang/crates.io-index"
checksum = "c644b91adb5bcc66d3533607b6d3ee5c1c2d858d2d95e41dd6aae673e29e0509"
dependencies = [
 "camino",
 "globset",
 "guppy",
 "guppy-workspace-hack",
 "once_cell",
 "petgraph 0.6.4",
 "rayon",
 "serde",
 "toml 0.5.11",
]

[[package]]
name = "deunicode"
version = "0.4.5"
source = "registry+https://github.com/rust-lang/crates.io-index"
checksum = "71dbf1bf89c23e9cd1baf5e654f622872655f195b36588dc9dc38f7eda30758c"

[[package]]
name = "deunicode"
version = "1.4.3"
source = "registry+https://github.com/rust-lang/crates.io-index"
checksum = "b6e854126756c496b8c81dec88f9a706b15b875c5849d4097a3854476b9fdf94"

[[package]]
name = "dialoguer"
version = "0.11.0"
source = "registry+https://github.com/rust-lang/crates.io-index"
checksum = "658bce805d770f407bc62102fca7c2c64ceef2fbcb2b8bd19d2765ce093980de"
dependencies = [
 "console",
 "shell-words",
 "tempfile",
 "thiserror",
 "zeroize",
]

[[package]]
name = "diesel"
version = "2.1.5"
source = "registry+https://github.com/rust-lang/crates.io-index"
checksum = "03fc05c17098f21b89bc7d98fe1dd3cce2c11c2ad8e145f2a44fe08ed28eb559"
dependencies = [
 "bitflags 2.5.0",
 "byteorder",
 "chrono",
 "diesel_derives",
 "itoa",
 "pq-sys",
 "r2d2",
 "serde_json",
]

[[package]]
name = "diesel-derive-enum"
version = "2.1.0"
source = "registry+https://github.com/rust-lang/crates.io-index"
checksum = "81c5131a2895ef64741dad1d483f358c2a229a3a2d1b256778cdc5e146db64d4"
dependencies = [
 "heck 0.4.1",
 "proc-macro2 1.0.79",
 "quote 1.0.35",
 "syn 2.0.57",
]

[[package]]
name = "diesel_derives"
version = "2.1.3"
source = "registry+https://github.com/rust-lang/crates.io-index"
checksum = "5d02eecb814ae714ffe61ddc2db2dd03e6c49a42e269b5001355500d431cce0c"
dependencies = [
 "diesel_table_macro_syntax",
 "proc-macro2 1.0.79",
 "quote 1.0.35",
 "syn 2.0.57",
]

[[package]]
name = "diesel_migrations"
version = "2.1.0"
source = "registry+https://github.com/rust-lang/crates.io-index"
checksum = "6036b3f0120c5961381b570ee20a02432d7e2d27ea60de9578799cf9156914ac"
dependencies = [
 "diesel",
 "migrations_internals",
 "migrations_macros",
]

[[package]]
name = "diesel_table_macro_syntax"
version = "0.1.0"
source = "registry+https://github.com/rust-lang/crates.io-index"
checksum = "fc5557efc453706fed5e4fa85006fe9817c224c3f480a34c7e5959fd700921c5"
dependencies = [
 "syn 2.0.57",
]

[[package]]
name = "diff"
version = "0.1.13"
source = "registry+https://github.com/rust-lang/crates.io-index"
checksum = "56254986775e3233ffa9c4d7d3faaf6d36a2c09d30b20687e9f88bc8bafc16c8"

[[package]]
name = "difference"
version = "2.0.0"
source = "registry+https://github.com/rust-lang/crates.io-index"
checksum = "524cbf6897b527295dff137cec09ecf3a05f4fddffd7dfcd1585403449e74198"

[[package]]
name = "difflib"
version = "0.4.0"
source = "registry+https://github.com/rust-lang/crates.io-index"
checksum = "6184e33543162437515c2e2b48714794e37845ec9851711914eec9d308f6ebe8"

[[package]]
name = "diffus"
version = "0.10.0"
source = "registry+https://github.com/rust-lang/crates.io-index"
checksum = "3c0ff24a73b51d9009c40897faf87d31b77345c90ffbf4dc3a1d2957032c5653"
dependencies = [
 "itertools 0.10.5",
]

[[package]]
name = "diffy"
version = "0.3.0"
source = "registry+https://github.com/rust-lang/crates.io-index"
checksum = "e616e59155c92257e84970156f506287853355f58cd4a6eb167385722c32b790"
dependencies = [
 "nu-ansi-term",
]

[[package]]
name = "digest"
version = "0.9.0"
source = "registry+https://github.com/rust-lang/crates.io-index"
checksum = "d3dd60d1080a57a05ab032377049e0591415d2b31afd7028356dbf3cc6dcb066"
dependencies = [
 "generic-array 0.14.7",
]

[[package]]
name = "digest"
version = "0.10.7"
source = "registry+https://github.com/rust-lang/crates.io-index"
checksum = "9ed9a281f7bc9b7576e61468ba615a66a5c8cfdff42420a70aa82701a3b1e292"
dependencies = [
 "block-buffer 0.10.4",
 "const-oid",
 "crypto-common",
 "subtle",
]

[[package]]
name = "dirs"
version = "4.0.0"
source = "registry+https://github.com/rust-lang/crates.io-index"
checksum = "ca3aa72a6f96ea37bbc5aa912f6788242832f75369bdfdadcb0e38423f100059"
dependencies = [
 "dirs-sys 0.3.7",
]

[[package]]
name = "dirs"
version = "5.0.1"
source = "registry+https://github.com/rust-lang/crates.io-index"
checksum = "44c45a9d03d6676652bcb5e724c7e988de1acad23a711b5217ab9cbecbec2225"
dependencies = [
 "dirs-sys 0.4.1",
]

[[package]]
name = "dirs-next"
version = "2.0.0"
source = "registry+https://github.com/rust-lang/crates.io-index"
checksum = "b98cf8ebf19c3d1b223e151f99a4f9f0690dca41414773390fc824184ac833e1"
dependencies = [
 "cfg-if 1.0.0",
 "dirs-sys-next",
]

[[package]]
name = "dirs-sys"
version = "0.3.7"
source = "registry+https://github.com/rust-lang/crates.io-index"
checksum = "1b1d1d91c932ef41c0f2663aa8b0ca0342d444d842c06914aa0a7e352d0bada6"
dependencies = [
 "libc",
 "redox_users",
 "winapi",
]

[[package]]
name = "dirs-sys"
version = "0.4.1"
source = "registry+https://github.com/rust-lang/crates.io-index"
checksum = "520f05a5cbd335fae5a99ff7a6ab8627577660ee5cfd6a94a6a929b52ff0321c"
dependencies = [
 "libc",
 "option-ext",
 "redox_users",
 "windows-sys 0.48.0",
]

[[package]]
name = "dirs-sys-next"
version = "0.1.2"
source = "registry+https://github.com/rust-lang/crates.io-index"
checksum = "4ebda144c4fe02d1f7ea1a7d9641b6fc6b580adcfa024ae48797ecdeb6825b4d"
dependencies = [
 "libc",
 "redox_users",
 "winapi",
]

[[package]]
name = "displaydoc"
version = "0.2.4"
source = "registry+https://github.com/rust-lang/crates.io-index"
checksum = "487585f4d0c6655fe74905e2504d8ad6908e4db67f744eb140876906c2f3175d"
dependencies = [
 "proc-macro2 1.0.79",
 "quote 1.0.35",
 "syn 2.0.57",
]

[[package]]
name = "dissimilar"
version = "1.0.7"
source = "registry+https://github.com/rust-lang/crates.io-index"
checksum = "86e3bdc80eee6e16b2b6b0f87fbc98c04bee3455e35174c0de1a125d0688c632"

[[package]]
name = "doc-comment"
version = "0.3.3"
source = "registry+https://github.com/rust-lang/crates.io-index"
checksum = "fea41bba32d969b513997752735605054bc0dfa92b4c56bf1189f2e174be7a10"

[[package]]
name = "downcast"
version = "0.11.0"
source = "registry+https://github.com/rust-lang/crates.io-index"
checksum = "1435fa1053d8b2fbbe9be7e97eca7f33d37b28409959813daefc1446a14247f1"

[[package]]
name = "downcast-rs"
version = "1.2.0"
source = "registry+https://github.com/rust-lang/crates.io-index"
checksum = "9ea835d29036a4087793836fa931b08837ad5e957da9e23886b29586fb9b6650"

[[package]]
name = "dunce"
version = "1.0.4"
source = "registry+https://github.com/rust-lang/crates.io-index"
checksum = "56ce8c6da7551ec6c462cbaf3bfbc75131ebbfa1c944aeaa9dab51ca1c5f0c3b"

[[package]]
name = "duration-str"
version = "0.5.1"
source = "registry+https://github.com/rust-lang/crates.io-index"
checksum = "d9f037c488d179e21c87ef5fa9c331e8e62f5dddfa84618b41bb197da03edff1"
dependencies = [
 "chrono",
 "nom 7.1.3",
 "rust_decimal",
 "serde",
 "thiserror",
 "time 0.3.34",
]

[[package]]
name = "dyn-clone"
version = "1.0.17"
source = "registry+https://github.com/rust-lang/crates.io-index"
checksum = "0d6ef0072f8a535281e4876be788938b528e9a1d43900b82c2569af7da799125"

[[package]]
name = "ecdsa"
version = "0.14.8"
source = "registry+https://github.com/rust-lang/crates.io-index"
checksum = "413301934810f597c1d19ca71c8710e99a3f1ba28a0d2ebc01551a2daeea3c5c"
dependencies = [
 "der 0.6.1",
 "elliptic-curve 0.12.3",
 "rfc6979 0.3.1",
 "signature 1.6.4",
]

[[package]]
name = "ecdsa"
version = "0.16.9"
source = "registry+https://github.com/rust-lang/crates.io-index"
checksum = "ee27f32b5c5292967d2d4a9d7f1e0b0aed2c15daded5a60300e4abb9d8020bca"
dependencies = [
 "der 0.7.8",
 "digest 0.10.7",
 "elliptic-curve 0.13.8",
 "rfc6979 0.4.0",
 "signature 2.2.0",
 "spki 0.7.3",
]

[[package]]
name = "ed25519"
version = "1.5.3"
source = "registry+https://github.com/rust-lang/crates.io-index"
checksum = "91cff35c70bba8a626e3185d8cd48cc11b5437e1a5bcd15b9b5fa3c64b6dfee7"
dependencies = [
 "pkcs8 0.9.0",
 "serde",
 "signature 1.6.4",
 "zeroize",
]

[[package]]
name = "ed25519-consensus"
version = "2.1.0"
source = "registry+https://github.com/rust-lang/crates.io-index"
checksum = "3c8465edc8ee7436ffea81d21a019b16676ee3db267aa8d5a8d729581ecf998b"
dependencies = [
 "curve25519-dalek-ng",
 "hex",
 "rand_core 0.6.4",
 "serde",
 "sha2 0.9.9",
 "thiserror",
 "zeroize",
]

[[package]]
name = "ed25519-dalek-fiat"
version = "0.1.0"
source = "registry+https://github.com/rust-lang/crates.io-index"
checksum = "97c6ac152eba578c1c53d2cefe8ad02e239e3d6f971b0f1ef3cb54cd66037fa0"
dependencies = [
 "curve25519-dalek-fiat",
 "ed25519",
 "rand 0.8.5",
 "serde",
 "serde_bytes",
 "sha2 0.9.9",
 "zeroize",
]

[[package]]
name = "either"
version = "1.10.0"
source = "registry+https://github.com/rust-lang/crates.io-index"
checksum = "11157ac094ffbdde99aa67b23417ebdd801842852b500e395a45a9c0aac03e4a"

[[package]]
name = "elliptic-curve"
version = "0.12.3"
source = "registry+https://github.com/rust-lang/crates.io-index"
checksum = "e7bb888ab5300a19b8e5bceef25ac745ad065f3c9f7efc6de1b91958110891d3"
dependencies = [
 "base16ct 0.1.1",
 "crypto-bigint 0.4.9",
 "der 0.6.1",
 "digest 0.10.7",
 "ff 0.12.1",
 "generic-array 0.14.7",
 "group 0.12.1",
 "rand_core 0.6.4",
 "sec1 0.3.0",
 "subtle",
 "zeroize",
]

[[package]]
name = "elliptic-curve"
version = "0.13.8"
source = "registry+https://github.com/rust-lang/crates.io-index"
checksum = "b5e6043086bf7973472e0c7dff2142ea0b680d30e18d9cc40f267efbf222bd47"
dependencies = [
 "base16ct 0.2.0",
 "crypto-bigint 0.5.5",
 "digest 0.10.7",
 "ff 0.13.0",
 "generic-array 0.14.7",
 "group 0.13.0",
 "pem-rfc7468 0.7.0",
 "pkcs8 0.10.2",
 "rand_core 0.6.4",
 "sec1 0.7.3",
 "subtle",
 "zeroize",
]

[[package]]
name = "embedded-io"
version = "0.4.0"
source = "registry+https://github.com/rust-lang/crates.io-index"
checksum = "ef1a6892d9eef45c8fa6b9e0086428a2cca8491aca8f787c534a3d6d0bcb3ced"

[[package]]
name = "ena"
version = "0.14.2"
source = "registry+https://github.com/rust-lang/crates.io-index"
checksum = "c533630cf40e9caa44bd91aadc88a75d75a4c3a12b4cfde353cbed41daa1e1f1"
dependencies = [
 "log",
]

[[package]]
name = "encode_unicode"
version = "0.3.6"
source = "registry+https://github.com/rust-lang/crates.io-index"
checksum = "a357d28ed41a50f9c765dbfe56cbc04a64e53e5fc58ba79fbc34c10ef3df831f"

[[package]]
name = "encode_unicode"
version = "1.0.0"
source = "registry+https://github.com/rust-lang/crates.io-index"
checksum = "34aa73646ffb006b8f5147f3dc182bd4bcb190227ce861fc4a4844bf8e3cb2c0"

[[package]]
name = "encoding_rs"
version = "0.8.33"
source = "registry+https://github.com/rust-lang/crates.io-index"
checksum = "7268b386296a025e474d5140678f75d6de9493ae55a5d709eeb9dd08149945e1"
dependencies = [
 "cfg-if 1.0.0",
]

[[package]]
name = "endian-type"
version = "0.1.2"
source = "registry+https://github.com/rust-lang/crates.io-index"
checksum = "c34f04666d835ff5d62e058c3995147c06f42fe86ff053337632bca83e42702d"

[[package]]
name = "enr"
version = "0.10.0"
source = "registry+https://github.com/rust-lang/crates.io-index"
checksum = "2a3d8dc56e02f954cac8eb489772c552c473346fc34f67412bb6244fd647f7e4"
dependencies = [
 "base64 0.21.7",
 "bytes",
 "hex",
 "k256 0.13.3",
 "log",
 "rand 0.8.5",
 "rlp",
 "serde",
 "sha3 0.10.8",
 "zeroize",
]

[[package]]
name = "enum-compat-util"
version = "0.1.0"
source = "git+https://github.com/fuzzland/ityfuzz-sui-fork.git#a88e9595de3c04a540c059d5f4b6d148419c508c"
dependencies = [
 "serde_yaml 0.8.26",
]

[[package]]
name = "enum_dispatch"
version = "0.3.13"
source = "registry+https://github.com/rust-lang/crates.io-index"
checksum = "aa18ce2bc66555b3218614519ac839ddb759a7d6720732f979ef8d13be147ecd"
dependencies = [
 "once_cell",
 "proc-macro2 1.0.79",
 "quote 1.0.35",
 "syn 2.0.57",
]

[[package]]
name = "enumn"
version = "0.1.13"
source = "registry+https://github.com/rust-lang/crates.io-index"
checksum = "6fd000fd6988e73bbe993ea3db9b1aa64906ab88766d654973924340c8cddb42"
dependencies = [
 "proc-macro2 1.0.79",
 "quote 1.0.35",
 "syn 2.0.57",
]

[[package]]
name = "env_logger"
version = "0.9.3"
source = "registry+https://github.com/rust-lang/crates.io-index"
checksum = "a12e6657c4c97ebab115a42dcee77225f7f482cdd841cf7088c657a42e9e00e7"
dependencies = [
 "atty",
 "humantime",
 "log",
 "regex",
 "termcolor",
]

[[package]]
name = "equivalent"
version = "1.0.1"
source = "registry+https://github.com/rust-lang/crates.io-index"
checksum = "5443807d6dff69373d433ab9ef5378ad8df50ca6298caf15de6e52e24aaf54d5"

[[package]]
name = "erasable"
version = "1.2.1"
source = "registry+https://github.com/rust-lang/crates.io-index"
checksum = "5f11890ce181d47a64e5d1eb4b6caba0e7bae911a356723740d058a5d0340b7d"
dependencies = [
 "autocfg",
 "scopeguard",
]

[[package]]
name = "erased-serde"
version = "0.3.31"
source = "registry+https://github.com/rust-lang/crates.io-index"
checksum = "6c138974f9d5e7fe373eb04df7cae98833802ae4b11c24ac7039a21d5af4b26c"
dependencies = [
 "serde",
]

[[package]]
name = "erased-serde"
version = "0.4.4"
source = "registry+https://github.com/rust-lang/crates.io-index"
checksum = "2b73807008a3c7f171cc40312f37d95ef0396e048b5848d775f54b1a4dd4a0d3"
dependencies = [
 "serde",
]

[[package]]
name = "errno"
version = "0.2.8"
source = "registry+https://github.com/rust-lang/crates.io-index"
checksum = "f639046355ee4f37944e44f60642c6f3a7efa3cf6b78c78a0d989a8ce6c396a1"
dependencies = [
 "errno-dragonfly",
 "libc",
 "winapi",
]

[[package]]
name = "errno"
version = "0.3.8"
source = "registry+https://github.com/rust-lang/crates.io-index"
checksum = "a258e46cdc063eb8519c00b9fc845fc47bcfca4130e2f08e88665ceda8474245"
dependencies = [
 "libc",
 "windows-sys 0.52.0",
]

[[package]]
name = "errno-dragonfly"
version = "0.1.2"
source = "registry+https://github.com/rust-lang/crates.io-index"
checksum = "aa68f1b12764fab894d2755d2518754e71b4fd80ecfb822714a1206c2aab39bf"
dependencies = [
 "cc",
 "libc",
]

[[package]]
name = "error-code"
version = "2.3.1"
source = "registry+https://github.com/rust-lang/crates.io-index"
checksum = "64f18991e7bf11e7ffee451b5318b5c1a73c52d0d0ada6e5a3017c8c1ced6a21"
dependencies = [
 "libc",
 "str-buf",
]

[[package]]
name = "eth-keystore"
version = "0.5.0"
source = "registry+https://github.com/rust-lang/crates.io-index"
checksum = "1fda3bf123be441da5260717e0661c25a2fd9cb2b2c1d20bf2e05580047158ab"
dependencies = [
 "aes",
 "ctr",
 "digest 0.10.7",
 "hex",
 "hmac 0.12.1",
 "pbkdf2 0.11.0",
 "rand 0.8.5",
 "scrypt",
 "serde",
 "serde_json",
 "sha2 0.10.8",
 "sha3 0.10.8",
 "thiserror",
 "uuid 0.8.2",
]

[[package]]
name = "ethabi"
version = "18.0.0"
source = "registry+https://github.com/rust-lang/crates.io-index"
checksum = "7413c5f74cc903ea37386a8965a936cbeb334bd270862fdece542c1b2dcbc898"
dependencies = [
 "ethereum-types",
 "hex",
 "once_cell",
 "regex",
 "serde",
 "serde_json",
 "sha3 0.10.8",
 "thiserror",
 "uint",
]

[[package]]
name = "ethbloom"
version = "0.13.0"
source = "registry+https://github.com/rust-lang/crates.io-index"
checksum = "c22d4b5885b6aa2fe5e8b9329fb8d232bf739e434e6b87347c63bdd00c120f60"
dependencies = [
 "crunchy",
 "fixed-hash 0.8.0",
 "impl-codec 0.6.0",
 "impl-rlp",
 "impl-serde 0.4.0",
 "scale-info",
 "tiny-keccak",
]

[[package]]
name = "ethereum-types"
version = "0.14.1"
source = "registry+https://github.com/rust-lang/crates.io-index"
checksum = "02d215cbf040552efcbe99a38372fe80ab9d00268e20012b79fcd0f073edd8ee"
dependencies = [
 "ethbloom",
 "fixed-hash 0.8.0",
 "impl-codec 0.6.0",
 "impl-rlp",
 "impl-serde 0.4.0",
 "primitive-types 0.12.2",
 "scale-info",
 "uint",
]

[[package]]
name = "ethereum_ssz"
version = "0.5.3"
source = "registry+https://github.com/rust-lang/crates.io-index"
checksum = "e61ffea29f26e8249d35128a82ec8d3bd4fbc80179ea5f5e5e3daafef6a80fcb"
dependencies = [
 "ethereum-types",
 "itertools 0.10.5",
 "smallvec",
]

[[package]]
name = "ethers"
version = "2.0.14"
source = "registry+https://github.com/rust-lang/crates.io-index"
checksum = "816841ea989f0c69e459af1cf23a6b0033b19a55424a1ea3a30099becdb8dec0"
dependencies = [
 "ethers-addressbook",
 "ethers-contract",
 "ethers-core",
 "ethers-etherscan",
 "ethers-middleware",
 "ethers-providers",
 "ethers-signers",
 "ethers-solc",
]

[[package]]
name = "ethers-addressbook"
version = "2.0.14"
source = "registry+https://github.com/rust-lang/crates.io-index"
checksum = "5495afd16b4faa556c3bba1f21b98b4983e53c1755022377051a975c3b021759"
dependencies = [
 "ethers-core",
 "once_cell",
 "serde",
 "serde_json",
]

[[package]]
name = "ethers-contract"
version = "2.0.14"
source = "registry+https://github.com/rust-lang/crates.io-index"
checksum = "6fceafa3578c836eeb874af87abacfb041f92b4da0a78a5edd042564b8ecdaaa"
dependencies = [
 "const-hex",
 "ethers-contract-abigen",
 "ethers-contract-derive",
 "ethers-core",
 "ethers-providers",
 "futures-util",
 "once_cell",
 "pin-project",
 "serde",
 "serde_json",
 "thiserror",
]

[[package]]
name = "ethers-contract-abigen"
version = "2.0.14"
source = "registry+https://github.com/rust-lang/crates.io-index"
checksum = "04ba01fbc2331a38c429eb95d4a570166781f14290ef9fdb144278a90b5a739b"
dependencies = [
 "Inflector",
 "const-hex",
 "dunce",
 "ethers-core",
 "ethers-etherscan",
 "eyre",
 "prettyplease 0.2.17",
 "proc-macro2 1.0.79",
 "quote 1.0.35",
 "regex",
 "reqwest",
 "serde",
 "serde_json",
 "syn 2.0.57",
 "toml 0.8.12",
 "walkdir",
]

[[package]]
name = "ethers-contract-derive"
version = "2.0.14"
source = "registry+https://github.com/rust-lang/crates.io-index"
checksum = "87689dcabc0051cde10caaade298f9e9093d65f6125c14575db3fd8c669a168f"
dependencies = [
 "Inflector",
 "const-hex",
 "ethers-contract-abigen",
 "ethers-core",
 "proc-macro2 1.0.79",
 "quote 1.0.35",
 "serde_json",
 "syn 2.0.57",
]

[[package]]
name = "ethers-core"
version = "2.0.14"
source = "registry+https://github.com/rust-lang/crates.io-index"
checksum = "82d80cc6ad30b14a48ab786523af33b37f28a8623fc06afd55324816ef18fb1f"
dependencies = [
 "arrayvec 0.7.4",
 "bytes",
 "cargo_metadata 0.18.1",
 "chrono",
 "const-hex",
 "elliptic-curve 0.13.8",
 "ethabi",
 "generic-array 0.14.7",
 "k256 0.13.3",
 "num_enum 0.7.2",
 "once_cell",
 "open-fastrlp",
 "rand 0.8.5",
 "rlp",
 "serde",
 "serde_json",
 "strum 0.26.2",
 "syn 2.0.57",
 "tempfile",
 "thiserror",
 "tiny-keccak",
 "unicode-xid 0.2.4",
]

[[package]]
name = "ethers-etherscan"
version = "2.0.14"
source = "registry+https://github.com/rust-lang/crates.io-index"
checksum = "e79e5973c26d4baf0ce55520bd732314328cabe53193286671b47144145b9649"
dependencies = [
 "chrono",
 "ethers-core",
 "reqwest",
 "semver 1.0.22",
 "serde",
 "serde_json",
 "thiserror",
 "tracing",
]

[[package]]
name = "ethers-middleware"
version = "2.0.14"
source = "registry+https://github.com/rust-lang/crates.io-index"
checksum = "48f9fdf09aec667c099909d91908d5eaf9be1bd0e2500ba4172c1d28bfaa43de"
dependencies = [
 "async-trait",
 "auto_impl",
 "ethers-contract",
 "ethers-core",
 "ethers-etherscan",
 "ethers-providers",
 "ethers-signers",
 "futures-channel",
 "futures-locks",
 "futures-util",
 "instant",
 "reqwest",
 "serde",
 "serde_json",
 "thiserror",
 "tokio",
 "tracing",
 "tracing-futures",
 "url",
]

[[package]]
name = "ethers-providers"
version = "2.0.14"
source = "registry+https://github.com/rust-lang/crates.io-index"
checksum = "6434c9a33891f1effc9c75472e12666db2fa5a0fec4b29af6221680a6fe83ab2"
dependencies = [
 "async-trait",
 "auto_impl",
 "base64 0.21.7",
 "bytes",
 "const-hex",
 "enr",
 "ethers-core",
 "futures-channel",
 "futures-core",
 "futures-timer",
 "futures-util",
 "hashers",
 "http",
 "instant",
 "jsonwebtoken",
 "once_cell",
 "pin-project",
 "reqwest",
 "serde",
 "serde_json",
 "thiserror",
 "tokio",
 "tokio-tungstenite",
 "tracing",
 "tracing-futures",
 "url",
 "wasm-bindgen",
 "wasm-bindgen-futures",
 "web-sys",
 "winapi",
 "ws_stream_wasm",
]

[[package]]
name = "ethers-signers"
version = "2.0.14"
source = "registry+https://github.com/rust-lang/crates.io-index"
checksum = "228875491c782ad851773b652dd8ecac62cda8571d3bc32a5853644dd26766c2"
dependencies = [
 "async-trait",
 "coins-bip32",
 "coins-bip39",
 "coins-ledger",
 "const-hex",
 "elliptic-curve 0.13.8",
 "eth-keystore",
 "ethers-core",
 "futures-executor",
 "futures-util",
 "home",
 "rand 0.8.5",
 "rusoto_core",
 "rusoto_kms",
 "semver 1.0.22",
 "sha2 0.10.8",
 "spki 0.7.3",
 "thiserror",
 "tracing",
 "trezor-client",
]

[[package]]
name = "ethers-solc"
version = "2.0.14"
source = "registry+https://github.com/rust-lang/crates.io-index"
checksum = "66244a771d9163282646dbeffe0e6eca4dda4146b6498644e678ac6089b11edd"
dependencies = [
 "cfg-if 1.0.0",
 "const-hex",
 "dirs 5.0.1",
 "dunce",
 "ethers-core",
 "glob",
 "home",
 "md-5 0.10.6",
 "num_cpus",
 "once_cell",
 "path-slash",
 "rayon",
 "regex",
 "semver 1.0.22",
 "serde",
 "serde_json",
 "solang-parser",
 "svm-rs 0.3.5",
 "thiserror",
 "tiny-keccak",
 "tokio",
 "tracing",
 "walkdir",
 "yansi 0.5.1",
]

[[package]]
name = "ethnum"
version = "1.5.0"
source = "registry+https://github.com/rust-lang/crates.io-index"
checksum = "b90ca2580b73ab6a1f724b76ca11ab632df820fd6040c336200d2c1df7b3c82c"

[[package]]
name = "event-listener"
version = "2.5.3"
source = "registry+https://github.com/rust-lang/crates.io-index"
checksum = "0206175f82b8d6bf6652ff7d71a1e27fd2e4efde587fd368662814d6ec1d9ce0"

[[package]]
name = "event-listener"
version = "4.0.3"
source = "registry+https://github.com/rust-lang/crates.io-index"
checksum = "67b215c49b2b248c855fb73579eb1f4f26c38ffdc12973e20e07b91d78d5646e"
dependencies = [
 "concurrent-queue",
 "parking",
 "pin-project-lite",
]

[[package]]
name = "event-listener"
version = "5.2.0"
source = "registry+https://github.com/rust-lang/crates.io-index"
checksum = "2b5fb89194fa3cad959b833185b3063ba881dbfc7030680b314250779fb4cc91"
dependencies = [
 "concurrent-queue",
 "parking",
 "pin-project-lite",
]

[[package]]
name = "event-listener-strategy"
version = "0.4.0"
source = "registry+https://github.com/rust-lang/crates.io-index"
checksum = "958e4d70b6d5e81971bebec42271ec641e7ff4e170a6fa605f2b8a8b65cb97d3"
dependencies = [
 "event-listener 4.0.3",
 "pin-project-lite",
]

[[package]]
name = "event-listener-strategy"
version = "0.5.0"
source = "registry+https://github.com/rust-lang/crates.io-index"
checksum = "feedafcaa9b749175d5ac357452a9d41ea2911da598fde46ce1fe02c37751291"
dependencies = [
 "event-listener 5.2.0",
 "pin-project-lite",
]

[[package]]
name = "evmole"
version = "0.3.8"
source = "registry+https://github.com/rust-lang/crates.io-index"
checksum = "0ffb1f458e6901be6a6aaa485ce3a5d81478644edde1ffbe95da114ad9c94467"
dependencies = [
 "ruint",
]

[[package]]
name = "expect-test"
version = "1.4.1"
source = "registry+https://github.com/rust-lang/crates.io-index"
checksum = "30d9eafeadd538e68fb28016364c9732d78e420b9ff8853fa5e4058861e9f8d3"
dependencies = [
 "dissimilar",
 "once_cell",
]

[[package]]
name = "eyre"
version = "0.6.12"
source = "registry+https://github.com/rust-lang/crates.io-index"
checksum = "7cd915d99f24784cdc19fd37ef22b97e3ff0ae756c7e492e9fbfe897d61e2aec"
dependencies = [
 "indenter",
 "once_cell",
]

[[package]]
name = "fail"
version = "0.4.0"
source = "registry+https://github.com/rust-lang/crates.io-index"
checksum = "3be3c61c59fdc91f5dbc3ea31ee8623122ce80057058be560654c5d410d181a6"
dependencies = [
 "lazy_static",
 "log",
 "rand 0.7.3",
]

[[package]]
name = "fastcrypto"
version = "0.1.5"
source = "git+https://github.com/MystenLabs/fastcrypto?rev=c961a01596a87e76f590c7e43aca9d57106dbbb1#c961a01596a87e76f590c7e43aca9d57106dbbb1"
dependencies = [
 "aes",
 "aes-gcm",
 "ark-ec",
 "ark-ff 0.4.2",
 "ark-secp256r1",
 "ark-serialize 0.4.2",
 "auto_ops",
 "base64ct",
 "bincode",
 "blake2",
 "blake3",
 "blst",
 "bs58 0.4.0",
 "bulletproofs",
 "cbc",
 "ctr",
 "curve25519-dalek-ng",
 "derive_more",
 "digest 0.10.7",
 "ecdsa 0.16.9",
 "ed25519-consensus",
 "elliptic-curve 0.13.8",
 "eyre",
 "fastcrypto-derive",
 "generic-array 0.14.7",
 "hex",
 "hkdf",
 "lazy_static",
 "merlin",
 "once_cell",
 "p256",
 "rand 0.8.5",
 "readonly",
 "rfc6979 0.4.0",
 "rsa",
 "schemars",
 "secp256k1 0.27.0",
 "serde",
 "serde_bytes",
 "serde_with",
 "sha2 0.10.8",
 "sha3 0.10.8",
 "signature 2.2.0",
 "static_assertions",
 "thiserror",
 "tokio",
 "typenum",
 "zeroize",
]

[[package]]
name = "fastcrypto-derive"
version = "0.1.3"
source = "git+https://github.com/MystenLabs/fastcrypto?rev=c961a01596a87e76f590c7e43aca9d57106dbbb1#c961a01596a87e76f590c7e43aca9d57106dbbb1"
dependencies = [
 "convert_case 0.6.0",
 "proc-macro2 1.0.79",
 "quote 1.0.35",
 "syn 1.0.109",
]

[[package]]
name = "fastcrypto-zkp"
version = "0.1.1"
source = "git+https://github.com/MystenLabs/fastcrypto?rev=c961a01596a87e76f590c7e43aca9d57106dbbb1#c961a01596a87e76f590c7e43aca9d57106dbbb1"
dependencies = [
 "ark-bls12-381",
 "ark-bn254",
 "ark-crypto-primitives",
 "ark-ec",
 "ark-ff 0.4.2",
 "ark-groth16",
 "ark-relations",
 "ark-serialize 0.4.2",
 "blst",
 "byte-slice-cast",
 "derive_more",
 "fastcrypto",
 "num-bigint 0.4.4",
 "once_cell",
 "poseidon-ark",
 "regex",
 "schemars",
 "serde",
 "serde_json",
]

[[package]]
name = "fastrand"
version = "1.9.0"
source = "registry+https://github.com/rust-lang/crates.io-index"
checksum = "e51093e27b0797c359783294ca4f0a911c270184cb10f85783b118614a1501be"
dependencies = [
 "instant",
]

[[package]]
name = "fastrand"
version = "2.0.2"
source = "registry+https://github.com/rust-lang/crates.io-index"
checksum = "658bd65b1cf4c852a3cc96f18a8ce7b5640f6b703f905c7d74532294c2a63984"

[[package]]
name = "fastrlp"
version = "0.3.1"
source = "registry+https://github.com/rust-lang/crates.io-index"
checksum = "139834ddba373bbdd213dffe02c8d110508dcf1726c2be27e8d1f7d7e1856418"
dependencies = [
 "arrayvec 0.7.4",
 "auto_impl",
 "bytes",
]

[[package]]
name = "fd-lock"
version = "3.0.13"
source = "registry+https://github.com/rust-lang/crates.io-index"
checksum = "ef033ed5e9bad94e55838ca0ca906db0e043f517adda0c8b79c7a8c66c93c1b5"
dependencies = [
 "cfg-if 1.0.0",
 "rustix 0.38.32",
 "windows-sys 0.48.0",
]

[[package]]
name = "fdlimit"
version = "0.2.1"
source = "registry+https://github.com/rust-lang/crates.io-index"
checksum = "2c4c9e43643f5a3be4ca5b67d26b98031ff9db6806c3440ae32e02e3ceac3f1b"
dependencies = [
 "libc",
]

[[package]]
name = "ff"
version = "0.12.1"
source = "registry+https://github.com/rust-lang/crates.io-index"
checksum = "d013fc25338cc558c5c2cfbad646908fb23591e2404481826742b651c9af7160"
dependencies = [
 "rand_core 0.6.4",
 "subtle",
]

[[package]]
name = "ff"
version = "0.13.0"
source = "registry+https://github.com/rust-lang/crates.io-index"
checksum = "ded41244b729663b1e574f1b4fb731469f69f79c17667b5d776b16cda0479449"
dependencies = [
 "rand_core 0.6.4",
 "subtle",
]

[[package]]
name = "fiat-crypto"
version = "0.1.20"
source = "registry+https://github.com/rust-lang/crates.io-index"
checksum = "e825f6987101665dea6ec934c09ec6d721de7bc1bf92248e1d5810c8cd636b77"

[[package]]
name = "figment"
version = "0.10.15"
source = "registry+https://github.com/rust-lang/crates.io-index"
checksum = "7270677e7067213e04f323b55084586195f18308cd7546cfac9f873344ccceb6"
dependencies = [
 "atomic",
 "pear",
 "serde",
 "toml 0.8.12",
 "uncased",
 "version_check",
]

[[package]]
name = "findshlibs"
version = "0.10.2"
source = "registry+https://github.com/rust-lang/crates.io-index"
checksum = "40b9e59cd0f7e0806cca4be089683ecb6434e602038df21fe6bf6711b2f07f64"
dependencies = [
 "cc",
 "lazy_static",
 "libc",
 "winapi",
]

[[package]]
name = "fixed-hash"
version = "0.7.0"
source = "registry+https://github.com/rust-lang/crates.io-index"
checksum = "cfcf0ed7fe52a17a03854ec54a9f76d6d84508d1c0e66bc1793301c73fc8493c"
dependencies = [
 "byteorder",
 "rand 0.8.5",
 "rustc-hex",
 "static_assertions",
]

[[package]]
name = "fixed-hash"
version = "0.8.0"
source = "registry+https://github.com/rust-lang/crates.io-index"
checksum = "835c052cb0c08c1acf6ffd71c022172e18723949c8282f2b9f27efbc51e64534"
dependencies = [
 "arbitrary",
 "byteorder",
 "rand 0.8.5",
 "rustc-hex",
 "static_assertions",
]

[[package]]
name = "fixedbitset"
version = "0.2.0"
source = "registry+https://github.com/rust-lang/crates.io-index"
checksum = "37ab347416e802de484e4d03c7316c48f1ecb56574dfd4a46a80f173ce1de04d"

[[package]]
name = "fixedbitset"
version = "0.4.2"
source = "registry+https://github.com/rust-lang/crates.io-index"
checksum = "0ce7134b9999ecaf8bcd65542e436736ef32ddca1b3e06094cb6ec5755203b80"

[[package]]
name = "flate2"
version = "1.0.28"
source = "registry+https://github.com/rust-lang/crates.io-index"
checksum = "46303f565772937ffe1d394a4fac6f411c6013172fadde9dcdb1e147a086940e"
dependencies = [
 "crc32fast",
 "miniz_oxide 0.7.2",
]

[[package]]
name = "float-cmp"
version = "0.9.0"
source = "registry+https://github.com/rust-lang/crates.io-index"
checksum = "98de4bbd547a563b716d8dfa9aad1cb19bfab00f4fa09a6a4ed21dbcf44ce9c4"
dependencies = [
 "num-traits",
]

[[package]]
name = "fnv"
version = "1.0.7"
source = "registry+https://github.com/rust-lang/crates.io-index"
checksum = "3f9eec918d3f24069decb9af1554cad7c880e2da24a9afd88aca000531ab82c1"

[[package]]
name = "foreign-types"
version = "0.3.2"
source = "registry+https://github.com/rust-lang/crates.io-index"
checksum = "f6f339eb8adc052cd2ca78910fda869aefa38d22d5cb648e6485e4d3fc06f3b1"
dependencies = [
 "foreign-types-shared",
]

[[package]]
name = "foreign-types-shared"
version = "0.1.1"
source = "registry+https://github.com/rust-lang/crates.io-index"
checksum = "00b0228411908ca8685dba7fc2cdd70ec9990a6e753e89b6ac91a84c40fbaf4b"

[[package]]
name = "form_urlencoded"
version = "1.2.1"
source = "registry+https://github.com/rust-lang/crates.io-index"
checksum = "e13624c2627564efccf4934284bdd98cbaa14e79b0b5a141218e507b3a823456"
dependencies = [
 "percent-encoding",
]

[[package]]
name = "foundry-block-explorers"
version = "0.2.3"
source = "registry+https://github.com/rust-lang/crates.io-index"
checksum = "5a056d4aa33a639c0aa1e9e473c25b9b191be30cbea94b31445fac5c272418ae"
dependencies = [
 "alloy-chains",
 "alloy-json-abi",
 "alloy-primitives",
 "foundry-compilers",
 "reqwest",
 "semver 1.0.22",
 "serde",
 "serde_json",
 "thiserror",
 "tracing",
]

[[package]]
name = "foundry-cheatcodes"
version = "0.2.0"
source = "git+https://github.com/foundry-rs/foundry.git?rev=617dfc28#617dfc28cb8206a0003edcf73a6f1058adaef740"
dependencies = [
 "alloy-dyn-abi",
 "alloy-genesis",
 "alloy-json-abi",
 "alloy-primitives",
 "alloy-providers",
 "alloy-rpc-types",
 "alloy-signer",
 "alloy-sol-types",
 "base64 0.22.0",
 "const-hex",
 "dialoguer",
 "eyre",
 "foundry-cheatcodes-spec",
 "foundry-common",
 "foundry-compilers",
 "foundry-config",
 "foundry-evm-core",
 "foundry-wallets",
 "itertools 0.12.1",
 "jsonpath_lib",
 "k256 0.13.3",
 "p256",
 "parking_lot 0.12.1",
 "revm 7.2.0",
 "rustc-hash",
 "serde_json",
 "thiserror",
 "toml 0.8.12",
 "tracing",
 "walkdir",
]

[[package]]
name = "foundry-cheatcodes-spec"
version = "0.2.0"
source = "git+https://github.com/foundry-rs/foundry.git?rev=617dfc28#617dfc28cb8206a0003edcf73a6f1058adaef740"
dependencies = [
 "alloy-sol-types",
 "foundry-macros",
 "serde",
]

[[package]]
name = "foundry-common"
version = "0.2.0"
source = "git+https://github.com/foundry-rs/foundry.git?rev=617dfc28#617dfc28cb8206a0003edcf73a6f1058adaef740"
dependencies = [
 "alloy-dyn-abi",
 "alloy-json-abi",
 "alloy-json-rpc",
 "alloy-primitives",
 "alloy-providers",
 "alloy-pubsub",
 "alloy-rpc-client",
 "alloy-rpc-types",
 "alloy-signer",
 "alloy-sol-types",
 "alloy-transport",
 "alloy-transport-http",
 "alloy-transport-ipc",
 "alloy-transport-ws",
 "async-trait",
 "clap 4.5.4",
 "comfy-table 7.1.0",
 "const-hex",
 "dunce",
 "ethers-core",
 "ethers-middleware",
 "ethers-providers",
 "ethers-signers",
 "eyre",
 "foundry-block-explorers",
 "foundry-compilers",
 "foundry-config",
 "glob",
 "globset",
 "once_cell",
 "rand 0.8.5",
 "reqwest",
 "rustc-hash",
 "semver 1.0.22",
 "serde",
 "serde_json",
 "tempfile",
 "thiserror",
 "tokio",
 "tower",
 "tracing",
 "url",
 "walkdir",
 "yansi 0.5.1",
]

[[package]]
name = "foundry-compilers"
version = "0.3.13"
source = "registry+https://github.com/rust-lang/crates.io-index"
checksum = "079ada1a2093e0fec67caa15ccf018a2d1b5747c16ba1c11a28df53530eb1a5f"
dependencies = [
 "alloy-json-abi",
 "alloy-primitives",
 "cfg-if 1.0.0",
 "dirs 5.0.1",
 "dunce",
 "home",
 "itertools 0.12.1",
 "md-5 0.10.6",
 "memmap2 0.9.4",
 "once_cell",
 "path-slash",
 "rayon",
 "regex",
 "semver 1.0.22",
 "serde",
 "serde_json",
 "sha2 0.10.8",
 "solang-parser",
 "svm-rs 0.4.1",
 "svm-rs-builds",
 "thiserror",
 "tokio",
 "tracing",
 "walkdir",
 "yansi 0.5.1",
]

[[package]]
name = "foundry-config"
version = "0.2.0"
source = "git+https://github.com/foundry-rs/foundry.git?rev=617dfc28#617dfc28cb8206a0003edcf73a6f1058adaef740"
dependencies = [
 "Inflector",
 "alloy-chains",
 "alloy-primitives",
 "dirs-next",
 "dunce",
 "eyre",
 "figment",
 "foundry-block-explorers",
 "foundry-compilers",
 "globset",
 "number_prefix",
 "once_cell",
 "path-slash",
 "regex",
 "reqwest",
 "revm-primitives 3.1.0",
 "semver 1.0.22",
 "serde",
 "serde_json",
 "serde_regex",
 "solang-parser",
 "thiserror",
 "toml 0.8.12",
 "toml_edit 0.21.1",
 "tracing",
 "walkdir",
]

[[package]]
name = "foundry-evm-core"
version = "0.2.0"
source = "git+https://github.com/foundry-rs/foundry.git?rev=617dfc28#617dfc28cb8206a0003edcf73a6f1058adaef740"
dependencies = [
 "alloy-dyn-abi",
 "alloy-genesis",
 "alloy-json-abi",
 "alloy-primitives",
 "alloy-providers",
 "alloy-rpc-types",
 "alloy-sol-types",
 "arrayvec 0.7.4",
 "auto_impl",
 "const-hex",
 "derive_more",
 "eyre",
 "foundry-cheatcodes-spec",
 "foundry-common",
 "foundry-compilers",
 "foundry-config",
 "foundry-macros",
 "futures",
 "itertools 0.12.1",
 "once_cell",
 "parking_lot 0.12.1",
 "revm 7.2.0",
 "revm-inspectors",
 "rustc-hash",
 "serde",
 "serde_json",
 "thiserror",
 "tokio",
 "tracing",
 "url",
]

[[package]]
name = "foundry-macros"
version = "0.2.0"
source = "git+https://github.com/foundry-rs/foundry.git?rev=617dfc28#617dfc28cb8206a0003edcf73a6f1058adaef740"
dependencies = [
 "proc-macro-error",
 "proc-macro2 1.0.79",
 "quote 1.0.35",
 "syn 2.0.57",
]

[[package]]
name = "foundry-wallets"
version = "0.2.0"
source = "git+https://github.com/foundry-rs/foundry.git?rev=617dfc28#617dfc28cb8206a0003edcf73a6f1058adaef740"
dependencies = [
 "alloy-primitives",
 "async-trait",
 "clap 4.5.4",
 "const-hex",
 "derive_builder 0.20.0",
 "ethers-core",
 "ethers-providers",
 "ethers-signers",
 "eyre",
 "foundry-common",
 "foundry-config",
 "itertools 0.12.1",
 "rpassword",
 "rusoto_core",
 "rusoto_kms",
 "serde",
 "thiserror",
 "tracing",
]

[[package]]
name = "fragile"
version = "2.0.0"
source = "registry+https://github.com/rust-lang/crates.io-index"
checksum = "6c2141d6d6c8512188a7891b4b01590a45f6dac67afb4f255c4124dbb86d4eaa"

[[package]]
name = "fs2"
version = "0.4.3"
source = "registry+https://github.com/rust-lang/crates.io-index"
checksum = "9564fc758e15025b46aa6643b1b77d047d1a56a1aea6e01002ac0c7026876213"
dependencies = [
 "libc",
 "winapi",
]

[[package]]
name = "fs4"
version = "0.7.0"
source = "registry+https://github.com/rust-lang/crates.io-index"
checksum = "29f9df8a11882c4e3335eb2d18a0137c505d9ca927470b0cac9c6f0ae07d28f7"
dependencies = [
 "rustix 0.38.32",
 "windows-sys 0.48.0",
]

[[package]]
name = "fs_extra"
version = "1.3.0"
source = "registry+https://github.com/rust-lang/crates.io-index"
checksum = "42703706b716c37f96a77aea830392ad231f44c9e9a67872fa5548707e11b11c"

[[package]]
name = "fuchsia-cprng"
version = "0.1.1"
source = "registry+https://github.com/rust-lang/crates.io-index"
checksum = "a06f77d526c1a601b7c4cdd98f54b5eaabffc14d5f2f0296febdc7f357c6d3ba"

[[package]]
name = "funty"
version = "1.1.0"
source = "registry+https://github.com/rust-lang/crates.io-index"
checksum = "fed34cd105917e91daa4da6b3728c47b068749d6a62c59811f06ed2ac71d9da7"

[[package]]
name = "funty"
version = "2.0.0"
source = "registry+https://github.com/rust-lang/crates.io-index"
checksum = "e6d5a32815ae3f33302d95fdcb2ce17862f8c65363dcfd29360480ba1001fc9c"

[[package]]
name = "futures"
version = "0.3.30"
source = "registry+https://github.com/rust-lang/crates.io-index"
checksum = "645c6916888f6cb6350d2550b80fb63e734897a8498abe35cfb732b6487804b0"
dependencies = [
 "futures-channel",
 "futures-core",
 "futures-executor",
 "futures-io",
 "futures-sink",
 "futures-task",
 "futures-util",
]

[[package]]
name = "futures-channel"
version = "0.3.30"
source = "registry+https://github.com/rust-lang/crates.io-index"
checksum = "eac8f7d7865dcb88bd4373ab671c8cf4508703796caa2b1985a9ca867b3fcb78"
dependencies = [
 "futures-core",
 "futures-sink",
]

[[package]]
name = "futures-core"
version = "0.3.30"
source = "registry+https://github.com/rust-lang/crates.io-index"
checksum = "dfc6580bb841c5a68e9ef15c77ccc837b40a7504914d52e47b8b0e9bbda25a1d"

[[package]]
name = "futures-executor"
version = "0.3.30"
source = "registry+https://github.com/rust-lang/crates.io-index"
checksum = "a576fc72ae164fca6b9db127eaa9a9dda0d61316034f33a0a0d4eda41f02b01d"
dependencies = [
 "futures-core",
 "futures-task",
 "futures-util",
]

[[package]]
name = "futures-io"
version = "0.3.30"
source = "registry+https://github.com/rust-lang/crates.io-index"
checksum = "a44623e20b9681a318efdd71c299b6b222ed6f231972bfe2f224ebad6311f0c1"

[[package]]
name = "futures-lite"
version = "1.13.0"
source = "registry+https://github.com/rust-lang/crates.io-index"
checksum = "49a9d51ce47660b1e808d3c990b4709f2f415d928835a17dfd16991515c46bce"
dependencies = [
 "fastrand 1.9.0",
 "futures-core",
 "futures-io",
 "memchr",
 "parking",
 "pin-project-lite",
 "waker-fn",
]

[[package]]
name = "futures-lite"
version = "2.3.0"
source = "registry+https://github.com/rust-lang/crates.io-index"
checksum = "52527eb5074e35e9339c6b4e8d12600c7128b68fb25dcb9fa9dec18f7c25f3a5"
dependencies = [
 "futures-core",
 "pin-project-lite",
]

[[package]]
name = "futures-locks"
version = "0.7.1"
source = "registry+https://github.com/rust-lang/crates.io-index"
checksum = "45ec6fe3675af967e67c5536c0b9d44e34e6c52f86bedc4ea49c5317b8e94d06"
dependencies = [
 "futures-channel",
 "futures-task",
]

[[package]]
name = "futures-macro"
version = "0.3.30"
source = "registry+https://github.com/rust-lang/crates.io-index"
checksum = "87750cf4b7a4c0625b1529e4c543c2182106e4dedc60a2a6455e00d212c489ac"
dependencies = [
 "proc-macro2 1.0.79",
 "quote 1.0.35",
 "syn 2.0.57",
]

[[package]]
name = "futures-sink"
version = "0.3.30"
source = "registry+https://github.com/rust-lang/crates.io-index"
checksum = "9fb8e00e87438d937621c1c6269e53f536c14d3fbd6a042bb24879e57d474fb5"

[[package]]
name = "futures-task"
version = "0.3.30"
source = "registry+https://github.com/rust-lang/crates.io-index"
checksum = "38d84fa142264698cdce1a9f9172cf383a0c82de1bddcf3092901442c4097004"

[[package]]
name = "futures-timer"
version = "3.0.3"
source = "registry+https://github.com/rust-lang/crates.io-index"
checksum = "f288b0a4f20f9a56b5d1da57e2227c661b7b16168e2f72365f57b63326e29b24"
dependencies = [
 "gloo-timers",
 "send_wrapper 0.4.0",
]

[[package]]
name = "futures-util"
version = "0.3.30"
source = "registry+https://github.com/rust-lang/crates.io-index"
checksum = "3d6401deb83407ab3da39eba7e33987a73c3df0c82b4bb5813ee871c19c41d48"
dependencies = [
 "futures-channel",
 "futures-core",
 "futures-io",
 "futures-macro",
 "futures-sink",
 "futures-task",
 "memchr",
 "pin-project-lite",
 "pin-utils",
 "slab",
]

[[package]]
name = "fxhash"
version = "0.2.1"
source = "registry+https://github.com/rust-lang/crates.io-index"
checksum = "c31b6d751ae2c7f11320402d34e41349dd1016f8d5d45e48c4312bc8625af50c"
dependencies = [
 "byteorder",
]

[[package]]
name = "gcc"
version = "0.3.55"
source = "registry+https://github.com/rust-lang/crates.io-index"
checksum = "8f5f3913fa0bfe7ee1fd8248b6b9f42a5af4b9d65ec2dd2c3c26132b950ecfc2"

[[package]]
name = "generic-array"
version = "0.12.4"
source = "registry+https://github.com/rust-lang/crates.io-index"
checksum = "ffdf9f34f1447443d37393cc6c2b8313aebddcd96906caf34e54c68d8e57d7bd"
dependencies = [
 "typenum",
]

[[package]]
name = "generic-array"
version = "0.13.3"
source = "registry+https://github.com/rust-lang/crates.io-index"
checksum = "f797e67af32588215eaaab8327027ee8e71b9dd0b2b26996aedf20c030fce309"
dependencies = [
 "typenum",
]

[[package]]
name = "generic-array"
version = "0.14.7"
source = "registry+https://github.com/rust-lang/crates.io-index"
checksum = "85649ca51fd72272d7821adaf274ad91c288277713d9c18820d8499a7ff69e9a"
dependencies = [
 "serde",
 "typenum",
 "version_check",
 "zeroize",
]

[[package]]
name = "getrandom"
version = "0.1.16"
source = "registry+https://github.com/rust-lang/crates.io-index"
checksum = "8fc3cb4d91f53b50155bdcfd23f6a4c39ae1969c2ae85982b135750cccaf5fce"
dependencies = [
 "cfg-if 1.0.0",
 "libc",
 "wasi 0.9.0+wasi-snapshot-preview1",
]

[[package]]
name = "getrandom"
version = "0.2.12"
source = "registry+https://github.com/rust-lang/crates.io-index"
checksum = "190092ea657667030ac6a35e305e62fc4dd69fd98ac98631e5d3a2b1575a12b5"
dependencies = [
 "cfg-if 1.0.0",
 "js-sys",
 "libc",
 "wasi 0.11.0+wasi-snapshot-preview1",
 "wasm-bindgen",
]

[[package]]
name = "ghash"
version = "0.5.1"
source = "registry+https://github.com/rust-lang/crates.io-index"
checksum = "f0d8a4362ccb29cb0b265253fb0a2728f592895ee6854fd9bc13f2ffda266ff1"
dependencies = [
 "opaque-debug",
 "polyval",
]

[[package]]
name = "gimli"
version = "0.27.3"
source = "registry+https://github.com/rust-lang/crates.io-index"
checksum = "b6c80984affa11d98d1b88b66ac8853f143217b399d3c74116778ff8fdb4ed2e"

[[package]]
name = "gimli"
version = "0.28.1"
source = "registry+https://github.com/rust-lang/crates.io-index"
checksum = "4271d37baee1b8c7e4b708028c57d816cf9d2434acb33a549475f78c181f6253"

[[package]]
name = "git-version"
version = "0.3.9"
source = "registry+https://github.com/rust-lang/crates.io-index"
checksum = "1ad568aa3db0fcbc81f2f116137f263d7304f512a1209b35b85150d3ef88ad19"
dependencies = [
 "git-version-macro",
]

[[package]]
name = "git-version-macro"
version = "0.3.9"
source = "registry+https://github.com/rust-lang/crates.io-index"
checksum = "53010ccb100b96a67bc32c0175f0ed1426b31b655d562898e57325f81c023ac0"
dependencies = [
 "proc-macro2 1.0.79",
 "quote 1.0.35",
 "syn 2.0.57",
]

[[package]]
name = "glob"
version = "0.3.1"
source = "registry+https://github.com/rust-lang/crates.io-index"
checksum = "d2fabcfbdc87f4758337ca535fb41a6d701b65693ce38287d856d1674551ec9b"

[[package]]
name = "globset"
version = "0.4.14"
source = "registry+https://github.com/rust-lang/crates.io-index"
checksum = "57da3b9b5b85bd66f31093f8c408b90a74431672542466497dcbdfdc02034be1"
dependencies = [
 "aho-corasick 1.1.3",
 "bstr",
 "log",
 "regex-automata 0.4.6",
 "regex-syntax 0.8.3",
]

[[package]]
name = "globwalk"
version = "0.8.1"
source = "registry+https://github.com/rust-lang/crates.io-index"
checksum = "93e3af942408868f6934a7b85134a3230832b9977cf66125df2f9edcfce4ddcc"
dependencies = [
 "bitflags 1.3.2",
 "ignore",
 "walkdir",
]

[[package]]
name = "gloo-net"
version = "0.2.6"
source = "registry+https://github.com/rust-lang/crates.io-index"
checksum = "9902a044653b26b99f7e3693a42f171312d9be8b26b5697bd1e43ad1f8a35e10"
dependencies = [
 "futures-channel",
 "futures-core",
 "futures-sink",
 "gloo-utils",
 "js-sys",
 "pin-project",
 "serde",
 "serde_json",
 "thiserror",
 "wasm-bindgen",
 "wasm-bindgen-futures",
 "web-sys",
]

[[package]]
name = "gloo-timers"
version = "0.2.6"
source = "registry+https://github.com/rust-lang/crates.io-index"
checksum = "9b995a66bb87bebce9a0f4a95aed01daca4872c050bfcb21653361c03bc35e5c"
dependencies = [
 "futures-channel",
 "futures-core",
 "js-sys",
 "wasm-bindgen",
]

[[package]]
name = "gloo-utils"
version = "0.1.7"
source = "registry+https://github.com/rust-lang/crates.io-index"
checksum = "037fcb07216cb3a30f7292bd0176b050b7b9a052ba830ef7d5d65f6dc64ba58e"
dependencies = [
 "js-sys",
 "serde",
 "serde_json",
 "wasm-bindgen",
 "web-sys",
]

[[package]]
name = "governor"
version = "0.5.1"
source = "registry+https://github.com/rust-lang/crates.io-index"
checksum = "c390a940a5d157878dd057c78680a33ce3415bcd05b4799509ea44210914b4d5"
dependencies = [
 "cfg-if 1.0.0",
 "dashmap",
 "futures",
 "futures-timer",
 "no-std-compat",
 "nonzero_ext",
 "parking_lot 0.12.1",
 "quanta",
 "rand 0.8.5",
 "smallvec",
]

[[package]]
name = "group"
version = "0.12.1"
source = "registry+https://github.com/rust-lang/crates.io-index"
checksum = "5dfbfb3a6cfbd390d5c9564ab283a0349b9b9fcd46a706c1eb10e0db70bfbac7"
dependencies = [
 "ff 0.12.1",
 "rand_core 0.6.4",
 "subtle",
]

[[package]]
name = "group"
version = "0.13.0"
source = "registry+https://github.com/rust-lang/crates.io-index"
checksum = "f0f9ef7462f7c099f518d754361858f86d8a07af53ba9af0fe635bbccb151a63"
dependencies = [
 "ff 0.13.0",
 "rand_core 0.6.4",
 "subtle",
]

[[package]]
name = "guppy"
version = "0.15.2"
source = "registry+https://github.com/rust-lang/crates.io-index"
checksum = "f822a2716041492e071691606474f5a7e4fa7c2acbfd7da7b29884fb448291c7"
dependencies = [
 "camino",
 "cargo_metadata 0.15.4",
 "cfg-if 1.0.0",
 "debug-ignore",
 "fixedbitset 0.4.2",
 "guppy-summaries",
 "guppy-workspace-hack",
 "indexmap 1.9.3",
 "itertools 0.10.5",
 "nested",
 "once_cell",
 "pathdiff",
 "petgraph 0.6.4",
 "rayon",
 "semver 1.0.22",
 "serde",
 "serde_json",
 "smallvec",
 "static_assertions",
 "target-spec",
 "toml 0.5.11",
]

[[package]]
name = "guppy-summaries"
version = "0.7.1"
source = "registry+https://github.com/rust-lang/crates.io-index"
checksum = "8bd039b8f587513b48754811cfa37c2ba079df537b490b602fa641ce18f6e72a"
dependencies = [
 "camino",
 "cfg-if 1.0.0",
 "diffus",
 "guppy-workspace-hack",
 "semver 1.0.22",
 "serde",
 "toml 0.5.11",
]

[[package]]
name = "guppy-workspace-hack"
version = "0.1.0"
source = "registry+https://github.com/rust-lang/crates.io-index"
checksum = "92620684d99f750bae383ecb3be3748142d6095760afd5cbcf2261e9a279d780"

[[package]]
name = "h2"
version = "0.3.25"
source = "registry+https://github.com/rust-lang/crates.io-index"
checksum = "4fbd2820c5e49886948654ab546d0688ff24530286bdcf8fca3cefb16d4618eb"
dependencies = [
 "bytes",
 "fnv",
 "futures-core",
 "futures-sink",
 "futures-util",
 "http",
 "indexmap 2.2.6",
 "slab",
 "tokio",
 "tokio-util 0.7.10",
 "tracing",
]

[[package]]
name = "hakari"
version = "0.13.2"
source = "registry+https://github.com/rust-lang/crates.io-index"
checksum = "2af0223111b69beda15417ad6a960bffb093c916f0eaa559036c7036efa2d199"
dependencies = [
 "atomicwrites",
 "bimap",
 "camino",
 "cfg-if 1.0.0",
 "debug-ignore",
 "diffy",
 "guppy",
 "guppy-workspace-hack",
 "include_dir",
 "indenter",
 "itertools 0.10.5",
 "owo-colors",
 "pathdiff",
 "rayon",
 "serde",
 "tabular",
 "target-spec",
 "toml 0.5.11",
 "toml_edit 0.17.1",
 "twox-hash",
]

[[package]]
name = "half"
version = "1.8.3"
source = "registry+https://github.com/rust-lang/crates.io-index"
checksum = "1b43ede17f21864e81be2fa654110bf1e793774238d86ef8555c37e6519c0403"

[[package]]
name = "half"
version = "2.4.0"
source = "registry+https://github.com/rust-lang/crates.io-index"
checksum = "b5eceaaeec696539ddaf7b333340f1af35a5aa87ae3e4f3ead0532f72affab2e"
dependencies = [
 "cfg-if 1.0.0",
 "crunchy",
]

[[package]]
name = "handlebars"
version = "4.5.0"
source = "registry+https://github.com/rust-lang/crates.io-index"
checksum = "faa67bab9ff362228eb3d00bd024a4965d8231bbb7921167f0cfa66c6626b225"
dependencies = [
 "log",
 "pest",
 "pest_derive",
 "serde",
 "serde_json",
 "thiserror",
]

[[package]]
name = "hash32"
version = "0.1.1"
source = "registry+https://github.com/rust-lang/crates.io-index"
checksum = "d4041af86e63ac4298ce40e5cca669066e75b6f1aa3390fe2561ffa5e1d9f4cc"
dependencies = [
 "byteorder",
]

[[package]]
name = "hashbrown"
version = "0.12.3"
source = "registry+https://github.com/rust-lang/crates.io-index"
checksum = "8a9ee70c43aaf417c914396645a0fa852624801b24ebb7ae78fe8272889ac888"
dependencies = [
 "ahash 0.7.8",
]

[[package]]
name = "hashbrown"
version = "0.13.2"
source = "registry+https://github.com/rust-lang/crates.io-index"
checksum = "43a3c133739dddd0d2990f9a4bdf8eb4b21ef50e4851ca85ab661199821d510e"
dependencies = [
 "ahash 0.8.11",
 "serde",
]

[[package]]
name = "hashbrown"
version = "0.14.3"
source = "registry+https://github.com/rust-lang/crates.io-index"
checksum = "290f1a1d9242c78d09ce40a5e87e7554ee637af1351968159f4952f028f75604"
dependencies = [
 "ahash 0.8.11",
 "allocator-api2",
 "serde",
]

[[package]]
name = "hashers"
version = "1.0.1"
source = "registry+https://github.com/rust-lang/crates.io-index"
checksum = "b2bca93b15ea5a746f220e56587f71e73c6165eab783df9e26590069953e3c30"
dependencies = [
 "fxhash",
]

[[package]]
name = "hdrhistogram"
version = "7.5.4"
source = "registry+https://github.com/rust-lang/crates.io-index"
checksum = "765c9198f173dd59ce26ff9f95ef0aafd0a0fe01fb9d72841bc5066a4c06511d"
dependencies = [
 "base64 0.21.7",
 "byteorder",
 "crossbeam-channel",
 "flate2",
 "nom 7.1.3",
 "num-traits",
]

[[package]]
name = "headers"
version = "0.3.9"
source = "registry+https://github.com/rust-lang/crates.io-index"
checksum = "06683b93020a07e3dbcf5f8c0f6d40080d725bea7936fc01ad345c01b97dc270"
dependencies = [
 "base64 0.21.7",
 "bytes",
 "headers-core",
 "http",
 "httpdate",
 "mime",
 "sha1",
]

[[package]]
name = "headers-core"
version = "0.2.0"
source = "registry+https://github.com/rust-lang/crates.io-index"
checksum = "e7f66481bfee273957b1f20485a4ff3362987f85b2c236580d81b4eb7a326429"
dependencies = [
 "http",
]

[[package]]
name = "heapless"
version = "0.5.6"
source = "registry+https://github.com/rust-lang/crates.io-index"
checksum = "74911a68a1658cfcfb61bc0ccfbd536e3b6e906f8c2f7883ee50157e3e2184f1"
dependencies = [
 "as-slice",
 "generic-array 0.13.3",
 "hash32",
 "stable_deref_trait",
]

[[package]]
name = "heck"
version = "0.3.3"
source = "registry+https://github.com/rust-lang/crates.io-index"
checksum = "6d621efb26863f0e9924c6ac577e8275e5e6b77455db64ffa6c65c904e9e132c"
dependencies = [
 "unicode-segmentation",
]

[[package]]
name = "heck"
version = "0.4.1"
source = "registry+https://github.com/rust-lang/crates.io-index"
checksum = "95505c38b4572b2d910cecb0281560f54b440a19336cbbcb27bf6ce6adc6f5a8"

[[package]]
name = "heck"
version = "0.5.0"
source = "registry+https://github.com/rust-lang/crates.io-index"
checksum = "2304e00983f87ffb38b55b444b5e3b60a884b5d30c0fca7d82fe33449bbe55ea"

[[package]]
name = "hermit-abi"
version = "0.1.19"
source = "registry+https://github.com/rust-lang/crates.io-index"
checksum = "62b467343b94ba476dcb2500d242dadbb39557df889310ac77c5d99100aaac33"
dependencies = [
 "libc",
]

[[package]]
name = "hermit-abi"
version = "0.3.9"
source = "registry+https://github.com/rust-lang/crates.io-index"
checksum = "d231dfb89cfffdbc30e7fc41579ed6066ad03abda9e567ccafae602b97ec5024"

[[package]]
name = "hex"
version = "0.4.3"
source = "registry+https://github.com/rust-lang/crates.io-index"
checksum = "7f24254aa9a54b5c858eaee2f5bccdb46aaf0e486a595ed5fd8f86ba55232a70"
dependencies = [
 "serde",
]

[[package]]
name = "hex-literal"
version = "0.3.4"
source = "registry+https://github.com/rust-lang/crates.io-index"
checksum = "7ebdb29d2ea9ed0083cd8cece49bbd968021bd99b0849edb4a9a7ee0fdf6a4e0"

[[package]]
name = "hex-literal"
version = "0.4.1"
source = "registry+https://github.com/rust-lang/crates.io-index"
checksum = "6fe2267d4ed49bc07b63801559be28c718ea06c4738b7a03c94df7386d2cde46"

[[package]]
name = "hidapi-rusb"
version = "1.3.3"
source = "registry+https://github.com/rust-lang/crates.io-index"
checksum = "efdc2ec354929a6e8f3c6b6923a4d97427ec2f764cfee8cd4bfe890946cdf08b"
dependencies = [
 "cc",
 "libc",
 "pkg-config",
 "rusb",
]

[[package]]
name = "hkdf"
version = "0.12.4"
source = "registry+https://github.com/rust-lang/crates.io-index"
checksum = "7b5f8eb2ad728638ea2c7d47a21db23b7b58a72ed6a38256b8a1849f15fbbdf7"
dependencies = [
 "hmac 0.12.1",
]

[[package]]
name = "hmac"
version = "0.11.0"
source = "registry+https://github.com/rust-lang/crates.io-index"
checksum = "2a2a2320eb7ec0ebe8da8f744d7812d9fc4cb4d09344ac01898dbcb6a20ae69b"
dependencies = [
 "crypto-mac",
 "digest 0.9.0",
]

[[package]]
name = "hmac"
version = "0.12.1"
source = "registry+https://github.com/rust-lang/crates.io-index"
checksum = "6c49c37c09c17a53d937dfbb742eb3a961d65a994e6bcdcf37e7399d0cc8ab5e"
dependencies = [
 "digest 0.10.7",
]

[[package]]
name = "hmac-sha512"
version = "0.1.9"
source = "registry+https://github.com/rust-lang/crates.io-index"
checksum = "77e806677ce663d0a199541030c816847b36e8dc095f70dae4a4f4ad63da5383"

[[package]]
name = "home"
version = "0.5.9"
source = "registry+https://github.com/rust-lang/crates.io-index"
checksum = "e3d1354bf6b7235cb4a0576c2619fd4ed18183f689b12b006a0ee7329eeff9a5"
dependencies = [
 "windows-sys 0.52.0",
]

[[package]]
name = "hostname"
version = "0.3.1"
source = "registry+https://github.com/rust-lang/crates.io-index"
checksum = "3c731c3e10504cc8ed35cfe2f1db4c9274c3d35fa486e3b31df46f068ef3e867"
dependencies = [
 "libc",
 "match_cfg",
 "winapi",
]

[[package]]
name = "http"
version = "0.2.12"
source = "registry+https://github.com/rust-lang/crates.io-index"
checksum = "601cbb57e577e2f5ef5be8e7b83f0f63994f25aa94d673e54a92d5c516d101f1"
dependencies = [
 "bytes",
 "fnv",
 "itoa",
]

[[package]]
name = "http-body"
version = "0.4.6"
source = "registry+https://github.com/rust-lang/crates.io-index"
checksum = "7ceab25649e9960c0311ea418d17bee82c0dcec1bd053b5f9a66e265a693bed2"
dependencies = [
 "bytes",
 "http",
 "pin-project-lite",
]

[[package]]
name = "http-range-header"
version = "0.3.1"
source = "registry+https://github.com/rust-lang/crates.io-index"
checksum = "add0ab9360ddbd88cfeb3bd9574a1d85cfdfa14db10b3e21d3700dbc4328758f"

[[package]]
name = "httparse"
version = "1.8.0"
source = "registry+https://github.com/rust-lang/crates.io-index"
checksum = "d897f394bad6a705d5f4104762e116a75639e470d80901eed05a860a95cb1904"

[[package]]
name = "httpdate"
version = "1.0.3"
source = "registry+https://github.com/rust-lang/crates.io-index"
checksum = "df3b46402a9d5adb4c86a0cf463f42e19994e3ee891101b1841f30a545cb49a9"

[[package]]
name = "humansize"
version = "1.1.1"
source = "registry+https://github.com/rust-lang/crates.io-index"
checksum = "02296996cb8796d7c6e3bc2d9211b7802812d36999a51bb754123ead7d37d026"

[[package]]
name = "humansize"
version = "2.1.3"
source = "registry+https://github.com/rust-lang/crates.io-index"
checksum = "6cb51c9a029ddc91b07a787f1d86b53ccfa49b0e86688c946ebe8d3555685dd7"
dependencies = [
 "libm",
]

[[package]]
name = "humantime"
version = "2.1.0"
source = "registry+https://github.com/rust-lang/crates.io-index"
checksum = "9a3a5bfb195931eeb336b2a7b4d761daec841b97f947d34394601737a7bba5e4"

[[package]]
name = "hyper"
version = "0.14.28"
source = "registry+https://github.com/rust-lang/crates.io-index"
checksum = "bf96e135eb83a2a8ddf766e426a841d8ddd7449d5f00d34ea02b41d2f19eef80"
dependencies = [
 "bytes",
 "futures-channel",
 "futures-core",
 "futures-util",
 "h2",
 "http",
 "http-body",
 "httparse",
 "httpdate",
 "itoa",
 "pin-project-lite",
 "socket2 0.5.6",
 "tokio",
 "tower-service",
 "tracing",
 "want",
]

[[package]]
name = "hyper-rustls"
version = "0.23.2"
source = "registry+https://github.com/rust-lang/crates.io-index"
checksum = "1788965e61b367cd03a62950836d5cd41560c3577d90e40e0819373194d1661c"
dependencies = [
 "http",
 "hyper",
 "log",
 "rustls 0.20.9",
 "rustls-native-certs",
 "tokio",
 "tokio-rustls 0.23.4",
 "webpki-roots 0.22.6",
]

[[package]]
name = "hyper-rustls"
version = "0.24.2"
source = "registry+https://github.com/rust-lang/crates.io-index"
checksum = "ec3efd23720e2049821a693cbc7e65ea87c72f1c58ff2f9522ff332b1491e590"
dependencies = [
 "futures-util",
 "http",
 "hyper",
 "log",
 "rustls 0.21.10",
 "rustls-native-certs",
 "tokio",
 "tokio-rustls 0.24.1",
]

[[package]]
name = "hyper-timeout"
version = "0.4.1"
source = "registry+https://github.com/rust-lang/crates.io-index"
checksum = "bbb958482e8c7be4bc3cf272a766a2b0bf1a6755e7a6ae777f017a31d11b13b1"
dependencies = [
 "hyper",
 "pin-project-lite",
 "tokio",
 "tokio-io-timeout",
]

[[package]]
name = "hyper-tls"
version = "0.5.0"
source = "registry+https://github.com/rust-lang/crates.io-index"
checksum = "d6183ddfa99b85da61a140bea0efc93fdf56ceaa041b37d553518030827f9905"
dependencies = [
 "bytes",
 "hyper",
 "native-tls",
 "tokio",
 "tokio-native-tls",
]

[[package]]
name = "iana-time-zone"
version = "0.1.60"
source = "registry+https://github.com/rust-lang/crates.io-index"
checksum = "e7ffbb5a1b541ea2561f8c41c087286cc091e21e556a4f09a8f6cbf17b69b141"
dependencies = [
 "android_system_properties",
 "core-foundation-sys",
 "iana-time-zone-haiku",
 "js-sys",
 "wasm-bindgen",
 "windows-core 0.52.0",
]

[[package]]
name = "iana-time-zone-haiku"
version = "0.1.2"
source = "registry+https://github.com/rust-lang/crates.io-index"
checksum = "f31827a206f56af32e590ba56d5d2d085f558508192593743f16b2306495269f"
dependencies = [
 "cc",
]

[[package]]
name = "ident_case"
version = "1.0.1"
source = "registry+https://github.com/rust-lang/crates.io-index"
checksum = "b9e0384b61958566e926dc50660321d12159025e767c18e043daf26b70104c39"

[[package]]
name = "idna"
version = "0.3.0"
source = "registry+https://github.com/rust-lang/crates.io-index"
checksum = "e14ddfc70884202db2244c223200c204c2bda1bc6e0998d11b5e024d657209e6"
dependencies = [
 "unicode-bidi",
 "unicode-normalization",
]

[[package]]
name = "idna"
version = "0.5.0"
source = "registry+https://github.com/rust-lang/crates.io-index"
checksum = "634d9b1461af396cad843f47fdba5597a4f9e6ddd4bfb6ff5d85028c25cb12f6"
dependencies = [
 "unicode-bidi",
 "unicode-normalization",
]

[[package]]
name = "if_chain"
version = "1.0.2"
source = "registry+https://github.com/rust-lang/crates.io-index"
checksum = "cb56e1aa765b4b4f3aadfab769793b7087bb03a4ea4920644a6d238e2df5b9ed"

[[package]]
name = "ignore"
version = "0.4.22"
source = "registry+https://github.com/rust-lang/crates.io-index"
checksum = "b46810df39e66e925525d6e38ce1e7f6e1d208f72dc39757880fcb66e2c58af1"
dependencies = [
 "crossbeam-deque",
 "globset",
 "log",
 "memchr",
 "regex-automata 0.4.6",
 "same-file",
 "walkdir",
 "winapi-util",
]

[[package]]
name = "im"
version = "15.1.0"
source = "registry+https://github.com/rust-lang/crates.io-index"
checksum = "d0acd33ff0285af998aaf9b57342af478078f53492322fafc47450e09397e0e9"
dependencies = [
 "bitmaps",
 "rand_core 0.6.4",
 "rand_xoshiro",
 "sized-chunks",
 "typenum",
 "version_check",
]

[[package]]
name = "impl-codec"
version = "0.5.1"
source = "registry+https://github.com/rust-lang/crates.io-index"
checksum = "161ebdfec3c8e3b52bf61c4f3550a1eea4f9579d10dc1b936f3171ebdcd6c443"
dependencies = [
 "parity-scale-codec 2.3.1",
]

[[package]]
name = "impl-codec"
version = "0.6.0"
source = "registry+https://github.com/rust-lang/crates.io-index"
checksum = "ba6a270039626615617f3f36d15fc827041df3b78c439da2cadfa47455a77f2f"
dependencies = [
 "parity-scale-codec 3.6.9",
]

[[package]]
name = "impl-rlp"
version = "0.3.0"
source = "registry+https://github.com/rust-lang/crates.io-index"
checksum = "f28220f89297a075ddc7245cd538076ee98b01f2a9c23a53a4f1105d5a322808"
dependencies = [
 "rlp",
]

[[package]]
name = "impl-serde"
version = "0.3.2"
source = "registry+https://github.com/rust-lang/crates.io-index"
checksum = "4551f042f3438e64dbd6226b20527fc84a6e1fe65688b58746a2f53623f25f5c"
dependencies = [
 "serde",
]

[[package]]
name = "impl-serde"
version = "0.4.0"
source = "registry+https://github.com/rust-lang/crates.io-index"
checksum = "ebc88fc67028ae3db0c853baa36269d398d5f45b6982f95549ff5def78c935cd"
dependencies = [
 "serde",
]

[[package]]
name = "impl-trait-for-tuples"
version = "0.2.2"
source = "registry+https://github.com/rust-lang/crates.io-index"
checksum = "11d7a9f6330b71fea57921c9b61c47ee6e84f72d394754eff6163ae67e7395eb"
dependencies = [
 "proc-macro2 1.0.79",
 "quote 1.0.35",
 "syn 1.0.109",
]

[[package]]
name = "include_dir"
version = "0.7.3"
source = "registry+https://github.com/rust-lang/crates.io-index"
checksum = "18762faeff7122e89e0857b02f7ce6fcc0d101d5e9ad2ad7846cc01d61b7f19e"
dependencies = [
 "glob",
 "include_dir_macros",
]

[[package]]
name = "include_dir_macros"
version = "0.7.3"
source = "registry+https://github.com/rust-lang/crates.io-index"
checksum = "b139284b5cf57ecfa712bcc66950bb635b31aff41c188e8a4cfc758eca374a3f"
dependencies = [
 "proc-macro2 1.0.79",
 "quote 1.0.35",
]

[[package]]
name = "indenter"
version = "0.3.3"
source = "registry+https://github.com/rust-lang/crates.io-index"
checksum = "ce23b50ad8242c51a442f3ff322d56b02f08852c77e4c0b4d3fd684abc89c683"

[[package]]
name = "indexmap"
version = "1.9.3"
source = "registry+https://github.com/rust-lang/crates.io-index"
checksum = "bd070e393353796e801d209ad339e89596eb4c8d430d18ede6a1cced8fafbd99"
dependencies = [
 "autocfg",
 "hashbrown 0.12.3",
 "serde",
]

[[package]]
name = "indexmap"
version = "2.2.6"
source = "registry+https://github.com/rust-lang/crates.io-index"
checksum = "168fb715dda47215e360912c096649d23d58bf392ac62f73919e831745e40f26"
dependencies = [
 "equivalent",
 "hashbrown 0.14.3",
]

[[package]]
name = "indicatif"
version = "0.17.8"
source = "registry+https://github.com/rust-lang/crates.io-index"
checksum = "763a5a8f45087d6bcea4222e7b72c291a054edf80e4ef6efd2a4979878c7bea3"
dependencies = [
 "console",
 "instant",
 "number_prefix",
 "portable-atomic 1.6.0",
 "unicode-width",
]

[[package]]
name = "indoc"
version = "2.0.5"
source = "registry+https://github.com/rust-lang/crates.io-index"
checksum = "b248f5224d1d606005e02c97f5aa4e88eeb230488bcc03bc9ca4d7991399f2b5"

[[package]]
name = "inferno"
version = "0.11.19"
source = "registry+https://github.com/rust-lang/crates.io-index"
checksum = "321f0f839cd44a4686e9504b0a62b4d69a50b62072144c71c68f5873c167b8d9"
dependencies = [
 "ahash 0.8.11",
 "indexmap 2.2.6",
 "is-terminal",
 "itoa",
 "log",
 "num-format",
 "once_cell",
 "quick-xml",
 "rgb",
 "str_stack",
]

[[package]]
name = "inlinable_string"
version = "0.1.15"
source = "registry+https://github.com/rust-lang/crates.io-index"
checksum = "c8fae54786f62fb2918dcfae3d568594e50eb9b5c25bf04371af6fe7516452fb"

[[package]]
name = "inout"
version = "0.1.3"
source = "registry+https://github.com/rust-lang/crates.io-index"
checksum = "a0c10553d664a4d0bcff9f4215d0aac67a639cc68ef660840afe309b807bc9f5"
dependencies = [
 "block-padding 0.3.3",
 "generic-array 0.14.7",
]

[[package]]
name = "inquire"
version = "0.6.2"
source = "registry+https://github.com/rust-lang/crates.io-index"
checksum = "c33e7c1ddeb15c9abcbfef6029d8e29f69b52b6d6c891031b88ed91b5065803b"
dependencies = [
 "bitflags 1.3.2",
 "crossterm 0.25.0",
 "dyn-clone",
 "lazy_static",
 "newline-converter",
 "thiserror",
 "unicode-segmentation",
 "unicode-width",
]

[[package]]
name = "insta"
version = "1.38.0"
source = "registry+https://github.com/rust-lang/crates.io-index"
checksum = "3eab73f58e59ca6526037208f0e98851159ec1633cf17b6cd2e1f2c3fd5d53cc"
dependencies = [
 "console",
 "lazy_static",
 "linked-hash-map",
 "pest",
 "pest_derive",
 "serde",
 "similar",
]

[[package]]
name = "instant"
version = "0.1.12"
source = "registry+https://github.com/rust-lang/crates.io-index"
checksum = "7a5bbe824c507c5da5956355e86a746d82e0e1464f65d862cc5e71da70e94b2c"
dependencies = [
 "cfg-if 1.0.0",
]

[[package]]
name = "integer-encoding"
version = "3.0.4"
source = "registry+https://github.com/rust-lang/crates.io-index"
checksum = "8bb03732005da905c88227371639bf1ad885cc712789c011c31c5fb3ab3ccf02"

[[package]]
name = "internment"
version = "0.5.6"
source = "registry+https://github.com/rust-lang/crates.io-index"
checksum = "6ab388864246d58a276e60e7569a833d9cc4cd75c66e5ca77c177dad38e59996"
dependencies = [
 "ahash 0.7.8",
 "dashmap",
 "hashbrown 0.12.3",
 "once_cell",
 "parking_lot 0.12.1",
]

[[package]]
name = "interprocess"
version = "1.2.1"
source = "registry+https://github.com/rust-lang/crates.io-index"
checksum = "81f2533f3be42fffe3b5e63b71aeca416c1c3bc33e4e27be018521e76b1f38fb"
dependencies = [
 "blocking",
 "cfg-if 1.0.0",
 "futures-core",
 "futures-io",
 "intmap",
 "libc",
 "once_cell",
 "rustc_version 0.4.0",
 "spinning",
 "thiserror",
 "to_method",
 "tokio",
 "winapi",
]

[[package]]
name = "intmap"
version = "0.7.1"
source = "registry+https://github.com/rust-lang/crates.io-index"
checksum = "ae52f28f45ac2bc96edb7714de995cffc174a395fb0abf5bff453587c980d7b9"

[[package]]
name = "inventory"
version = "0.3.15"
source = "registry+https://github.com/rust-lang/crates.io-index"
checksum = "f958d3d68f4167080a18141e10381e7634563984a537f2a49a30fd8e53ac5767"

[[package]]
name = "io-lifetimes"
version = "1.0.11"
source = "registry+https://github.com/rust-lang/crates.io-index"
checksum = "eae7b9aee968036d54dce06cebaefd919e4472e753296daccd6d344e3e2df0c2"
dependencies = [
 "hermit-abi 0.3.9",
 "libc",
 "windows-sys 0.48.0",
]

[[package]]
name = "ipnet"
version = "2.9.0"
source = "registry+https://github.com/rust-lang/crates.io-index"
checksum = "8f518f335dce6725a761382244631d86cf0ccb2863413590b31338feb467f9c3"

[[package]]
name = "iri-string"
version = "0.4.1"
source = "registry+https://github.com/rust-lang/crates.io-index"
checksum = "8f0f7638c1e223529f1bfdc48c8b133b9e0b434094d1d28473161ee48b235f78"
dependencies = [
 "nom 7.1.3",
]

[[package]]
name = "is-terminal"
version = "0.4.12"
source = "registry+https://github.com/rust-lang/crates.io-index"
checksum = "f23ff5ef2b80d608d61efee834934d862cd92461afc0560dedf493e4c033738b"
dependencies = [
 "hermit-abi 0.3.9",
 "libc",
 "windows-sys 0.52.0",
]

[[package]]
name = "itertools"
version = "0.10.5"
source = "registry+https://github.com/rust-lang/crates.io-index"
checksum = "b0fd2260e829bddf4cb6ea802289de2f86d6a7a690192fbe91b3f46e0f2c8473"
dependencies = [
 "either",
]

[[package]]
name = "itertools"
version = "0.11.0"
source = "registry+https://github.com/rust-lang/crates.io-index"
checksum = "b1c173a5686ce8bfa551b3563d0c2170bf24ca44da99c7ca4bfdab5418c3fe57"
dependencies = [
 "either",
]

[[package]]
name = "itertools"
version = "0.12.1"
source = "registry+https://github.com/rust-lang/crates.io-index"
checksum = "ba291022dbbd398a455acf126c1e341954079855bc60dfdda641363bd6922569"
dependencies = [
 "either",
]

[[package]]
name = "itoa"
version = "1.0.11"
source = "registry+https://github.com/rust-lang/crates.io-index"
checksum = "49f1f14873335454500d59611f1cf4a4b0f786f9ac11f4312a78e4cf2566695b"

[[package]]
name = "ityfuzz"
version = "0.1.0"
dependencies = [
 "alloy-dyn-abi",
 "alloy-json-abi",
 "alloy-primitives",
 "alloy-sol-types",
 "anyhow",
 "bytes",
 "clap 4.5.4",
 "colored",
 "criterion",
 "either",
 "ethers",
 "evmole",
 "foundry-cheatcodes",
 "glob",
 "handlebars",
 "hex",
 "itertools 0.10.5",
 "lazy_static",
 "libafl",
 "libafl_bolts",
 "move-binary-format",
 "move-core-types",
 "move-stdlib",
 "move-vm-runtime",
 "move-vm-types",
 "nix 0.27.1",
 "num_cpus",
 "once_cell",
 "permutator",
 "primitive-types 0.12.2",
 "rand 0.8.5",
 "regex",
 "reqwest",
 "retry",
 "revm 3.3.0",
 "revm-interpreter 1.1.2",
 "revm-primitives 1.1.2",
 "rlp",
 "rust-crypto",
 "semver 1.0.22",
 "sentry",
 "serde",
 "serde_cbor",
 "serde_json",
 "serde_traitobject",
 "sui-move-natives-latest",
 "sui-protocol-config",
 "sui-types",
 "thiserror",
 "tracing",
 "tracing-subscriber 0.3.18",
 "typetag",
 "z3",
 "z3-sys",
]

[[package]]
name = "jemalloc-ctl"
version = "0.5.4"
source = "registry+https://github.com/rust-lang/crates.io-index"
checksum = "7cffc705424a344c054e135d12ee591402f4539245e8bbd64e6c9eaa9458b63c"
dependencies = [
 "jemalloc-sys",
 "libc",
 "paste",
]

[[package]]
name = "jemalloc-sys"
version = "0.5.4****.0-patched"
source = "registry+https://github.com/rust-lang/crates.io-index"
checksum = "ac6c1946e1cea1788cbfde01c993b52a10e2da07f4bac608228d1bed20bfebf2"
dependencies = [
 "cc",
 "libc",
]

[[package]]
name = "jobserver"
version = "0.1.28"
source = "registry+https://github.com/rust-lang/crates.io-index"
checksum = "ab46a6e9526ddef3ae7f787c06f0f2600639ba80ea3eade3d8e670a2230f51d6"
dependencies = [
 "libc",
]

[[package]]
name = "js-sys"
version = "0.3.69"
source = "registry+https://github.com/rust-lang/crates.io-index"
checksum = "29c15563dc2726973df627357ce0c9ddddbea194836909d655df6a75d2cf296d"
dependencies = [
 "wasm-bindgen",
]

[[package]]
name = "json_to_table"
version = "0.6.0"
source = "git+https://github.com/zhiburt/tabled/?rev=e449317a1c02eb6b29e409ad6617e5d9eb7b3bd4#e449317a1c02eb6b29e409ad6617e5d9eb7b3bd4"
dependencies = [
 "serde_json",
 "tabled",
]

[[package]]
name = "jsonpath_lib"
version = "0.3.0"
source = "registry+https://github.com/rust-lang/crates.io-index"
checksum = "eaa63191d68230cccb81c5aa23abd53ed64d83337cacbb25a7b8c7979523774f"
dependencies = [
 "log",
 "serde",
 "serde_json",
]

[[package]]
name = "jsonrpsee"
version = "0.16.2"
source = "git+https://github.com/wlmyng/jsonrpsee.git?rev=b1b300784795f6a64d0fcdf8f03081a9bc38bde8#b1b300784795f6a64d0fcdf8f03081a9bc38bde8"
dependencies = [
 "jsonrpsee-client-transport",
 "jsonrpsee-core",
 "jsonrpsee-http-client",
 "jsonrpsee-proc-macros",
 "jsonrpsee-server",
 "jsonrpsee-types",
 "jsonrpsee-wasm-client",
 "jsonrpsee-ws-client",
 "tracing",
]

[[package]]
name = "jsonrpsee-client-transport"
version = "0.16.2"
source = "git+https://github.com/wlmyng/jsonrpsee.git?rev=b1b300784795f6a64d0fcdf8f03081a9bc38bde8#b1b300784795f6a64d0fcdf8f03081a9bc38bde8"
dependencies = [
 "anyhow",
 "futures-channel",
 "futures-timer",
 "futures-util",
 "gloo-net",
 "http",
 "jsonrpsee-core",
 "jsonrpsee-types",
 "pin-project",
 "rustls-native-certs",
 "soketto",
 "thiserror",
 "tokio",
 "tokio-rustls 0.23.4",
 "tokio-util 0.7.10",
 "tracing",
 "webpki-roots 0.22.6",
]

[[package]]
name = "jsonrpsee-core"
version = "0.16.2"
source = "git+https://github.com/wlmyng/jsonrpsee.git?rev=b1b300784795f6a64d0fcdf8f03081a9bc38bde8#b1b300784795f6a64d0fcdf8f03081a9bc38bde8"
dependencies = [
 "anyhow",
 "arrayvec 0.7.4",
 "async-lock 2.8.0",
 "async-trait",
 "beef",
 "futures-channel",
 "futures-timer",
 "futures-util",
 "globset",
 "hyper",
 "jsonrpsee-types",
 "parking_lot 0.12.1",
 "rand 0.8.5",
 "rustc-hash",
 "serde",
 "serde_json",
 "soketto",
 "thiserror",
 "tokio",
 "tracing",
 "wasm-bindgen-futures",
]

[[package]]
name = "jsonrpsee-http-client"
version = "0.16.2"
source = "git+https://github.com/wlmyng/jsonrpsee.git?rev=b1b300784795f6a64d0fcdf8f03081a9bc38bde8#b1b300784795f6a64d0fcdf8f03081a9bc38bde8"
dependencies = [
 "async-trait",
 "hyper",
 "hyper-rustls 0.23.2",
 "jsonrpsee-core",
 "jsonrpsee-types",
 "rustc-hash",
 "serde",
 "serde_json",
 "thiserror",
 "tokio",
 "tracing",
]

[[package]]
name = "jsonrpsee-proc-macros"
version = "0.16.2"
source = "git+https://github.com/wlmyng/jsonrpsee.git?rev=b1b300784795f6a64d0fcdf8f03081a9bc38bde8#b1b300784795f6a64d0fcdf8f03081a9bc38bde8"
dependencies = [
 "heck 0.4.1",
 "proc-macro-crate 1.1.3",
 "proc-macro2 1.0.79",
 "quote 1.0.35",
 "syn 1.0.109",
]

[[package]]
name = "jsonrpsee-server"
version = "0.16.2"
source = "git+https://github.com/wlmyng/jsonrpsee.git?rev=b1b300784795f6a64d0fcdf8f03081a9bc38bde8#b1b300784795f6a64d0fcdf8f03081a9bc38bde8"
dependencies = [
 "futures-channel",
 "futures-util",
 "http",
 "hyper",
 "jsonrpsee-core",
 "jsonrpsee-types",
 "serde",
 "serde_json",
 "soketto",
 "tokio",
 "tokio-stream",
 "tokio-util 0.7.10",
 "tower",
 "tracing",
]

[[package]]
name = "jsonrpsee-types"
version = "0.16.2"
source = "git+https://github.com/wlmyng/jsonrpsee.git?rev=b1b300784795f6a64d0fcdf8f03081a9bc38bde8#b1b300784795f6a64d0fcdf8f03081a9bc38bde8"
dependencies = [
 "anyhow",
 "beef",
 "serde",
 "serde_json",
 "thiserror",
 "tracing",
]

[[package]]
name = "jsonrpsee-wasm-client"
version = "0.16.2"
source = "git+https://github.com/wlmyng/jsonrpsee.git?rev=b1b300784795f6a64d0fcdf8f03081a9bc38bde8#b1b300784795f6a64d0fcdf8f03081a9bc38bde8"
dependencies = [
 "jsonrpsee-client-transport",
 "jsonrpsee-core",
 "jsonrpsee-types",
]

[[package]]
name = "jsonrpsee-ws-client"
version = "0.16.2"
source = "git+https://github.com/wlmyng/jsonrpsee.git?rev=b1b300784795f6a64d0fcdf8f03081a9bc38bde8#b1b300784795f6a64d0fcdf8f03081a9bc38bde8"
dependencies = [
 "http",
 "jsonrpsee-client-transport",
 "jsonrpsee-core",
 "jsonrpsee-types",
]

[[package]]
name = "jsonwebtoken"
version = "8.3.0"
source = "registry+https://github.com/rust-lang/crates.io-index"
checksum = "6971da4d9c3aa03c3d8f3ff0f4155b534aad021292003895a469716b2a230378"
dependencies = [
 "base64 0.21.7",
 "pem",
 "ring 0.16.20",
 "serde",
 "serde_json",
 "simple_asn1",
]

[[package]]
name = "k256"
version = "0.11.6"
source = "registry+https://github.com/rust-lang/crates.io-index"
checksum = "72c1e0b51e7ec0a97369623508396067a486bd0cbed95a2659a4b863d28cfc8b"
dependencies = [
 "cfg-if 1.0.0",
 "ecdsa 0.14.8",
 "elliptic-curve 0.12.3",
 "sha2 0.10.8",
 "sha3 0.10.8",
]

[[package]]
name = "k256"
version = "0.13.3"
source = "registry+https://github.com/rust-lang/crates.io-index"
checksum = "956ff9b67e26e1a6a866cb758f12c6f8746208489e3e4a4b5580802f2f0a587b"
dependencies = [
 "cfg-if 1.0.0",
 "ecdsa 0.16.9",
 "elliptic-curve 0.13.8",
 "once_cell",
 "sha2 0.10.8",
 "signature 2.2.0",
]

[[package]]
name = "keccak"
version = "0.1.5"
source = "registry+https://github.com/rust-lang/crates.io-index"
checksum = "ecc2af9a1119c51f12a14607e783cb977bde58bc069ff0c3da1095e635d70654"
dependencies = [
 "cpufeatures",
]

[[package]]
name = "keccak-asm"
version = "0.1.0"
source = "registry+https://github.com/rust-lang/crates.io-index"
checksum = "bb8515fff80ed850aea4a1595f2e519c003e2a00a82fe168ebf5269196caf444"
dependencies = [
 "digest 0.10.7",
 "sha3-asm",
]

[[package]]
name = "lalrpop"
version = "0.20.2"
source = "registry+https://github.com/rust-lang/crates.io-index"
checksum = "55cb077ad656299f160924eb2912aa147d7339ea7d69e1b5517326fdcec3c1ca"
dependencies = [
 "ascii-canvas",
 "bit-set",
 "ena",
 "itertools 0.11.0",
 "lalrpop-util",
 "petgraph 0.6.4",
 "regex",
 "regex-syntax 0.8.3",
 "string_cache",
 "term",
 "tiny-keccak",
 "unicode-xid 0.2.4",
 "walkdir",
]

[[package]]
name = "lalrpop-util"
version = "0.20.2"
source = "registry+https://github.com/rust-lang/crates.io-index"
checksum = "507460a910eb7b32ee961886ff48539633b788a36b65692b95f225b844c82553"
dependencies = [
 "regex-automata 0.4.6",
]

[[package]]
name = "lazy_static"
version = "1.4.0"
source = "registry+https://github.com/rust-lang/crates.io-index"
checksum = "e2abad23fbc42b3700f2f279844dc832adb2b2eb069b2df918f455c4e18cc646"
dependencies = [
 "spin 0.5.2",
]

[[package]]
name = "lazycell"
version = "1.3.0"
source = "registry+https://github.com/rust-lang/crates.io-index"
checksum = "830d08ce1d1d941e6b30645f1a0eb5643013d835ce3779a5fc208261dbe10f55"

[[package]]
name = "leb128"
version = "0.2.5"
source = "registry+https://github.com/rust-lang/crates.io-index"
checksum = "884e2677b40cc8c339eaefcb701c32ef1fd2493d71118dc0ca4b6a736c93bd67"

[[package]]
name = "libafl"
version = "0.11.2"
source = "registry+https://github.com/rust-lang/crates.io-index"
checksum = "a820f5d1bc8e52c719c023bd9b89b93e399cf3164f0a9876391e9d4d2c987c58"
dependencies = [
 "ahash 0.8.11",
 "backtrace",
 "bincode",
 "c2rust-bitfields",
 "crossterm 0.27.0",
 "hashbrown 0.14.3",
 "libafl_bolts",
 "libafl_derive",
 "libc",
 "libm",
 "log",
 "meminterval",
 "nix 0.26.4",
 "num-traits",
 "postcard",
 "ratatui",
 "regex",
 "rustversion",
 "serde",
 "serde_json",
 "serial_test",
 "tuple_list",
 "typed-builder",
 "uuid 1.8.0",
 "wait-timeout",
 "windows",
]

[[package]]
name = "libafl_bolts"
version = "0.11.2"
source = "registry+https://github.com/rust-lang/crates.io-index"
checksum = "043cd82830f9df1775d97f53c508cf6b4957c88a973da00e09f1e72219ed07f2"
dependencies = [
 "ahash 0.8.11",
 "backtrace",
 "ctor 0.2.7",
 "erased-serde 0.3.31",
 "hashbrown 0.14.3",
 "hostname",
 "libafl_derive",
 "libc",
 "log",
 "miniz_oxide 0.7.2",
 "nix 0.26.4",
 "num_enum 0.7.2",
 "postcard",
 "rand_core 0.6.4",
 "rustversion",
 "serde",
 "serde_json",
 "serial_test",
 "tuple_list",
 "uds",
 "uuid 1.8.0",
 "windows",
 "xxhash-rust",
]

[[package]]
name = "libafl_derive"
version = "0.11.2"
source = "registry+https://github.com/rust-lang/crates.io-index"
checksum = "5c849878dcc707721a16fd5ec2af449301c1da5e3f31682593d60f5e6ccb7568"
dependencies = [
 "quote 1.0.35",
 "syn 2.0.57",
]

[[package]]
name = "libc"
version = "0.2.153"
source = "registry+https://github.com/rust-lang/crates.io-index"
checksum = "9c198f91728a82281a64e1f4f9eeb25d82cb32a5de251c6bd1b5154d63a8e7bd"

[[package]]
name = "libloading"
version = "0.7.4"
source = "registry+https://github.com/rust-lang/crates.io-index"
checksum = "b67380fd3b2fbe7527a606e18729d21c6f3951633d0500574c4dc22d2d638b9f"
dependencies = [
 "cfg-if 1.0.0",
 "winapi",
]

[[package]]
name = "libloading"
version = "0.8.3"
source = "registry+https://github.com/rust-lang/crates.io-index"
checksum = "0c2a198fb6b0eada2a8df47933734e6d35d350665a33a3593d7164fa52c75c19"
dependencies = [
 "cfg-if 1.0.0",
 "windows-targets 0.52.4",
]

[[package]]
name = "libm"
version = "0.2.8"
source = "registry+https://github.com/rust-lang/crates.io-index"
checksum = "4ec2a862134d2a7d32d7983ddcdd1c4923530833c9f2ea1a44fc5fa473989058"

[[package]]
name = "libredox"
version = "0.0.1"
source = "registry+https://github.com/rust-lang/crates.io-index"
checksum = "85c833ca1e66078851dba29046874e38f08b2c883700aa29a03ddd3b23814ee8"
dependencies = [
 "bitflags 2.5.0",
 "libc",
 "redox_syscall 0.4.1",
]

[[package]]
name = "librocksdb-sys"
version = "0.11.0+8.1.1"
source = "registry+https://github.com/rust-lang/crates.io-index"
checksum = "d3386f101bcb4bd252d8e9d2fb41ec3b0862a15a62b478c355b2982efa469e3e"
dependencies = [
 "bindgen 0.65.1",
 "bzip2-sys",
 "cc",
 "glob",
 "libc",
 "libz-sys",
 "lz4-sys",
 "zstd-sys",
]

[[package]]
name = "libtest-mimic"
version = "0.5.2"
source = "registry+https://github.com/rust-lang/crates.io-index"
checksum = "79529479c298f5af41375b0c1a77ef670d450b4c9cd7949d2b43af08121b20ec"
dependencies = [
 "clap 3.2.25",
 "termcolor",
 "threadpool",
]

[[package]]
name = "libusb1-sys"
version = "0.6.4"
source = "registry+https://github.com/rust-lang/crates.io-index"
checksum = "f9d0e2afce4245f2c9a418511e5af8718bcaf2fa408aefb259504d1a9cb25f27"
dependencies = [
 "cc",
 "libc",
 "pkg-config",
 "vcpkg",
]

[[package]]
name = "libz-sys"
version = "1.1.16"
source = "registry+https://github.com/rust-lang/crates.io-index"
checksum = "5e143b5e666b2695d28f6bca6497720813f699c9602dd7f5cac91008b8ada7f9"
dependencies = [
 "cc",
 "pkg-config",
 "vcpkg",
]

[[package]]
name = "linked-hash-map"
version = "0.5.6"
source = "registry+https://github.com/rust-lang/crates.io-index"
checksum = "0717cef1bc8b636c6e1c1bbdefc09e6322da8a9321966e8928ef80d20f7f770f"

[[package]]
name = "linux-raw-sys"
version = "0.1.4"
source = "registry+https://github.com/rust-lang/crates.io-index"
checksum = "f051f77a7c8e6957c0696eac88f26b0117e54f52d3fc682ab19397a8812846a4"

[[package]]
name = "linux-raw-sys"
version = "0.3.8"
source = "registry+https://github.com/rust-lang/crates.io-index"
checksum = "ef53942eb7bf7ff43a617b3e2c1c4a5ecf5944a7c1bc12d7ee39bbb15e5c1519"

[[package]]
name = "linux-raw-sys"
version = "0.4.13"
source = "registry+https://github.com/rust-lang/crates.io-index"
checksum = "01cda141df6706de531b6c46c3a33ecca755538219bd484262fa09410c13539c"

[[package]]
name = "lock_api"
version = "0.4.11"
source = "registry+https://github.com/rust-lang/crates.io-index"
checksum = "3c168f8615b12bc01f9c17e2eb0cc07dcae1940121185446edc3744920e8ef45"
dependencies = [
 "autocfg",
 "scopeguard",
]

[[package]]
name = "log"
version = "0.4.21"
source = "registry+https://github.com/rust-lang/crates.io-index"
checksum = "90ed8c1e510134f979dbc4f070f87d4313098b704861a105fe34231c70a3901c"
dependencies = [
 "serde",
]

[[package]]
name = "lru"
version = "0.10.1"
source = "registry+https://github.com/rust-lang/crates.io-index"
checksum = "718e8fae447df0c7e1ba7f5189829e63fd536945c8988d61444c19039f16b670"
dependencies = [
 "hashbrown 0.13.2",
]

[[package]]
name = "lru"
version = "0.12.3"
source = "registry+https://github.com/rust-lang/crates.io-index"
checksum = "d3262e75e648fce39813cb56ac41f3c3e3f65217ebf3844d818d1f9398cfb0dc"
dependencies = [
 "hashbrown 0.14.3",
]

[[package]]
name = "lz4-sys"
version = "1.9.4"
source = "registry+https://github.com/rust-lang/crates.io-index"
checksum = "57d27b317e207b10f69f5e75494119e391a96f48861ae870d1da6edac98ca900"
dependencies = [
 "cc",
 "libc",
]

[[package]]
name = "mach"
version = "0.3.2"
source = "registry+https://github.com/rust-lang/crates.io-index"
checksum = "b823e83b2affd8f40a9ee8c29dbc56404c1e34cd2710921f2801e2cf29527afa"
dependencies = [
 "libc",
]

[[package]]
name = "match_cfg"
version = "0.1.0"
source = "registry+https://github.com/rust-lang/crates.io-index"
checksum = "ffbee8634e0d45d258acb448e7eaab3fce7a0a467395d4d9f228e3c1f01fb2e4"

[[package]]
name = "match_opt"
version = "0.1.2"
source = "registry+https://github.com/rust-lang/crates.io-index"
checksum = "405ba1524a1e6ae755334d6966380c60ec40157e0155f9032dd3c294b6384da9"

[[package]]
name = "matchers"
version = "0.1.0"
source = "registry+https://github.com/rust-lang/crates.io-index"
checksum = "8263075bb86c5a1b1427b5ae862e8889656f126e9f77c484496e8b47cf5c5558"
dependencies = [
 "regex-automata 0.1.10",
]

[[package]]
name = "matchit"
version = "0.5.0"
source = "registry+https://github.com/rust-lang/crates.io-index"
checksum = "73cbba799671b762df5a175adf59ce145165747bb891505c43d09aefbbf38beb"

[[package]]
name = "matchit"
version = "0.7.3"
source = "registry+https://github.com/rust-lang/crates.io-index"
checksum = "0e7465ac9959cc2b1404e8e2367b43684a6d13790fe23056cc8c6c5a6b7bcb94"

[[package]]
name = "md-5"
version = "0.9.1"
source = "registry+https://github.com/rust-lang/crates.io-index"
checksum = "7b5a279bb9607f9f53c22d496eade00d138d1bdcccd07d74650387cf94942a15"
dependencies = [
 "block-buffer 0.9.0",
 "digest 0.9.0",
 "opaque-debug",
]

[[package]]
name = "md-5"
version = "0.10.6"
source = "registry+https://github.com/rust-lang/crates.io-index"
checksum = "d89e7ee0cfbedfc4da3340218492196241d89eefb6dab27de5df917a6d2e78cf"
dependencies = [
 "cfg-if 1.0.0",
 "digest 0.10.7",
]

[[package]]
name = "memchr"
version = "2.7.2"
source = "registry+https://github.com/rust-lang/crates.io-index"
checksum = "6c8640c5d730cb13ebd907d8d04b52f55ac9a2eec55b440c8892f40d56c76c1d"

[[package]]
name = "meminterval"
version = "0.4.1"
source = "registry+https://github.com/rust-lang/crates.io-index"
checksum = "c6f8614cf855d251be1c2138d330c04f134923fddec0dcfc8b6f58ac499bf248"
dependencies = [
 "num-traits",
 "serde",
]

[[package]]
name = "memmap2"
version = "0.5.10"
source = "registry+https://github.com/rust-lang/crates.io-index"
checksum = "83faa42c0a078c393f6b29d5db232d8be22776a891f8f56e5284faee4a20b327"
dependencies = [
 "libc",
]

[[package]]
name = "memmap2"
version = "0.9.4"
source = "registry+https://github.com/rust-lang/crates.io-index"
checksum = "fe751422e4a8caa417e13c3ea66452215d7d63e19e604f4980461212f3ae1322"
dependencies = [
 "libc",
]

[[package]]
name = "memoffset"
version = "0.6.5"
source = "registry+https://github.com/rust-lang/crates.io-index"
checksum = "5aa361d4faea93603064a027415f07bd8e1d5c88c9fbf68bf56a285428fd79ce"
dependencies = [
 "autocfg",
]

[[package]]
name = "memoffset"
version = "0.7.1"
source = "registry+https://github.com/rust-lang/crates.io-index"
checksum = "5de893c32cde5f383baa4c04c5d6dbdd735cfd4a794b0debdb2bb1b421da5ff4"
dependencies = [
 "autocfg",
]

[[package]]
name = "merlin"
version = "3.0.0"
source = "registry+https://github.com/rust-lang/crates.io-index"
checksum = "58c38e2799fc0978b65dfff8023ec7843e2330bb462f19198840b34b6582397d"
dependencies = [
 "byteorder",
 "keccak",
 "rand_core 0.6.4",
 "zeroize",
]

[[package]]
name = "metatype"
version = "0.2.1"
source = "registry+https://github.com/rust-lang/crates.io-index"
checksum = "23decce7c32638bcefbd5a5a5d79a5bb5b720c47b82ad5cb670a7eb912705946"

[[package]]
name = "migrations_internals"
version = "2.1.0"
source = "registry+https://github.com/rust-lang/crates.io-index"
checksum = "0f23f71580015254b020e856feac3df5878c2c7a8812297edd6c0a485ac9dada"
dependencies = [
 "serde",
 "toml 0.7.8",
]

[[package]]
name = "migrations_macros"
version = "2.1.0"
source = "registry+https://github.com/rust-lang/crates.io-index"
checksum = "cce3325ac70e67bbab5bd837a31cae01f1a6db64e0e744a33cb03a543469ef08"
dependencies = [
 "migrations_internals",
 "proc-macro2 1.0.79",
 "quote 1.0.35",
]

[[package]]
name = "mime"
version = "0.3.17"
source = "registry+https://github.com/rust-lang/crates.io-index"
checksum = "6877bb514081ee2a7ff5ef9de3281f14a4dd4bceac4c09388074a6b5df8a139a"

[[package]]
name = "mime_guess"
version = "2.0.4"
source = "registry+https://github.com/rust-lang/crates.io-index"
checksum = "4192263c238a5f0d0c6bfd21f336a313a4ce1c450542449ca191bb657b4642ef"
dependencies = [
 "mime",
 "unicase",
]

[[package]]
name = "minimal-lexical"
version = "0.2.1"
source = "registry+https://github.com/rust-lang/crates.io-index"
checksum = "68354c5c6bd36d73ff3feceb05efa59b6acb7626617f4962be322a825e61f79a"

[[package]]
name = "miniz_oxide"
version = "0.6.2"
source = "registry+https://github.com/rust-lang/crates.io-index"
checksum = "b275950c28b37e794e8c55d88aeb5e139d0ce23fdbbeda68f8d7174abdf9e8fa"
dependencies = [
 "adler",
]

[[package]]
name = "miniz_oxide"
version = "0.7.2"
source = "registry+https://github.com/rust-lang/crates.io-index"
checksum = "9d811f3e15f28568be3407c8e7fdb6514c1cda3cb30683f15b6a1a1dc4ea14a7"
dependencies = [
 "adler",
]

[[package]]
name = "mio"
version = "0.7.14"
source = "registry+https://github.com/rust-lang/crates.io-index"
checksum = "8067b404fe97c70829f082dec8bcf4f71225d7eaea1d8645349cb76fa06205cc"
dependencies = [
 "libc",
 "log",
 "miow",
 "ntapi 0.3.7",
 "winapi",
]

[[package]]
name = "mio"
version = "0.8.11"
source = "registry+https://github.com/rust-lang/crates.io-index"
checksum = "a4a650543ca06a924e8b371db273b2756685faae30f8487da1b56505a8f78b0c"
dependencies = [
 "libc",
 "log",
 "wasi 0.11.0+wasi-snapshot-preview1",
 "windows-sys 0.48.0",
]

[[package]]
name = "mio"
version = "1.0.1"
source = "registry+https://github.com/rust-lang/crates.io-index"
checksum = "4569e456d394deccd22ce1c1913e6ea0e54519f577285001215d33557431afe4"
dependencies = [
 "hermit-abi 0.3.9",
 "libc",
 "wasi 0.11.0+wasi-snapshot-preview1",
 "windows-sys 0.52.0",
]

[[package]]
name = "miow"
version = "0.3.7"
source = "registry+https://github.com/rust-lang/crates.io-index"
checksum = "b9f1c5b025cda876f66ef43a113f91ebc9f4ccef34843000e0adf6ebbab84e21"
dependencies = [
 "winapi",
]

[[package]]
name = "mockall"
version = "0.11.4"
source = "registry+https://github.com/rust-lang/crates.io-index"
checksum = "4c84490118f2ee2d74570d114f3d0493cbf02790df303d2707606c3e14e07c96"
dependencies = [
 "cfg-if 1.0.0",
 "downcast",
 "fragile",
 "lazy_static",
 "mockall_derive",
 "predicates 2.1.5",
 "predicates-tree",
]

[[package]]
name = "mockall_derive"
version = "0.11.4"
source = "registry+https://github.com/rust-lang/crates.io-index"
checksum = "22ce75669015c4f47b289fd4d4f56e894e4c96003ffdf3ac51313126f94c6cbb"
dependencies = [
 "cfg-if 1.0.0",
 "proc-macro2 1.0.79",
 "quote 1.0.35",
 "syn 1.0.109",
]

[[package]]
name = "more-asserts"
version = "0.3.1"
source = "registry+https://github.com/rust-lang/crates.io-index"
checksum = "1fafa6961cabd9c63bcd77a45d7e3b7f3b552b70417831fb0f56db717e72407e"

[[package]]
name = "move-abigen"
version = "0.1.0"
source = "git+https://github.com/fuzzland/ityfuzz-sui-fork.git#a88e9595de3c04a540c059d5f4b6d148419c508c"
dependencies = [
 "anyhow",
 "bcs",
 "heck 0.3.3",
 "log",
 "move-binary-format",
 "move-bytecode-verifier",
 "move-command-line-common",
 "move-core-types",
 "move-model",
 "serde",
]

[[package]]
name = "move-abstract-stack"
version = "0.0.1"
source = "git+https://github.com/fuzzland/ityfuzz-sui-fork.git#a88e9595de3c04a540c059d5f4b6d148419c508c"

[[package]]
name = "move-binary-format"
version = "0.0.3"
source = "git+https://github.com/fuzzland/ityfuzz-sui-fork.git#a88e9595de3c04a540c059d5f4b6d148419c508c"
dependencies = [
 "anyhow",
 "enum-compat-util",
 "move-core-types",
 "move-proc-macros",
 "once_cell",
 "ref-cast",
 "serde",
 "variant_count",
]

[[package]]
name = "move-borrow-graph"
version = "0.0.1"
source = "git+https://github.com/fuzzland/ityfuzz-sui-fork.git#a88e9595de3c04a540c059d5f4b6d148419c508c"

[[package]]
name = "move-bytecode-source-map"
version = "0.1.0"
source = "git+https://github.com/fuzzland/ityfuzz-sui-fork.git#a88e9595de3c04a540c059d5f4b6d148419c508c"
dependencies = [
 "anyhow",
 "bcs",
 "move-binary-format",
 "move-command-line-common",
 "move-core-types",
 "move-ir-types",
 "move-symbol-pool",
 "serde",
]

[[package]]
name = "move-bytecode-utils"
version = "0.1.0"
source = "git+https://github.com/fuzzland/ityfuzz-sui-fork.git#a88e9595de3c04a540c059d5f4b6d148419c508c"
dependencies = [
 "anyhow",
 "move-binary-format",
 "move-core-types",
 "petgraph 0.5.1",
 "serde-reflection",
]

[[package]]
name = "move-bytecode-verifier"
version = "0.1.0"
source = "git+https://github.com/fuzzland/ityfuzz-sui-fork.git#a88e9595de3c04a540c059d5f4b6d148419c508c"
dependencies = [
 "anyhow",
 "fail",
 "move-abstract-stack",
 "move-binary-format",
 "move-borrow-graph",
 "move-core-types",
 "move-vm-config",
 "petgraph 0.5.1",
]

[[package]]
name = "move-bytecode-verifier-v0"
version = "0.1.0"
source = "git+https://github.com/fuzzland/ityfuzz-sui-fork.git#a88e9595de3c04a540c059d5f4b6d148419c508c"
dependencies = [
 "anyhow",
 "fail",
 "move-abstract-stack",
 "move-binary-format",
 "move-borrow-graph",
 "move-core-types",
 "move-vm-config",
 "petgraph 0.5.1",
]

[[package]]
name = "move-bytecode-viewer"
version = "0.1.0"
source = "git+https://github.com/fuzzland/ityfuzz-sui-fork.git#a88e9595de3c04a540c059d5f4b6d148419c508c"
dependencies = [
 "anyhow",
 "clap 3.2.25",
 "crossterm 0.21.0",
 "move-binary-format",
 "move-bytecode-source-map",
 "move-command-line-common",
 "move-disassembler",
 "move-ir-types",
 "regex",
 "tui",
]

[[package]]
name = "move-cli"
version = "0.1.0"
source = "git+https://github.com/fuzzland/ityfuzz-sui-fork.git#a88e9595de3c04a540c059d5f4b6d148419c508c"
dependencies = [
 "anyhow",
 "bcs",
 "clap 3.2.25",
 "codespan-reporting",
 "colored",
 "difference",
 "itertools 0.10.5",
 "move-binary-format",
 "move-bytecode-source-map",
 "move-bytecode-utils",
 "move-bytecode-verifier",
 "move-bytecode-viewer",
 "move-command-line-common",
 "move-compiler",
 "move-core-types",
 "move-coverage",
 "move-disassembler",
 "move-docgen",
 "move-errmapgen",
 "move-ir-types",
 "move-package",
 "move-prover",
 "move-resource-viewer",
 "move-stdlib",
 "move-symbol-pool",
 "move-unit-test",
 "move-vm-profiler",
 "move-vm-runtime",
 "move-vm-test-utils",
 "move-vm-types",
 "once_cell",
 "read-write-set",
 "read-write-set-dynamic",
 "reqwest",
 "serde",
 "serde_json",
 "serde_yaml 0.8.26",
 "tempfile",
 "toml_edit 0.14.4",
 "walkdir",
]

[[package]]
name = "move-command-line-common"
version = "0.1.0"
source = "git+https://github.com/fuzzland/ityfuzz-sui-fork.git#a88e9595de3c04a540c059d5f4b6d148419c508c"
dependencies = [
 "anyhow",
 "difference",
 "dirs-next",
 "hex",
 "move-core-types",
 "num-bigint 0.4.4",
 "once_cell",
 "serde",
 "sha2 0.9.9",
 "walkdir",
]

[[package]]
name = "move-compiler"
version = "0.0.1"
source = "git+https://github.com/fuzzland/ityfuzz-sui-fork.git#a88e9595de3c04a540c059d5f4b6d148419c508c"
dependencies = [
 "anyhow",
 "bcs",
 "clap 3.2.25",
 "codespan-reporting",
 "difference",
 "hex",
 "move-binary-format",
 "move-borrow-graph",
 "move-bytecode-source-map",
 "move-bytecode-verifier",
 "move-command-line-common",
 "move-core-types",
 "move-ir-to-bytecode",
 "move-ir-types",
 "move-symbol-pool",
 "num-bigint 0.4.4",
 "once_cell",
 "petgraph 0.5.1",
 "regex",
 "serde",
 "sha3 0.9.1",
 "tempfile",
 "walkdir",
]

[[package]]
name = "move-core-types"
version = "0.0.4"
source = "git+https://github.com/fuzzland/ityfuzz-sui-fork.git#a88e9595de3c04a540c059d5f4b6d148419c508c"
dependencies = [
 "anyhow",
 "arbitrary",
 "bcs",
 "enum-compat-util",
 "ethnum",
 "hex",
 "move-proc-macros",
 "num 0.4.1",
 "once_cell",
 "primitive-types 0.10.1",
 "proptest",
 "proptest-derive 0.3.0",
 "rand 0.8.5",
 "ref-cast",
 "serde",
 "serde_bytes",
 "uint",
]

[[package]]
name = "move-coverage"
version = "0.1.0"
source = "git+https://github.com/fuzzland/ityfuzz-sui-fork.git#a88e9595de3c04a540c059d5f4b6d148419c508c"
dependencies = [
 "anyhow",
 "bcs",
 "clap 3.2.25",
 "codespan",
 "colored",
 "move-binary-format",
 "move-bytecode-source-map",
 "move-command-line-common",
 "move-core-types",
 "move-ir-types",
 "once_cell",
 "petgraph 0.5.1",
 "serde",
]

[[package]]
name = "move-disassembler"
version = "0.1.0"
source = "git+https://github.com/fuzzland/ityfuzz-sui-fork.git#a88e9595de3c04a540c059d5f4b6d148419c508c"
dependencies = [
 "anyhow",
 "clap 3.2.25",
 "colored",
 "hex",
 "move-binary-format",
 "move-bytecode-source-map",
 "move-bytecode-verifier",
 "move-command-line-common",
 "move-compiler",
 "move-core-types",
 "move-coverage",
 "move-ir-types",
]

[[package]]
name = "move-docgen"
version = "0.1.0"
source = "git+https://github.com/fuzzland/ityfuzz-sui-fork.git#a88e9595de3c04a540c059d5f4b6d148419c508c"
dependencies = [
 "anyhow",
 "codespan",
 "codespan-reporting",
 "itertools 0.10.5",
 "log",
 "move-compiler",
 "move-model",
 "num 0.4.1",
 "once_cell",
 "regex",
 "serde",
]

[[package]]
name = "move-errmapgen"
version = "0.1.0"
source = "git+https://github.com/fuzzland/ityfuzz-sui-fork.git#a88e9595de3c04a540c059d5f4b6d148419c508c"
dependencies = [
 "anyhow",
 "bcs",
 "log",
 "move-command-line-common",
 "move-core-types",
 "move-model",
 "serde",
]

[[package]]
name = "move-ir-compiler"
version = "0.1.0"
source = "git+https://github.com/fuzzland/ityfuzz-sui-fork.git#a88e9595de3c04a540c059d5f4b6d148419c508c"
dependencies = [
 "anyhow",
 "bcs",
 "clap 3.2.25",
 "move-binary-format",
 "move-bytecode-source-map",
 "move-bytecode-verifier",
 "move-command-line-common",
 "move-core-types",
 "move-ir-to-bytecode",
 "move-ir-types",
 "move-symbol-pool",
 "serde_json",
]

[[package]]
name = "move-ir-to-bytecode"
version = "0.1.0"
source = "git+https://github.com/fuzzland/ityfuzz-sui-fork.git#a88e9595de3c04a540c059d5f4b6d148419c508c"
dependencies = [
 "anyhow",
 "codespan-reporting",
 "log",
 "move-binary-format",
 "move-bytecode-source-map",
 "move-command-line-common",
 "move-core-types",
 "move-ir-to-bytecode-syntax",
 "move-ir-types",
 "move-symbol-pool",
 "ouroboros 0.9.5",
 "thiserror",
]

[[package]]
name = "move-ir-to-bytecode-syntax"
version = "0.1.0"
source = "git+https://github.com/fuzzland/ityfuzz-sui-fork.git#a88e9595de3c04a540c059d5f4b6d148419c508c"
dependencies = [
 "anyhow",
 "hex",
 "move-command-line-common",
 "move-core-types",
 "move-ir-types",
 "move-symbol-pool",
]

[[package]]
name = "move-ir-types"
version = "0.1.0"
source = "git+https://github.com/fuzzland/ityfuzz-sui-fork.git#a88e9595de3c04a540c059d5f4b6d148419c508c"
dependencies = [
 "anyhow",
 "hex",
 "move-command-line-common",
 "move-core-types",
 "move-symbol-pool",
 "once_cell",
 "serde",
]

[[package]]
name = "move-model"
version = "0.1.0"
source = "git+https://github.com/fuzzland/ityfuzz-sui-fork.git#a88e9595de3c04a540c059d5f4b6d148419c508c"
dependencies = [
 "anyhow",
 "codespan",
 "codespan-reporting",
 "internment",
 "itertools 0.10.5",
 "log",
 "move-binary-format",
 "move-bytecode-source-map",
 "move-bytecode-verifier",
 "move-command-line-common",
 "move-compiler",
 "move-core-types",
 "move-disassembler",
 "move-ir-types",
 "move-symbol-pool",
 "num 0.4.1",
 "once_cell",
 "regex",
 "serde",
]

[[package]]
name = "move-package"
version = "0.1.0"
source = "git+https://github.com/fuzzland/ityfuzz-sui-fork.git#a88e9595de3c04a540c059d5f4b6d148419c508c"
dependencies = [
 "anyhow",
 "bcs",
 "clap 3.2.25",
 "colored",
 "dirs-next",
 "itertools 0.10.5",
 "move-abigen",
 "move-binary-format",
 "move-bytecode-source-map",
 "move-bytecode-utils",
 "move-command-line-common",
 "move-compiler",
 "move-core-types",
 "move-docgen",
 "move-model",
 "move-symbol-pool",
 "named-lock",
 "once_cell",
 "petgraph 0.5.1",
 "regex",
 "reqwest",
 "serde",
 "serde_yaml 0.8.26",
 "sha2 0.9.9",
 "tempfile",
 "toml 0.5.11",
 "treeline",
 "walkdir",
 "whoami",
]

[[package]]
name = "move-proc-macros"
version = "0.1.0"
source = "git+https://github.com/fuzzland/ityfuzz-sui-fork.git#a88e9595de3c04a540c059d5f4b6d148419c508c"
dependencies = [
 "enum-compat-util",
 "proc-macro2 1.0.79",
 "quote 1.0.35",
 "syn 2.0.57",
]

[[package]]
name = "move-prover"
version = "0.1.0"
source = "git+https://github.com/fuzzland/ityfuzz-sui-fork.git#a88e9595de3c04a540c059d5f4b6d148419c508c"
dependencies = [
 "anyhow",
 "async-trait",
 "atty",
 "clap 3.2.25",
 "codespan",
 "codespan-reporting",
 "futures",
 "hex",
 "itertools 0.10.5",
 "log",
 "move-abigen",
 "move-binary-format",
 "move-command-line-common",
 "move-compiler",
 "move-core-types",
 "move-docgen",
 "move-errmapgen",
 "move-ir-types",
 "move-model",
 "move-prover-boogie-backend",
 "move-stackless-bytecode",
 "num 0.4.1",
 "once_cell",
 "pretty",
 "rand 0.8.5",
 "serde",
 "serde_json",
 "simplelog",
 "tokio",
 "toml 0.5.11",
]

[[package]]
name = "move-prover-boogie-backend"
version = "0.1.0"
source = "git+https://github.com/fuzzland/ityfuzz-sui-fork.git#a88e9595de3c04a540c059d5f4b6d148419c508c"
dependencies = [
 "anyhow",
 "async-trait",
 "codespan",
 "codespan-reporting",
 "futures",
 "itertools 0.10.5",
 "log",
 "move-binary-format",
 "move-command-line-common",
 "move-core-types",
 "move-model",
 "move-stackless-bytecode",
 "num 0.4.1",
 "once_cell",
 "pretty",
 "rand 0.8.5",
 "regex",
 "serde",
 "serde_json",
 "tera",
 "tokio",
]

[[package]]
name = "move-read-write-set-types"
version = "0.0.3"
source = "git+https://github.com/fuzzland/ityfuzz-sui-fork.git#a88e9595de3c04a540c059d5f4b6d148419c508c"
dependencies = [
 "anyhow",
 "move-binary-format",
 "move-core-types",
 "serde",
]

[[package]]
name = "move-resource-viewer"
version = "0.1.0"
source = "git+https://github.com/fuzzland/ityfuzz-sui-fork.git#a88e9595de3c04a540c059d5f4b6d148419c508c"
dependencies = [
 "anyhow",
 "bcs",
 "enum-compat-util",
 "hex",
 "move-binary-format",
 "move-bytecode-utils",
 "move-core-types",
 "move-proc-macros",
 "once_cell",
 "serde",
]

[[package]]
name = "move-stackless-bytecode"
version = "0.1.0"
source = "git+https://github.com/fuzzland/ityfuzz-sui-fork.git#a88e9595de3c04a540c059d5f4b6d148419c508c"
dependencies = [
 "codespan",
 "codespan-reporting",
 "ethnum",
 "im",
 "itertools 0.10.5",
 "log",
 "move-binary-format",
 "move-borrow-graph",
 "move-bytecode-verifier",
 "move-command-line-common",
 "move-compiler",
 "move-core-types",
 "move-ir-to-bytecode",
 "move-model",
 "move-read-write-set-types",
 "num 0.4.1",
 "once_cell",
 "paste",
 "petgraph 0.5.1",
 "serde",
]

[[package]]
name = "move-stackless-bytecode-interpreter"
version = "0.1.0"
source = "git+https://github.com/fuzzland/ityfuzz-sui-fork.git#a88e9595de3c04a540c059d5f4b6d148419c508c"
dependencies = [
 "anyhow",
 "bytecode-interpreter-crypto",
 "clap 3.2.25",
 "codespan-reporting",
 "itertools 0.10.5",
 "move-binary-format",
 "move-core-types",
 "move-model",
 "move-stackless-bytecode",
 "num 0.4.1",
 "serde",
]

[[package]]
name = "move-stdlib"
version = "0.1.1"
source = "git+https://github.com/fuzzland/ityfuzz-sui-fork.git#a88e9595de3c04a540c059d5f4b6d148419c508c"
dependencies = [
 "anyhow",
 "hex",
 "log",
 "move-binary-format",
 "move-command-line-common",
 "move-core-types",
 "move-docgen",
 "move-errmapgen",
 "move-prover",
 "move-vm-runtime",
 "move-vm-types",
 "sha2 0.9.9",
 "sha3 0.9.1",
 "smallvec",
 "walkdir",
]

[[package]]
name = "move-stdlib-v0"
version = "0.1.1"
source = "git+https://github.com/fuzzland/ityfuzz-sui-fork.git#a88e9595de3c04a540c059d5f4b6d148419c508c"
dependencies = [
 "anyhow",
 "hex",
 "log",
 "move-binary-format",
 "move-command-line-common",
 "move-core-types",
 "move-docgen",
 "move-errmapgen",
 "move-prover",
 "move-vm-runtime-v0",
 "move-vm-types",
 "sha2 0.9.9",
 "sha3 0.9.1",
 "smallvec",
 "walkdir",
]

[[package]]
name = "move-symbol-pool"
version = "0.1.0"
source = "git+https://github.com/fuzzland/ityfuzz-sui-fork.git#a88e9595de3c04a540c059d5f4b6d148419c508c"
dependencies = [
 "once_cell",
 "phf",
 "serde",
]

[[package]]
name = "move-transactional-test-runner"
version = "0.1.0"
source = "git+https://github.com/fuzzland/ityfuzz-sui-fork.git#a88e9595de3c04a540c059d5f4b6d148419c508c"
dependencies = [
 "anyhow",
 "async-trait",
 "clap 3.2.25",
 "colored",
 "move-binary-format",
 "move-bytecode-source-map",
 "move-bytecode-utils",
 "move-bytecode-verifier",
 "move-cli",
 "move-command-line-common",
 "move-compiler",
 "move-core-types",
 "move-disassembler",
 "move-ir-compiler",
 "move-ir-types",
 "move-resource-viewer",
 "move-stackless-bytecode-interpreter",
 "move-stdlib",
 "move-symbol-pool",
 "move-vm-config",
 "move-vm-runtime",
 "move-vm-test-utils",
 "move-vm-types",
 "once_cell",
 "rayon",
 "regex",
 "tempfile",
 "tokio",
]

[[package]]
name = "move-unit-test"
version = "0.1.0"
source = "git+https://github.com/fuzzland/ityfuzz-sui-fork.git#a88e9595de3c04a540c059d5f4b6d148419c508c"
dependencies = [
 "anyhow",
 "better_any",
 "clap 3.2.25",
 "codespan-reporting",
 "colored",
 "itertools 0.10.5",
 "move-binary-format",
 "move-bytecode-utils",
 "move-command-line-common",
 "move-compiler",
 "move-core-types",
 "move-ir-types",
 "move-model",
 "move-resource-viewer",
 "move-stackless-bytecode-interpreter",
 "move-stdlib",
 "move-symbol-pool",
 "move-vm-profiler",
 "move-vm-runtime",
 "move-vm-test-utils",
 "move-vm-types",
 "once_cell",
 "rayon",
 "regex",
]

[[package]]
name = "move-vm-config"
version = "0.1.0"
source = "git+https://github.com/fuzzland/ityfuzz-sui-fork.git#a88e9595de3c04a540c059d5f4b6d148419c508c"
dependencies = [
 "move-binary-format",
]

[[package]]
name = "move-vm-profiler"
version = "0.1.0"
source = "git+https://github.com/fuzzland/ityfuzz-sui-fork.git#a88e9595de3c04a540c059d5f4b6d148419c508c"
dependencies = [
 "move-vm-config",
 "once_cell",
 "serde",
 "serde_json",
 "serde_with",
]

[[package]]
name = "move-vm-runtime"
version = "0.1.0"
source = "git+https://github.com/fuzzland/ityfuzz-sui-fork.git#a88e9595de3c04a540c059d5f4b6d148419c508c"
dependencies = [
 "better_any",
 "fail",
 "move-binary-format",
 "move-bytecode-verifier",
 "move-core-types",
 "move-vm-config",
 "move-vm-profiler",
 "move-vm-types",
 "once_cell",
 "parking_lot 0.11.2",
 "sha3 0.9.1",
 "smallvec",
 "tracing",
]

[[package]]
name = "move-vm-runtime-v0"
version = "0.1.0"
source = "git+https://github.com/fuzzland/ityfuzz-sui-fork.git#a88e9595de3c04a540c059d5f4b6d148419c508c"
dependencies = [
 "better_any",
 "fail",
 "move-binary-format",
 "move-bytecode-verifier-v0",
 "move-core-types",
 "move-vm-config",
 "move-vm-profiler",
 "move-vm-types",
 "once_cell",
 "parking_lot 0.11.2",
 "sha3 0.9.1",
 "smallvec",
 "tracing",
]

[[package]]
name = "move-vm-test-utils"
version = "0.1.0"
source = "git+https://github.com/fuzzland/ityfuzz-sui-fork.git#a88e9595de3c04a540c059d5f4b6d148419c508c"
dependencies = [
 "anyhow",
 "move-binary-format",
 "move-core-types",
 "move-vm-profiler",
 "move-vm-types",
 "once_cell",
 "serde",
]

[[package]]
name = "move-vm-types"
version = "0.1.0"
source = "git+https://github.com/fuzzland/ityfuzz-sui-fork.git#a88e9595de3c04a540c059d5f4b6d148419c508c"
dependencies = [
 "bcs",
 "move-binary-format",
 "move-core-types",
 "move-vm-profiler",
 "once_cell",
 "serde",
 "smallvec",
]

[[package]]
name = "msim"
version = "0.1.0"
source = "git+https://github.com/MystenLabs/mysten-sim.git?rev=bd870b9a11870a2618c55ba703d58fafd41d686c#bd870b9a11870a2618c55ba703d58fafd41d686c"
dependencies = [
 "ahash 0.7.8",
 "async-task 4.3.0",
 "bincode",
 "bytes",
 "cc",
 "downcast-rs",
 "env_logger",
 "erasable",
 "futures",
 "lazy_static",
 "libc",
 "msim-macros",
 "naive-timer",
 "pin-project-lite",
 "rand 0.8.5",
 "real_tokio",
 "serde",
 "socket2 0.4.10",
 "tap",
 "tokio-util 0.7.7",
 "toml 0.5.11",
 "tracing",
 "tracing-subscriber 0.3.18",
]

[[package]]
name = "msim-macros"
version = "0.1.0"
source = "git+https://github.com/MystenLabs/mysten-sim.git?rev=bd870b9a11870a2618c55ba703d58fafd41d686c#bd870b9a11870a2618c55ba703d58fafd41d686c"
dependencies = [
 "darling 0.14.4",
 "proc-macro2 1.0.79",
 "quote 1.0.35",
 "syn 1.0.109",
]

[[package]]
name = "multiaddr"
version = "0.17.1"
source = "registry+https://github.com/rust-lang/crates.io-index"
checksum = "2b36f567c7099511fa8612bbbb52dda2419ce0bdbacf31714e3a5ffdb766d3bd"
dependencies = [
 "arrayref",
 "byteorder",
 "data-encoding",
 "log",
 "multibase",
 "multihash",
 "percent-encoding",
 "serde",
 "static_assertions",
 "unsigned-varint",
 "url",
]

[[package]]
name = "multibase"
version = "0.9.1"
source = "registry+https://github.com/rust-lang/crates.io-index"
checksum = "9b3539ec3c1f04ac9748a260728e855f261b4977f5c3406612c884564f329404"
dependencies = [
 "base-x",
 "data-encoding",
 "data-encoding-macro",
]

[[package]]
name = "multihash"
version = "0.17.0"
source = "registry+https://github.com/rust-lang/crates.io-index"
checksum = "835d6ff01d610179fbce3de1694d007e500bf33a7f29689838941d6bf783ae40"
dependencies = [
 "core2",
 "multihash-derive",
 "unsigned-varint",
]

[[package]]
name = "multihash-derive"
version = "0.8.1"
source = "registry+https://github.com/rust-lang/crates.io-index"
checksum = "1d6d4752e6230d8ef7adf7bd5d8c4b1f6561c1014c5ba9a37445ccefe18aa1db"
dependencies = [
 "proc-macro-crate 1.1.3",
 "proc-macro-error",
 "proc-macro2 1.0.79",
 "quote 1.0.35",
 "syn 1.0.109",
 "synstructure",
]

[[package]]
name = "multimap"
version = "0.8.3"
source = "registry+https://github.com/rust-lang/crates.io-index"
checksum = "e5ce46fe64a9d73be07dcbe690a38ce1b293be448fd8ce1e6c1b8062c9f72c6a"

[[package]]
name = "mysten-metrics"
version = "0.7.0"
source = "git+https://github.com/fuzzland/ityfuzz-sui-fork.git#a88e9595de3c04a540c059d5f4b6d148419c508c"
dependencies = [
 "async-trait",
 "dashmap",
 "futures",
 "once_cell",
 "parking_lot 0.12.1",
 "prometheus",
 "prometheus-closure-metric",
 "scopeguard",
 "tap",
 "tokio",
 "tracing",
 "uuid 1.8.0",
 "workspace-hack",
]

[[package]]
name = "mysten-network"
version = "0.2.0"
source = "git+https://github.com/fuzzland/ityfuzz-sui-fork.git#a88e9595de3c04a540c059d5f4b6d148419c508c"
dependencies = [
 "anemo",
 "bcs",
 "bytes",
 "eyre",
 "futures",
 "http",
 "multiaddr",
 "serde",
 "snap",
 "tokio",
 "tokio-stream",
 "tonic 0.8.3",
 "tonic-health",
 "tower",
 "tower-http",
 "tracing",
 "workspace-hack",
]

[[package]]
name = "mysten-util-mem"
version = "0.11.0"
source = "git+https://github.com/fuzzland/ityfuzz-sui-fork.git#a88e9595de3c04a540c059d5f4b6d148419c508c"
dependencies = [
 "cfg-if 1.0.0",
 "ed25519-consensus",
 "fastcrypto",
 "hashbrown 0.12.3",
 "impl-trait-for-tuples",
 "indexmap 1.9.3",
 "mysten-util-mem-derive",
 "once_cell",
 "parking_lot 0.12.1",
 "roaring",
 "smallvec",
 "workspace-hack",
]

[[package]]
name = "mysten-util-mem-derive"
version = "0.1.0"
source = "git+https://github.com/fuzzland/ityfuzz-sui-fork.git#a88e9595de3c04a540c059d5f4b6d148419c508c"
dependencies = [
 "proc-macro2 1.0.79",
 "syn 1.0.109",
 "synstructure",
 "workspace-hack",
]

[[package]]
name = "naive-timer"
version = "0.2.0"
source = "registry+https://github.com/rust-lang/crates.io-index"
checksum = "034a0ad7deebf0c2abcf2435950a6666c3c15ea9d8fad0c0f48efa8a7f843fed"

[[package]]
name = "named-lock"
version = "0.2.0"
source = "registry+https://github.com/rust-lang/crates.io-index"
checksum = "40a3eb6b7c682b65d1f631ec3176829d72ab450b3aacdd3f719bf220822e59ac"
dependencies = [
 "libc",
 "once_cell",
 "parking_lot 0.12.1",
 "thiserror",
 "widestring",
 "winapi",
]

[[package]]
name = "narwhal-config"
version = "0.1.0"
source = "git+https://github.com/fuzzland/ityfuzz-sui-fork.git#a88e9595de3c04a540c059d5f4b6d148419c508c"
dependencies = [
 "fastcrypto",
 "match_opt",
 "mysten-network",
 "mysten-util-mem",
 "narwhal-crypto",
 "rand 0.8.5",
 "serde",
 "serde_json",
 "sui-protocol-config",
 "thiserror",
 "tracing",
 "workspace-hack",
]

[[package]]
name = "narwhal-crypto"
version = "0.1.0"
source = "git+https://github.com/fuzzland/ityfuzz-sui-fork.git#a88e9595de3c04a540c059d5f4b6d148419c508c"
dependencies = [
 "bcs",
 "fastcrypto",
 "serde",
 "shared-crypto",
 "workspace-hack",
]

[[package]]
name = "native-tls"
version = "0.2.11"
source = "registry+https://github.com/rust-lang/crates.io-index"
checksum = "07226173c32f2926027b63cce4bcd8076c3552846cbe7925f3aaffeac0a3b92e"
dependencies = [
 "lazy_static",
 "libc",
 "log",
 "openssl",
 "openssl-probe",
 "openssl-sys",
 "schannel",
 "security-framework",
 "security-framework-sys",
 "tempfile",
]

[[package]]
name = "nested"
version = "0.1.1"
source = "registry+https://github.com/rust-lang/crates.io-index"
checksum = "ca2b420f638f07fe83056b55ea190bb815f609ec5a35e7017884a10f78839c9e"

[[package]]
name = "new_debug_unreachable"
version = "1.0.6"
source = "registry+https://github.com/rust-lang/crates.io-index"
checksum = "650eef8c711430f1a879fdd01d4745a7deea475becfb90269c06775983bbf086"

[[package]]
name = "newline-converter"
version = "0.2.2"
source = "registry+https://github.com/rust-lang/crates.io-index"
checksum = "1f71d09d5c87634207f894c6b31b6a2b2c64ea3bdcf71bd5599fdbbe1600c00f"
dependencies = [
 "unicode-segmentation",
]

[[package]]
name = "nexlint"
version = "0.1.0"
source = "git+https://github.com/nextest-rs/nexlint.git?rev=94da5c787636dad779c340affa65219134d127f5#94da5c787636dad779c340affa65219134d127f5"
dependencies = [
 "camino",
 "debug-ignore",
 "determinator",
 "guppy",
 "hakari",
 "hex",
 "once_cell",
 "serde",
]

[[package]]
name = "nexlint-lints"
version = "0.1.0"
source = "git+https://github.com/nextest-rs/nexlint.git?rev=94da5c787636dad779c340affa65219134d127f5#94da5c787636dad779c340affa65219134d127f5"
dependencies = [
 "anyhow",
 "camino",
 "colored-diff",
 "globset",
 "guppy",
 "nexlint",
 "regex",
 "serde",
 "toml 0.5.11",
]

[[package]]
name = "nibble_vec"
version = "0.1.0"
source = "registry+https://github.com/rust-lang/crates.io-index"
checksum = "77a5d83df9f36fe23f0c3648c6bbb8b0298bb5f1939c8f2704431371f4b84d43"
dependencies = [
 "smallvec",
]

[[package]]
name = "nix"
version = "0.15.0"
source = "registry+https://github.com/rust-lang/crates.io-index"
checksum = "3b2e0b4f3320ed72aaedb9a5ac838690a8047c7b275da22711fddff4f8a14229"
dependencies = [
 "bitflags 1.3.2",
 "cc",
 "cfg-if 0.1.10",
 "libc",
 "void",
]

[[package]]
name = "nix"
version = "0.23.2"
source = "registry+https://github.com/rust-lang/crates.io-index"
checksum = "8f3790c00a0150112de0f4cd161e3d7fc4b2d8a5542ffc35f099a2562aecb35c"
dependencies = [
 "bitflags 1.3.2",
 "cc",
 "cfg-if 1.0.0",
 "libc",
 "memoffset 0.6.5",
]

[[package]]
name = "nix"
version = "0.24.3"
source = "registry+https://github.com/rust-lang/crates.io-index"
checksum = "fa52e972a9a719cecb6864fb88568781eb706bac2cd1d4f04a648542dbf78069"
dependencies = [
 "bitflags 1.3.2",
 "cfg-if 1.0.0",
 "libc",
]

[[package]]
name = "nix"
version = "0.26.4"
source = "registry+https://github.com/rust-lang/crates.io-index"
checksum = "598beaf3cc6fdd9a5dfb1630c2800c7acd31df7aaf0f565796fba2b53ca1af1b"
dependencies = [
 "bitflags 1.3.2",
 "cfg-if 1.0.0",
 "libc",
 "memoffset 0.7.1",
 "pin-utils",
]

[[package]]
name = "nix"
version = "0.27.1"
source = "registry+https://github.com/rust-lang/crates.io-index"
checksum = "2eb04e9c688eff1c89d72b407f168cf79bb9e867a9d3323ed6c01519eb9cc053"
dependencies = [
 "bitflags 2.5.0",
 "cfg-if 1.0.0",
 "libc",
]

[[package]]
name = "no-std-compat"
version = "0.4.1"
source = "registry+https://github.com/rust-lang/crates.io-index"
checksum = "b93853da6d84c2e3c7d730d6473e8817692dd89be387eb01b94d7f108ecb5b8c"

[[package]]
name = "nom"
version = "2.2.1"
source = "registry+https://github.com/rust-lang/crates.io-index"
checksum = "cf51a729ecf40266a2368ad335a5fdde43471f545a967109cd62146ecf8b66ff"

[[package]]
name = "nom"
version = "7.1.3"
source = "registry+https://github.com/rust-lang/crates.io-index"
checksum = "d273983c5a657a70a3e8f2a01329822f3b8c8172b73826411a55751e404a0a4a"
dependencies = [
 "memchr",
 "minimal-lexical",
]

[[package]]
name = "nom8"
version = "0.2.0"
source = "registry+https://github.com/rust-lang/crates.io-index"
checksum = "ae01545c9c7fc4486ab7debaf2aad7003ac19431791868fb2e8066df97fad2f8"
dependencies = [
 "memchr",
]

[[package]]
name = "nonzero_ext"
version = "0.3.0"
source = "registry+https://github.com/rust-lang/crates.io-index"
checksum = "38bf9645c8b145698bb0b18a4637dcacbc421ea49bef2317e4fd8065a387cf21"

[[package]]
name = "normalize-line-endings"
version = "0.3.0"
source = "registry+https://github.com/rust-lang/crates.io-index"
checksum = "61807f77802ff30975e01f4f071c8ba10c022052f98b3294119f3e615d13e5be"

[[package]]
name = "ntapi"
version = "0.3.7"
source = "registry+https://github.com/rust-lang/crates.io-index"
checksum = "c28774a7fd2fbb4f0babd8237ce554b73af68021b5f695a3cebd6c59bac0980f"
dependencies = [
 "winapi",
]

[[package]]
name = "ntapi"
version = "0.4.1"
source = "registry+https://github.com/rust-lang/crates.io-index"
checksum = "e8a3895c6391c39d7fe7ebc444a87eb2991b2a0bc718fdabd071eec617fc68e4"
dependencies = [
 "winapi",
]

[[package]]
name = "ntest"
version = "0.9.2"
source = "registry+https://github.com/rust-lang/crates.io-index"
checksum = "41cd16a2e6992865367e7ca50cd6953d09daaed93641421168733a1274afadd6"
dependencies = [
 "ntest_test_cases",
 "ntest_timeout",
]

[[package]]
name = "ntest_test_cases"
version = "0.9.2"
source = "registry+https://github.com/rust-lang/crates.io-index"
checksum = "197eff6c12b80ff5de6173e438fa3c1340a9e708118c1626e690f65aee1e5332"
dependencies = [
 "proc-macro2 1.0.79",
 "quote 1.0.35",
 "syn 1.0.109",
]

[[package]]
name = "ntest_timeout"
version = "0.9.2"
source = "registry+https://github.com/rust-lang/crates.io-index"
checksum = "ef492b5cf80f90c050b287e747228a1fa6517e9d754f364b5a7e0e038e49a25f"
dependencies = [
 "proc-macro-crate 3.1.0",
 "proc-macro2 1.0.79",
 "quote 1.0.35",
 "syn 1.0.109",
]

[[package]]
name = "nu-ansi-term"
version = "0.46.0"
source = "registry+https://github.com/rust-lang/crates.io-index"
checksum = "77a8165726e8236064dbb45459242600304b42a5ea24ee2948e18e023bf7ba84"
dependencies = [
 "overload",
 "winapi",
]

[[package]]
name = "num"
version = "0.2.1"
source = "registry+https://github.com/rust-lang/crates.io-index"
checksum = "b8536030f9fea7127f841b45bb6243b27255787fb4eb83958aa1ef9d2fdc0c36"
dependencies = [
 "num-bigint 0.2.6",
 "num-complex 0.2.4",
 "num-integer",
 "num-iter",
 "num-rational 0.2.4",
 "num-traits",
]

[[package]]
name = "num"
version = "0.4.1"
source = "registry+https://github.com/rust-lang/crates.io-index"
checksum = "b05180d69e3da0e530ba2a1dae5110317e49e3b7f3d41be227dc5f92e49ee7af"
dependencies = [
 "num-bigint 0.4.4",
 "num-complex 0.4.5",
 "num-integer",
 "num-iter",
 "num-rational 0.4.1",
 "num-traits",
]

[[package]]
name = "num-bigint"
version = "0.2.6"
source = "registry+https://github.com/rust-lang/crates.io-index"
checksum = "090c7f9998ee0ff65aa5b723e4009f7b217707f1fb5ea551329cc4d6231fb304"
dependencies = [
 "autocfg",
 "num-integer",
 "num-traits",
]

[[package]]
name = "num-bigint"
version = "0.4.4"
source = "registry+https://github.com/rust-lang/crates.io-index"
checksum = "608e7659b5c3d7cba262d894801b9ec9d00de989e8a82bd4bef91d08da45cdc0"
dependencies = [
 "autocfg",
 "num-integer",
 "num-traits",
 "rand 0.8.5",
]

[[package]]
name = "num-bigint-dig"
version = "0.8.4"
source = "registry+https://github.com/rust-lang/crates.io-index"
checksum = "dc84195820f291c7697304f3cbdadd1cb7199c0efc917ff5eafd71225c136151"
dependencies = [
 "byteorder",
 "lazy_static",
 "libm",
 "num-integer",
 "num-iter",
 "num-traits",
 "rand 0.8.5",
 "smallvec",
 "zeroize",
]

[[package]]
name = "num-complex"
version = "0.2.4"
source = "registry+https://github.com/rust-lang/crates.io-index"
checksum = "b6b19411a9719e753aff12e5187b74d60d3dc449ec3f4dc21e3989c3f554bc95"
dependencies = [
 "autocfg",
 "num-traits",
]

[[package]]
name = "num-complex"
version = "0.4.5"
source = "registry+https://github.com/rust-lang/crates.io-index"
checksum = "23c6602fda94a57c990fe0df199a035d83576b496aa29f4e634a8ac6004e68a6"
dependencies = [
 "num-traits",
]

[[package]]
name = "num-conv"
version = "0.1.0"
source = "registry+https://github.com/rust-lang/crates.io-index"
checksum = "51d515d32fb182ee37cda2ccdcb92950d6a3c2893aa280e540671c2cd0f3b1d9"

[[package]]
name = "num-format"
version = "0.4.4"
source = "registry+https://github.com/rust-lang/crates.io-index"
checksum = "a652d9771a63711fd3c3deb670acfbe5c30a4072e664d7a3bf5a9e1056ac72c3"
dependencies = [
 "arrayvec 0.7.4",
 "itoa",
]

[[package]]
name = "num-integer"
version = "0.1.46"
source = "registry+https://github.com/rust-lang/crates.io-index"
checksum = "7969661fd2958a5cb096e56c8e1ad0444ac2bbcd0061bd28660485a44879858f"
dependencies = [
 "num-traits",
]

[[package]]
name = "num-iter"
version = "0.1.44"
source = "registry+https://github.com/rust-lang/crates.io-index"
checksum = "d869c01cc0c455284163fd0092f1f93835385ccab5a98a0dcc497b2f8bf055a9"
dependencies = [
 "autocfg",
 "num-integer",
 "num-traits",
]

[[package]]
name = "num-rational"
version = "0.2.4"
source = "registry+https://github.com/rust-lang/crates.io-index"
checksum = "5c000134b5dbf44adc5cb772486d335293351644b801551abe8f75c84cfa4aef"
dependencies = [
 "autocfg",
 "num-bigint 0.2.6",
 "num-integer",
 "num-traits",
]

[[package]]
name = "num-rational"
version = "0.4.1"
source = "registry+https://github.com/rust-lang/crates.io-index"
checksum = "0638a1c9d0a3c0914158145bc76cff373a75a627e6ecbfb71cbe6f453a5a19b0"
dependencies = [
 "autocfg",
 "num-bigint 0.4.4",
 "num-integer",
 "num-traits",
]

[[package]]
name = "num-traits"
version = "0.2.18"
source = "registry+https://github.com/rust-lang/crates.io-index"
checksum = "da0df0e5185db44f69b44f26786fe401b6c293d1907744beaa7fa62b2e5a517a"
dependencies = [
 "autocfg",
 "libm",
]

[[package]]
name = "num_cpus"
version = "1.16.0"
source = "registry+https://github.com/rust-lang/crates.io-index"
checksum = "4161fcb6d602d4d2081af7c3a45852d875a03dd337a6bfdd6e06407b61342a43"
dependencies = [
 "hermit-abi 0.3.9",
 "libc",
]

[[package]]
name = "num_enum"
version = "0.6.1"
source = "registry+https://github.com/rust-lang/crates.io-index"
checksum = "7a015b430d3c108a207fd776d2e2196aaf8b1cf8cf93253e3a097ff3085076a1"
dependencies = [
 "num_enum_derive 0.6.1",
]

[[package]]
name = "num_enum"
version = "0.7.2"
source = "registry+https://github.com/rust-lang/crates.io-index"
checksum = "02339744ee7253741199f897151b38e72257d13802d4ee837285cc2990a90845"
dependencies = [
 "num_enum_derive 0.7.2",
]

[[package]]
name = "num_enum_derive"
version = "0.6.1"
source = "registry+https://github.com/rust-lang/crates.io-index"
checksum = "96667db765a921f7b295ffee8b60472b686a51d4f21c2ee4ffdb94c7013b65a6"
dependencies = [
 "proc-macro-crate 1.1.3",
 "proc-macro2 1.0.79",
 "quote 1.0.35",
 "syn 2.0.57",
]

[[package]]
name = "num_enum_derive"
version = "0.7.2"
source = "registry+https://github.com/rust-lang/crates.io-index"
checksum = "681030a937600a36906c185595136d26abfebb4aa9c65701cefcaf8578bb982b"
dependencies = [
 "proc-macro-crate 3.1.0",
 "proc-macro2 1.0.79",
 "quote 1.0.35",
 "syn 2.0.57",
]

[[package]]
name = "number_prefix"
version = "0.4.0"
source = "registry+https://github.com/rust-lang/crates.io-index"
checksum = "830b246a0e5f20af87141b25c173cd1b609bd7779a4617d6ec582abaf90870f3"

[[package]]
name = "object"
version = "0.30.4"
source = "registry+https://github.com/rust-lang/crates.io-index"
checksum = "03b4680b86d9cfafba8fc491dc9b6df26b68cf40e9e6cd73909194759a63c385"
dependencies = [
 "memchr",
]

[[package]]
name = "object"
version = "0.32.2"
source = "registry+https://github.com/rust-lang/crates.io-index"
checksum = "a6a622008b6e321afc04970976f62ee297fdbaa6f95318ca343e3eebb9648441"
dependencies = [
 "memchr",
]

[[package]]
name = "oid-registry"
version = "0.6.1"
source = "registry+https://github.com/rust-lang/crates.io-index"
checksum = "9bedf36ffb6ba96c2eb7144ef6270557b52e54b20c0a8e1eb2ff99a6c6959bff"
dependencies = [
 "asn1-rs",
]

[[package]]
name = "once_cell"
version = "1.19.0"
source = "registry+https://github.com/rust-lang/crates.io-index"
checksum = "3fdb12b2476b595f9358c5161aa467c2438859caa136dec86c26fdd2efe17b92"

[[package]]
name = "oorandom"
version = "11.1.3"
source = "registry+https://github.com/rust-lang/crates.io-index"
checksum = "0ab1bc2a289d34bd04a330323ac98a1b4bc82c9d9fcb1e66b63caa84da26b575"

[[package]]
name = "opaque-debug"
version = "0.3.1"
source = "registry+https://github.com/rust-lang/crates.io-index"
checksum = "c08d65885ee38876c4f86fa503fb49d7b507c2b62552df7c70b2fce627e06381"

[[package]]
name = "open-fastrlp"
version = "0.1.4"
source = "registry+https://github.com/rust-lang/crates.io-index"
checksum = "786393f80485445794f6043fd3138854dd109cc6c4bd1a6383db304c9ce9b9ce"
dependencies = [
 "arrayvec 0.7.4",
 "auto_impl",
 "bytes",
 "ethereum-types",
 "open-fastrlp-derive",
]

[[package]]
name = "open-fastrlp-derive"
version = "0.1.1"
source = "registry+https://github.com/rust-lang/crates.io-index"
checksum = "003b2be5c6c53c1cfeb0a238b8a1c3915cd410feb684457a36c10038f764bb1c"
dependencies = [
 "bytes",
 "proc-macro2 1.0.79",
 "quote 1.0.35",
 "syn 1.0.109",
]

[[package]]
name = "openssl"
version = "0.10.64"
source = "registry+https://github.com/rust-lang/crates.io-index"
checksum = "95a0481286a310808298130d22dd1fef0fa571e05a8f44ec801801e84b216b1f"
dependencies = [
 "bitflags 2.5.0",
 "cfg-if 1.0.0",
 "foreign-types",
 "libc",
 "once_cell",
 "openssl-macros",
 "openssl-sys",
]

[[package]]
name = "openssl-macros"
version = "0.1.1"
source = "registry+https://github.com/rust-lang/crates.io-index"
checksum = "a948666b637a0f465e8564c73e89d4dde00d72d4d473cc972f390fc3dcee7d9c"
dependencies = [
 "proc-macro2 1.0.79",
 "quote 1.0.35",
 "syn 2.0.57",
]

[[package]]
name = "openssl-probe"
version = "0.1.5"
source = "registry+https://github.com/rust-lang/crates.io-index"
checksum = "ff011a302c396a5197692431fc1948019154afc178baf7d8e37367442a4601cf"

[[package]]
name = "openssl-sys"
version = "0.9.102"
source = "registry+https://github.com/rust-lang/crates.io-index"
checksum = "c597637d56fbc83893a35eb0dd04b2b8e7a50c91e64e9493e398b5df4fb45fa2"
dependencies = [
 "cc",
 "libc",
 "pkg-config",
 "vcpkg",
]

[[package]]
name = "option-ext"
version = "0.2.0"
source = "registry+https://github.com/rust-lang/crates.io-index"
checksum = "04744f49eae99ab78e0d5c0b603ab218f515ea8cfe5a456d7629ad883a3b6e7d"

[[package]]
name = "os_info"
version = "3.8.2"
source = "registry+https://github.com/rust-lang/crates.io-index"
checksum = "ae99c7fa6dd38c7cafe1ec085e804f8f555a2f8659b0dbe03f1f9963a9b51092"
dependencies = [
 "log",
 "serde",
 "windows-sys 0.52.0",
]

[[package]]
name = "os_str_bytes"
version = "6.6.1"
source = "registry+https://github.com/rust-lang/crates.io-index"
checksum = "e2355d85b9a3786f481747ced0e0ff2ba35213a1f9bd406ed906554d7af805a1"

[[package]]
name = "ouroboros"
version = "0.9.5"
source = "registry+https://github.com/rust-lang/crates.io-index"
checksum = "fbeff60e3e37407a80ead3e9458145b456e978c4068cddbfea6afb48572962ca"
dependencies = [
 "ouroboros_macro 0.9.5",
 "stable_deref_trait",
]

[[package]]
name = "ouroboros"
version = "0.15.6"
source = "registry+https://github.com/rust-lang/crates.io-index"
checksum = "e1358bd1558bd2a083fed428ffeda486fbfb323e698cdda7794259d592ca72db"
dependencies = [
 "aliasable",
 "ouroboros_macro 0.15.6",
]

[[package]]
name = "ouroboros_macro"
version = "0.9.5"
source = "registry+https://github.com/rust-lang/crates.io-index"
checksum = "03f2cb802b5bdfdf52f1ffa0b54ce105e4d346e91990dd571f86c91321ad49e2"
dependencies = [
 "Inflector",
 "proc-macro-error",
 "proc-macro2 1.0.79",
 "quote 1.0.35",
 "syn 1.0.109",
]

[[package]]
name = "ouroboros_macro"
version = "0.15.6"
source = "registry+https://github.com/rust-lang/crates.io-index"
checksum = "5f7d21ccd03305a674437ee1248f3ab5d4b1db095cf1caf49f1713ddf61956b7"
dependencies = [
 "Inflector",
 "proc-macro-error",
 "proc-macro2 1.0.79",
 "quote 1.0.35",
 "syn 1.0.109",
]

[[package]]
name = "output_vt100"
version = "0.1.3"
source = "registry+https://github.com/rust-lang/crates.io-index"
checksum = "628223faebab4e3e40667ee0b2336d34a5b960ff60ea743ddfdbcf7770bcfb66"
dependencies = [
 "winapi",
]

[[package]]
name = "outref"
version = "0.5.1"
source = "registry+https://github.com/rust-lang/crates.io-index"
checksum = "4030760ffd992bef45b0ae3f10ce1aba99e33464c90d14dd7c039884963ddc7a"

[[package]]
name = "overload"
version = "0.1.1"
source = "registry+https://github.com/rust-lang/crates.io-index"
checksum = "b15813163c1d831bf4a13c3610c05c0d03b39feb07f7e09fa234dac9b15aaf39"

[[package]]
name = "owo-colors"
version = "3.5.0"
source = "registry+https://github.com/rust-lang/crates.io-index"
checksum = "c1b04fb49957986fdce4d6ee7a65027d55d4b6d2265e5848bbb507b58ccfdb6f"

[[package]]
name = "p256"
version = "0.13.2"
source = "registry+https://github.com/rust-lang/crates.io-index"
checksum = "c9863ad85fa8f4460f9c48cb909d38a0d689dba1f6f6988a5e3e0d31071bcd4b"
dependencies = [
 "ecdsa 0.16.9",
 "elliptic-curve 0.13.8",
 "primeorder",
 "sha2 0.10.8",
]

[[package]]
name = "palaver"
version = "0.2.8"
source = "registry+https://github.com/rust-lang/crates.io-index"
checksum = "49dfc200733ac34dcd9a1e4a7e454b521723936010bef3710e2d8024a32d685f"
dependencies = [
 "bitflags 1.3.2",
 "heapless",
 "lazy_static",
 "libc",
 "mach",
 "nix 0.15.0",
 "procinfo",
 "typenum",
 "winapi",
]

[[package]]
name = "papergrid"
version = "0.9.1"
source = "registry+https://github.com/rust-lang/crates.io-index"
checksum = "ae7891b22598926e4398790c8fe6447930c72a67d36d983a49d6ce682ce83290"
dependencies = [
 "bytecount",
 "fnv",
 "unicode-width",
]

[[package]]
name = "parity-scale-codec"
version = "2.3.1"
source = "registry+https://github.com/rust-lang/crates.io-index"
checksum = "373b1a4c1338d9cd3d1fa53b3a11bdab5ab6bd80a20f7f7becd76953ae2be909"
dependencies = [
 "arrayvec 0.7.4",
 "bitvec 0.20.4",
 "byte-slice-cast",
 "impl-trait-for-tuples",
 "parity-scale-codec-derive 2.3.1",
 "serde",
]

[[package]]
name = "parity-scale-codec"
version = "3.6.9"
source = "registry+https://github.com/rust-lang/crates.io-index"
checksum = "881331e34fa842a2fb61cc2db9643a8fedc615e47cfcc52597d1af0db9a7e8fe"
dependencies = [
 "arrayvec 0.7.4",
 "bitvec 1.0.1",
 "byte-slice-cast",
 "impl-trait-for-tuples",
 "parity-scale-codec-derive 3.6.9",
 "serde",
]

[[package]]
name = "parity-scale-codec-derive"
version = "2.3.1"
source = "registry+https://github.com/rust-lang/crates.io-index"
checksum = "1557010476e0595c9b568d16dcfb81b93cdeb157612726f5170d31aa707bed27"
dependencies = [
 "proc-macro-crate 1.1.3",
 "proc-macro2 1.0.79",
 "quote 1.0.35",
 "syn 1.0.109",
]

[[package]]
name = "parity-scale-codec-derive"
version = "3.6.9"
source = "registry+https://github.com/rust-lang/crates.io-index"
checksum = "be30eaf4b0a9fba5336683b38de57bb86d179a35862ba6bfcf57625d006bde5b"
dependencies = [
 "proc-macro-crate 2.0.0",
 "proc-macro2 1.0.79",
 "quote 1.0.35",
 "syn 1.0.109",
]

[[package]]
name = "parking"
version = "2.2.0"
source = "registry+https://github.com/rust-lang/crates.io-index"
checksum = "bb813b8af86854136c6922af0598d719255ecb2179515e6e7730d468f05c9cae"

[[package]]
name = "parking_lot"
version = "0.11.2"
source = "registry+https://github.com/rust-lang/crates.io-index"
checksum = "7d17b78036a60663b797adeaee46f5c9dfebb86948d1255007a1d6be0271ff99"
dependencies = [
 "instant",
 "lock_api",
 "parking_lot_core 0.8.6",
]

[[package]]
name = "parking_lot"
version = "0.12.1"
source = "registry+https://github.com/rust-lang/crates.io-index"
checksum = "3742b2c103b9f06bc9fff0a37ff4912935851bee6d36f3c02bcc755bcfec228f"
dependencies = [
 "lock_api",
 "parking_lot_core 0.9.9",
]

[[package]]
name = "parking_lot_core"
version = "0.8.6"
source = "registry+https://github.com/rust-lang/crates.io-index"
checksum = "60a2cfe6f0ad2bfc16aefa463b497d5c7a5ecd44a23efa72aa342d90177356dc"
dependencies = [
 "cfg-if 1.0.0",
 "instant",
 "libc",
 "redox_syscall 0.2.16",
 "smallvec",
 "winapi",
]

[[package]]
name = "parking_lot_core"
version = "0.9.9"
source = "registry+https://github.com/rust-lang/crates.io-index"
checksum = "4c42a9226546d68acdd9c0a280d17ce19bfe27a46bf68784e4066115788d008e"
dependencies = [
 "cfg-if 1.0.0",
 "libc",
 "redox_syscall 0.4.1",
 "smallvec",
 "windows-targets 0.48.5",
]

[[package]]
name = "parse-zoneinfo"
version = "0.3.0"
source = "registry+https://github.com/rust-lang/crates.io-index"
checksum = "c705f256449c60da65e11ff6626e0c16a0a0b96aaa348de61376b249bc340f41"
dependencies = [
 "regex",
]

[[package]]
name = "password-hash"
version = "0.4.2"
source = "registry+https://github.com/rust-lang/crates.io-index"
checksum = "7676374caaee8a325c9e7a2ae557f216c5563a171d6997b0ef8a65af35147700"
dependencies = [
 "base64ct",
 "rand_core 0.6.4",
 "subtle",
]

[[package]]
name = "paste"
version = "1.0.14"
source = "registry+https://github.com/rust-lang/crates.io-index"
checksum = "de3145af08024dea9fa9914f381a17b8fc6034dfb00f3a84013f7ff43f29ed4c"

[[package]]
name = "path-slash"
version = "0.2.1"
source = "registry+https://github.com/rust-lang/crates.io-index"
checksum = "1e91099d4268b0e11973f036e885d652fb0b21fedcf69738c627f94db6a44f42"

[[package]]
name = "pathdiff"
version = "0.2.1"
source = "registry+https://github.com/rust-lang/crates.io-index"
checksum = "8835116a5c179084a830efb3adc117ab007512b535bc1a21c991d3b32a6b44dd"
dependencies = [
 "camino",
]

[[package]]
name = "pbkdf2"
version = "0.11.0"
source = "registry+https://github.com/rust-lang/crates.io-index"
checksum = "83a0692ec44e4cf1ef28ca317f14f8f07da2d95ec3fa01f86e4467b725e60917"
dependencies = [
 "digest 0.10.7",
 "hmac 0.12.1",
 "password-hash",
 "sha2 0.10.8",
]

[[package]]
name = "pbkdf2"
version = "0.12.2"
source = "registry+https://github.com/rust-lang/crates.io-index"
checksum = "f8ed6a7761f76e3b9f92dfb0a60a6a6477c61024b775147ff0973a02653abaf2"
dependencies = [
 "digest 0.10.7",
 "hmac 0.12.1",
]

[[package]]
name = "pear"
version = "0.2.9"
source = "registry+https://github.com/rust-lang/crates.io-index"
checksum = "bdeeaa00ce488657faba8ebf44ab9361f9365a97bd39ffb8a60663f57ff4b467"
dependencies = [
 "inlinable_string",
 "pear_codegen",
 "yansi 1.0.1",
]

[[package]]
name = "pear_codegen"
version = "0.2.9"
source = "registry+https://github.com/rust-lang/crates.io-index"
checksum = "4bab5b985dc082b345f812b7df84e1bef27e7207b39e448439ba8bd69c93f147"
dependencies = [
 "proc-macro2 1.0.79",
 "proc-macro2-diagnostics",
 "quote 1.0.35",
 "syn 2.0.57",
]

[[package]]
name = "peeking_take_while"
version = "0.1.2"
source = "registry+https://github.com/rust-lang/crates.io-index"
checksum = "19b17cddbe7ec3f8bc800887bab5e717348c95ea2ca0b1bf0837fb964dc67099"

[[package]]
name = "pem"
version = "1.1.1"
source = "registry+https://github.com/rust-lang/crates.io-index"
checksum = "a8835c273a76a90455d7344889b0964598e3316e2a79ede8e36f16bdcf2228b8"
dependencies = [
 "base64 0.13.1",
]

[[package]]
name = "pem-rfc7468"
version = "0.6.0"
source = "registry+https://github.com/rust-lang/crates.io-index"
checksum = "24d159833a9105500e0398934e205e0773f0b27529557134ecfc51c27646adac"
dependencies = [
 "base64ct",
]

[[package]]
name = "pem-rfc7468"
version = "0.7.0"
source = "registry+https://github.com/rust-lang/crates.io-index"
checksum = "88b39c9bfcfc231068454382784bb460aae594343fb030d46e9f50a645418412"
dependencies = [
 "base64ct",
]

[[package]]
name = "percent-encoding"
version = "2.3.1"
source = "registry+https://github.com/rust-lang/crates.io-index"
checksum = "e3148f5046208a5d56bcfc03053e3ca6334e51da8dfb19b6cdc8b306fae3283e"

[[package]]
name = "permutator"
version = "0.4.3"
source = "registry+https://github.com/rust-lang/crates.io-index"
checksum = "a9c6f38fc32835c34be344aa470f8f198b1788986eab65fc2a04d25a6f2510d6"
dependencies = [
 "num 0.2.1",
]

[[package]]
name = "pest"
version = "2.7.8"
source = "registry+https://github.com/rust-lang/crates.io-index"
checksum = "56f8023d0fb78c8e03784ea1c7f3fa36e68a723138990b8d5a47d916b651e7a8"
dependencies = [
 "memchr",
 "thiserror",
 "ucd-trie",
]

[[package]]
name = "pest_derive"
version = "2.7.8"
source = "registry+https://github.com/rust-lang/crates.io-index"
checksum = "b0d24f72393fd16ab6ac5738bc33cdb6a9aa73f8b902e8fe29cf4e67d7dd1026"
dependencies = [
 "pest",
 "pest_generator",
]

[[package]]
name = "pest_generator"
version = "2.7.8"
source = "registry+https://github.com/rust-lang/crates.io-index"
checksum = "fdc17e2a6c7d0a492f0158d7a4bd66cc17280308bbaff78d5bef566dca35ab80"
dependencies = [
 "pest",
 "pest_meta",
 "proc-macro2 1.0.79",
 "quote 1.0.35",
 "syn 2.0.57",
]

[[package]]
name = "pest_meta"
version = "2.7.8"
source = "registry+https://github.com/rust-lang/crates.io-index"
checksum = "934cd7631c050f4674352a6e835d5f6711ffbfb9345c2fc0107155ac495ae293"
dependencies = [
 "once_cell",
 "pest",
 "sha2 0.10.8",
]

[[package]]
name = "petgraph"
version = "0.5.1"
source = "registry+https://github.com/rust-lang/crates.io-index"
checksum = "467d164a6de56270bd7c4d070df81d07beace25012d5103ced4e9ff08d6afdb7"
dependencies = [
 "fixedbitset 0.2.0",
 "indexmap 1.9.3",
]

[[package]]
name = "petgraph"
version = "0.6.4"
source = "registry+https://github.com/rust-lang/crates.io-index"
checksum = "e1d3afd2628e69da2be385eb6f2fd57c8ac7977ceeff6dc166ff1657b0e386a9"
dependencies = [
 "fixedbitset 0.4.2",
 "indexmap 2.2.6",
]

[[package]]
name = "pharos"
version = "0.5.3"
source = "registry+https://github.com/rust-lang/crates.io-index"
checksum = "e9567389417feee6ce15dd6527a8a1ecac205ef62c2932bcf3d9f6fc5b78b414"
dependencies = [
 "futures",
 "rustc_version 0.4.0",
]

[[package]]
name = "phf"
version = "0.11.2"
source = "registry+https://github.com/rust-lang/crates.io-index"
checksum = "ade2d8b8f33c7333b51bcf0428d37e217e9f32192ae4772156f65063b8ce03dc"
dependencies = [
 "phf_macros",
 "phf_shared 0.11.2",
]

[[package]]
name = "phf_codegen"
version = "0.11.2"
source = "registry+https://github.com/rust-lang/crates.io-index"
checksum = "e8d39688d359e6b34654d328e262234662d16cc0f60ec8dcbe5e718709342a5a"
dependencies = [
 "phf_generator",
 "phf_shared 0.11.2",
]

[[package]]
name = "phf_generator"
version = "0.11.2"
source = "registry+https://github.com/rust-lang/crates.io-index"
checksum = "48e4cc64c2ad9ebe670cb8fd69dd50ae301650392e81c05f9bfcb2d5bdbc24b0"
dependencies = [
 "phf_shared 0.11.2",
 "rand 0.8.5",
]

[[package]]
name = "phf_macros"
version = "0.11.2"
source = "registry+https://github.com/rust-lang/crates.io-index"
checksum = "3444646e286606587e49f3bcf1679b8cef1dc2c5ecc29ddacaffc305180d464b"
dependencies = [
 "phf_generator",
 "phf_shared 0.11.2",
 "proc-macro2 1.0.79",
 "quote 1.0.35",
 "syn 2.0.57",
]

[[package]]
name = "phf_shared"
version = "0.10.0"
source = "registry+https://github.com/rust-lang/crates.io-index"
checksum = "b6796ad771acdc0123d2a88dc428b5e38ef24456743ddb1744ed628f9815c096"
dependencies = [
 "siphasher",
]

[[package]]
name = "phf_shared"
version = "0.11.2"
source = "registry+https://github.com/rust-lang/crates.io-index"
checksum = "90fcb95eef784c2ac79119d1dd819e162b5da872ce6f3c3abe1e8ca1c082f72b"
dependencies = [
 "siphasher",
 "uncased",
]

[[package]]
name = "pin-project"
version = "1.1.5"
source = "registry+https://github.com/rust-lang/crates.io-index"
checksum = "b6bf43b791c5b9e34c3d182969b4abb522f9343702850a2e57f460d00d09b4b3"
dependencies = [
 "pin-project-internal",
]

[[package]]
name = "pin-project-internal"
version = "1.1.5"
source = "registry+https://github.com/rust-lang/crates.io-index"
checksum = "2f38a4412a78282e09a2cf38d195ea5420d15ba0602cb375210efbc877243965"
dependencies = [
 "proc-macro2 1.0.79",
 "quote 1.0.35",
 "syn 2.0.57",
]

[[package]]
name = "pin-project-lite"
version = "0.2.14"
source = "registry+https://github.com/rust-lang/crates.io-index"
checksum = "bda66fc9667c18cb2758a2ac84d1167245054bcf85d5d1aaa6923f45801bdd02"

[[package]]
name = "pin-utils"
version = "0.1.0"
source = "registry+https://github.com/rust-lang/crates.io-index"
checksum = "8b870d8c151b6f2fb93e84a13146138f05d02ed11c7e7c54f8826aaaf7c9f184"

[[package]]
name = "piper"
version = "0.2.1"
source = "registry+https://github.com/rust-lang/crates.io-index"
checksum = "668d31b1c4eba19242f2088b2bf3316b82ca31082a8335764db4e083db7485d4"
dependencies = [
 "atomic-waker",
 "fastrand 2.0.2",
 "futures-io",
]

[[package]]
name = "pkcs1"
version = "0.4.1"
source = "registry+https://github.com/rust-lang/crates.io-index"
checksum = "eff33bdbdfc54cc98a2eca766ebdec3e1b8fb7387523d5c9c9a2891da856f719"
dependencies = [
 "der 0.6.1",
 "pkcs8 0.9.0",
 "spki 0.6.0",
 "zeroize",
]

[[package]]
name = "pkcs8"
version = "0.9.0"
source = "registry+https://github.com/rust-lang/crates.io-index"
checksum = "9eca2c590a5f85da82668fa685c09ce2888b9430e83299debf1f34b65fd4a4ba"
dependencies = [
 "der 0.6.1",
 "spki 0.6.0",
]

[[package]]
name = "pkcs8"
version = "0.10.2"
source = "registry+https://github.com/rust-lang/crates.io-index"
checksum = "f950b2377845cebe5cf8b5165cb3cc1a5e0fa5cfa3e1f7f55707d8fd82e0a7b7"
dependencies = [
 "der 0.7.8",
 "spki 0.7.3",
]

[[package]]
name = "pkg-config"
version = "0.3.30"
source = "registry+https://github.com/rust-lang/crates.io-index"
checksum = "d231b230927b5e4ad203db57bbcbee2802f6bce620b1e4a9024a07d94e2907ec"

[[package]]
name = "plotters"
version = "0.3.5"
source = "registry+https://github.com/rust-lang/crates.io-index"
checksum = "d2c224ba00d7cadd4d5c660deaf2098e5e80e07846537c51f9cfa4be50c1fd45"
dependencies = [
 "num-traits",
 "plotters-backend",
 "plotters-svg",
 "wasm-bindgen",
 "web-sys",
]

[[package]]
name = "plotters-backend"
version = "0.3.5"
source = "registry+https://github.com/rust-lang/crates.io-index"
checksum = "9e76628b4d3a7581389a35d5b6e2139607ad7c75b17aed325f210aa91f4a9609"

[[package]]
name = "plotters-svg"
version = "0.3.5"
source = "registry+https://github.com/rust-lang/crates.io-index"
checksum = "38f6d39893cca0701371e3c27294f09797214b86f1fb951b89ade8ec04e2abab"
dependencies = [
 "plotters-backend",
]

[[package]]
name = "polyval"
version = "0.6.2"
source = "registry+https://github.com/rust-lang/crates.io-index"
checksum = "9d1fe60d06143b2430aa532c94cfe9e29783047f06c0d7fd359a9a51b729fa25"
dependencies = [
 "cfg-if 1.0.0",
 "cpufeatures",
 "opaque-debug",
 "universal-hash",
]

[[package]]
name = "portable-atomic"
version = "0.3.20"
source = "registry+https://github.com/rust-lang/crates.io-index"
checksum = "e30165d31df606f5726b090ec7592c308a0eaf61721ff64c9a3018e344a8753e"
dependencies = [
 "portable-atomic 1.6.0",
]

[[package]]
name = "portable-atomic"
version = "1.6.0"
source = "registry+https://github.com/rust-lang/crates.io-index"
checksum = "7170ef9988bc169ba16dd36a7fa041e5c4cbeb6a35b76d4c03daded371eae7c0"

[[package]]
name = "poseidon-ark"
version = "0.0.1"
source = "git+https://github.com/arnaucube/poseidon-ark.git?rev=bf96de3b946e8b343c6b65412bae92f8d32251ad#bf96de3b946e8b343c6b65412bae92f8d32251ad"
dependencies = [
 "ark-bn254",
 "ark-ff 0.4.2",
 "ark-std 0.4.0",
]

[[package]]
name = "postcard"
version = "1.0.8"
source = "registry+https://github.com/rust-lang/crates.io-index"
checksum = "a55c51ee6c0db07e68448e336cf8ea4131a620edefebf9893e759b2d793420f8"
dependencies = [
 "cobs",
 "embedded-io",
 "serde",
]

[[package]]
name = "powerfmt"
version = "0.2.0"
source = "registry+https://github.com/rust-lang/crates.io-index"
checksum = "439ee305def115ba05938db6eb1644ff94165c5ab5e9420d1c1bcedbba909391"

[[package]]
name = "pprof"
version = "0.11.1"
source = "registry+https://github.com/rust-lang/crates.io-index"
checksum = "196ded5d4be535690899a4631cc9f18cdc41b7ebf24a79400f46f48e49a11059"
dependencies = [
 "backtrace",
 "cfg-if 1.0.0",
 "criterion",
 "findshlibs",
 "inferno",
 "libc",
 "log",
 "nix 0.26.4",
 "once_cell",
 "parking_lot 0.12.1",
 "smallvec",
 "symbolic-demangle",
 "tempfile",
 "thiserror",
]

[[package]]
name = "ppv-lite86"
version = "0.2.17"
source = "registry+https://github.com/rust-lang/crates.io-index"
checksum = "5b40af805b3121feab8a3c29f04d8ad262fa8e0561883e7653e024ae4479e6de"

[[package]]
name = "pq-sys"
version = "0.4.8"
source = "registry+https://github.com/rust-lang/crates.io-index"
checksum = "31c0052426df997c0cbd30789eb44ca097e3541717a7b8fa36b1c464ee7edebd"
dependencies = [
 "vcpkg",
]

[[package]]
name = "precomputed-hash"
version = "0.1.1"
source = "registry+https://github.com/rust-lang/crates.io-index"
checksum = "925383efa346730478fb4838dbe9137d2a47675ad789c546d150a6e1dd4ab31c"

[[package]]
name = "predicates"
version = "2.1.5"
source = "registry+https://github.com/rust-lang/crates.io-index"
checksum = "59230a63c37f3e18569bdb90e4a89cbf5bf8b06fea0b84e65ea10cc4df47addd"
dependencies = [
 "difflib",
 "float-cmp",
 "itertools 0.10.5",
 "normalize-line-endings",
 "predicates-core",
 "regex",
]

[[package]]
name = "predicates"
version = "3.1.0"
source = "registry+https://github.com/rust-lang/crates.io-index"
checksum = "68b87bfd4605926cdfefc1c3b5f8fe560e3feca9d5552cf68c466d3d8236c7e8"
dependencies = [
 "anstyle",
 "difflib",
 "predicates-core",
]

[[package]]
name = "predicates-core"
version = "1.0.6"
source = "registry+https://github.com/rust-lang/crates.io-index"
checksum = "b794032607612e7abeb4db69adb4e33590fa6cf1149e95fd7cb00e634b92f174"

[[package]]
name = "predicates-tree"
version = "1.0.9"
source = "registry+https://github.com/rust-lang/crates.io-index"
checksum = "368ba315fb8c5052ab692e68a0eefec6ec57b23a36959c14496f0b0df2c0cecf"
dependencies = [
 "predicates-core",
 "termtree",
]

[[package]]
name = "pretty"
version = "0.10.0"
source = "registry+https://github.com/rust-lang/crates.io-index"
checksum = "ad9940b913ee56ddd94aec2d3cd179dd47068236f42a1a6415ccf9d880ce2a61"
dependencies = [
 "arrayvec 0.5.2",
 "typed-arena",
]

[[package]]
name = "pretty_assertions"
version = "1.4.0"
source = "registry+https://github.com/rust-lang/crates.io-index"
checksum = "af7cee1a6c8a5b9208b3cb1061f10c0cb689087b3d8ce85fb9d2dd7a29b6ba66"
dependencies = [
 "diff",
 "yansi 0.5.1",
]

[[package]]
name = "prettyplease"
version = "0.1.25"
source = "registry+https://github.com/rust-lang/crates.io-index"
checksum = "6c8646e95016a7a6c4adea95bafa8a16baab64b583356217f2c85db4a39d9a86"
dependencies = [
 "proc-macro2 1.0.79",
 "syn 1.0.109",
]

[[package]]
name = "prettyplease"
version = "0.2.17"
source = "registry+https://github.com/rust-lang/crates.io-index"
checksum = "8d3928fb5db768cb86f891ff014f0144589297e3c6a1aba6ed7cecfdace270c7"
dependencies = [
 "proc-macro2 1.0.79",
 "syn 2.0.57",
]

[[package]]
name = "prettytable-rs"
version = "0.10.0"
source = "registry+https://github.com/rust-lang/crates.io-index"
checksum = "eea25e07510aa6ab6547308ebe3c036016d162b8da920dbb079e3ba8acf3d95a"
dependencies = [
 "csv",
 "encode_unicode 1.0.0",
 "is-terminal",
 "lazy_static",
 "term",
 "unicode-width",
]

[[package]]
name = "primeorder"
version = "0.13.6"
source = "registry+https://github.com/rust-lang/crates.io-index"
checksum = "353e1ca18966c16d9deb1c69278edbc5f194139612772bd9537af60ac231e1e6"
dependencies = [
 "elliptic-curve 0.13.8",
]

[[package]]
name = "primitive-types"
version = "0.10.1"
source = "registry+https://github.com/rust-lang/crates.io-index"
checksum = "05e4722c697a58a99d5d06a08c30821d7c082a4632198de1eaa5a6c22ef42373"
dependencies = [
 "fixed-hash 0.7.0",
 "impl-codec 0.5.1",
 "impl-serde 0.3.2",
 "uint",
]

[[package]]
name = "primitive-types"
version = "0.12.2"
source = "registry+https://github.com/rust-lang/crates.io-index"
checksum = "0b34d9fd68ae0b74a41b21c03c2f62847aa0ffea044eee893b4c140b37e244e2"
dependencies = [
 "fixed-hash 0.8.0",
 "impl-codec 0.6.0",
 "impl-rlp",
 "impl-serde 0.4.0",
 "scale-info",
 "uint",
]

[[package]]
name = "proc-macro-crate"
version = "1.1.3"
source = "registry+https://github.com/rust-lang/crates.io-index"
checksum = "e17d47ce914bf4de440332250b0edd23ce48c005f59fab39d3335866b114f11a"
dependencies = [
 "thiserror",
 "toml 0.5.11",
]

[[package]]
name = "proc-macro-crate"
version = "2.0.0"
source = "registry+https://github.com/rust-lang/crates.io-index"
checksum = "7e8366a6159044a37876a2b9817124296703c586a5c92e2c53751fa06d8d43e8"
dependencies = [
 "toml_edit 0.20.7",
]

[[package]]
name = "proc-macro-crate"
version = "3.1.0"
source = "registry+https://github.com/rust-lang/crates.io-index"
checksum = "6d37c51ca738a55da99dc0c4a34860fd675453b8b36209178c2249bb13651284"
dependencies = [
 "toml_edit 0.21.1",
]

[[package]]
name = "proc-macro-error"
version = "1.0.4"
source = "registry+https://github.com/rust-lang/crates.io-index"
checksum = "da25490ff9892aab3fcf7c36f08cfb902dd3e71ca0f9f9517bea02a73a5ce38c"
dependencies = [
 "proc-macro-error-attr",
 "proc-macro2 1.0.79",
 "quote 1.0.35",
 "syn 1.0.109",
 "version_check",
]

[[package]]
name = "proc-macro-error-attr"
version = "1.0.4"
source = "registry+https://github.com/rust-lang/crates.io-index"
checksum = "a1be40180e52ecc98ad80b184934baf3d0d29f979574e439af5a55274b35f869"
dependencies = [
 "proc-macro2 1.0.79",
 "quote 1.0.35",
 "version_check",
]

[[package]]
name = "proc-macro-hack"
version = "0.5.20+deprecated"
source = "registry+https://github.com/rust-lang/crates.io-index"
checksum = "dc375e1527247fe1a97d8b7156678dfe7c1af2fc075c9a4db3690ecd2a148068"

[[package]]
name = "proc-macro2"
version = "0.4.30"
source = "registry+https://github.com/rust-lang/crates.io-index"
checksum = "cf3d2011ab5c909338f7887f4fc896d35932e29146c12c8d01da6b22a80ba759"
dependencies = [
 "unicode-xid 0.1.0",
]

[[package]]
name = "proc-macro2"
version = "1.0.79"
source = "registry+https://github.com/rust-lang/crates.io-index"
checksum = "e835ff2298f5721608eb1a980ecaee1aef2c132bf95ecc026a11b7bf3c01c02e"
dependencies = [
 "unicode-ident",
]

[[package]]
name = "proc-macro2-diagnostics"
version = "0.10.1"
source = "registry+https://github.com/rust-lang/crates.io-index"
checksum = "af066a9c399a26e020ada66a034357a868728e72cd426f3adcd35f80d88d88c8"
dependencies = [
 "proc-macro2 1.0.79",
 "quote 1.0.35",
 "syn 2.0.57",
 "version_check",
 "yansi 1.0.1",
]

[[package]]
name = "procinfo"
version = "0.4.2"
source = "registry+https://github.com/rust-lang/crates.io-index"
checksum = "6ab1427f3d2635891f842892dda177883dca0639e05fe66796a62c9d2f23b49c"
dependencies = [
 "byteorder",
 "libc",
 "nom 2.2.1",
 "rustc_version 0.2.3",
]

[[package]]
name = "prometheus"
version = "0.13.3"
source = "registry+https://github.com/rust-lang/crates.io-index"
checksum = "449811d15fbdf5ceb5c1144416066429cf82316e2ec8ce0c1f6f8a02e7bbcf8c"
dependencies = [
 "cfg-if 1.0.0",
 "fnv",
 "lazy_static",
 "memchr",
 "parking_lot 0.12.1",
 "protobuf 2.28.0",
 "thiserror",
]

[[package]]
name = "prometheus-closure-metric"
version = "0.1.0"
source = "git+https://github.com/fuzzland/ityfuzz-sui-fork.git#a88e9595de3c04a540c059d5f4b6d148419c508c"
dependencies = [
 "anyhow",
 "prometheus",
 "protobuf 2.28.0",
 "workspace-hack",
]

[[package]]
name = "prometheus-http-query"
version = "0.6.7"
source = "registry+https://github.com/rust-lang/crates.io-index"
checksum = "6704e3a7a78545b1496524d518658005a6cc308abc90ce5fccf01891ecdc298b"
dependencies = [
 "mime",
 "reqwest",
 "serde",
 "serde_json",
 "time 0.3.34",
 "url",
]

[[package]]
name = "proptest"
version = "1.4.0"
source = "registry+https://github.com/rust-lang/crates.io-index"
checksum = "31b476131c3c86cb68032fdc5cb6d5a1045e3e42d96b69fa599fd77701e1f5bf"
dependencies = [
 "bit-set",
 "bit-vec",
 "bitflags 2.5.0",
 "lazy_static",
 "num-traits",
 "rand 0.8.5",
 "rand_chacha 0.3.1",
 "rand_xorshift",
 "regex-syntax 0.8.3",
 "rusty-fork",
 "tempfile",
 "unarray",
]

[[package]]
name = "proptest-derive"
version = "0.3.0"
source = "registry+https://github.com/rust-lang/crates.io-index"
checksum = "90b46295382dc76166cb7cf2bb4a97952464e4b7ed5a43e6cd34e1fec3349ddc"
dependencies = [
 "proc-macro2 0.4.30",
 "quote 0.6.13",
 "syn 0.15.44",
]

[[package]]
name = "proptest-derive"
version = "0.4.0"
source = "registry+https://github.com/rust-lang/crates.io-index"
checksum = "9cf16337405ca084e9c78985114633b6827711d22b9e6ef6c6c0d665eb3f0b6e"
dependencies = [
 "proc-macro2 1.0.79",
 "quote 1.0.35",
 "syn 1.0.109",
]

[[package]]
name = "prost"
version = "0.11.9"
source = "registry+https://github.com/rust-lang/crates.io-index"
checksum = "0b82eaa1d779e9a4bc1c3217db8ffbeabaae1dca241bf70183242128d48681cd"
dependencies = [
 "bytes",
 "prost-derive",
]

[[package]]
name = "prost-build"
version = "0.11.9"
source = "registry+https://github.com/rust-lang/crates.io-index"
checksum = "119533552c9a7ffacc21e099c24a0ac8bb19c2a2a3f363de84cd9b844feab270"
dependencies = [
 "bytes",
 "heck 0.4.1",
 "itertools 0.10.5",
 "lazy_static",
 "log",
 "multimap",
 "petgraph 0.6.4",
 "prettyplease 0.1.25",
 "prost",
 "prost-types",
 "regex",
 "syn 1.0.109",
 "tempfile",
 "which",
]

[[package]]
name = "prost-derive"
version = "0.11.9"
source = "registry+https://github.com/rust-lang/crates.io-index"
checksum = "e5d2d8d10f3c6ded6da8b05b5fb3b8a5082514344d56c9f871412d29b4e075b4"
dependencies = [
 "anyhow",
 "itertools 0.10.5",
 "proc-macro2 1.0.79",
 "quote 1.0.35",
 "syn 1.0.109",
]

[[package]]
name = "prost-types"
version = "0.11.9"
source = "registry+https://github.com/rust-lang/crates.io-index"
checksum = "213622a1460818959ac1181aaeb2dc9c7f63df720db7d788b3e24eacd1983e13"
dependencies = [
 "prost",
]

[[package]]
name = "protobuf"
version = "2.28.0"
source = "registry+https://github.com/rust-lang/crates.io-index"
checksum = "106dd99e98437432fed6519dedecfade6a06a73bb7b2a1e019fdd2bee5778d94"
dependencies = [
 "bytes",
]

[[package]]
name = "protobuf"
version = "3.3.0"
source = "registry+https://github.com/rust-lang/crates.io-index"
checksum = "b65f4a8ec18723a734e5dc09c173e0abf9690432da5340285d536edcb4dac190"
dependencies = [
 "once_cell",
 "protobuf-support",
 "thiserror",
]

[[package]]
name = "protobuf-src"
version = "1.1.0+21.5"
source = "registry+https://github.com/rust-lang/crates.io-index"
checksum = "c7ac8852baeb3cc6fb83b93646fb93c0ffe5d14bf138c945ceb4b9948ee0e3c1"
dependencies = [
 "autotools",
]

[[package]]
name = "protobuf-support"
version = "3.3.0"
source = "registry+https://github.com/rust-lang/crates.io-index"
checksum = "6872f4d4f4b98303239a2b5838f5bbbb77b01ffc892d627957f37a22d7cfe69c"
dependencies = [
 "thiserror",
]

[[package]]
name = "quanta"
version = "0.9.3"
source = "registry+https://github.com/rust-lang/crates.io-index"
checksum = "20afe714292d5e879d8b12740aa223c6a88f118af41870e8b6196e39a02238a8"
dependencies = [
 "crossbeam-utils",
 "libc",
 "mach",
 "once_cell",
 "raw-cpuid",
 "wasi 0.10.2+wasi-snapshot-preview1",
 "web-sys",
 "winapi",
]

[[package]]
name = "quick-error"
version = "1.2.3"
source = "registry+https://github.com/rust-lang/crates.io-index"
checksum = "a1d01941d82fa2ab50be1e79e6714289dd7cde78eba4c074bc5a4374f650dfe0"

[[package]]
name = "quick-error"
version = "2.0.1"
source = "registry+https://github.com/rust-lang/crates.io-index"
checksum = "a993555f31e5a609f617c12db6250dedcac1b0a85076912c436e6fc9b2c8e6a3"

[[package]]
name = "quick-xml"
version = "0.26.0"
source = "registry+https://github.com/rust-lang/crates.io-index"
checksum = "7f50b1c63b38611e7d4d7f68b82d3ad0cc71a2ad2e7f61fc10f1328d917c93cd"
dependencies = [
 "memchr",
]

[[package]]
name = "quinn"
version = "0.10.2"
source = "registry+https://github.com/rust-lang/crates.io-index"
checksum = "8cc2c5017e4b43d5995dcea317bc46c1e09404c0a9664d2908f7f02dfe943d75"
dependencies = [
 "bytes",
 "futures-io",
 "pin-project-lite",
 "quinn-proto",
 "quinn-udp",
 "rustc-hash",
 "rustls 0.21.10",
 "thiserror",
 "tokio",
 "tracing",
]

[[package]]
name = "quinn-proto"
version = "0.10.6"
source = "registry+https://github.com/rust-lang/crates.io-index"
checksum = "141bf7dfde2fbc246bfd3fe12f2455aa24b0fbd9af535d8c86c7bd1381ff2b1a"
dependencies = [
 "bytes",
 "rand 0.8.5",
 "ring 0.16.20",
 "rustc-hash",
 "rustls 0.21.10",
 "slab",
 "thiserror",
 "tinyvec",
 "tracing",
]

[[package]]
name = "quinn-udp"
version = "0.4.1"
source = "registry+https://github.com/rust-lang/crates.io-index"
checksum = "055b4e778e8feb9f93c4e439f71dc2156ef13360b432b799e179a8c4cdf0b1d7"
dependencies = [
 "bytes",
 "libc",
 "socket2 0.5.6",
 "tracing",
 "windows-sys 0.48.0",
]

[[package]]
name = "quote"
version = "0.6.13"
source = "registry+https://github.com/rust-lang/crates.io-index"
checksum = "6ce23b6b870e8f94f81fb0a363d65d86675884b34a09043c81e5562f11c1f8e1"
dependencies = [
 "proc-macro2 0.4.30",
]

[[package]]
name = "quote"
version = "1.0.35"
source = "registry+https://github.com/rust-lang/crates.io-index"
checksum = "291ec9ab5efd934aaf503a6466c5d5251535d108ee747472c3977cc5acc868ef"
dependencies = [
 "proc-macro2 1.0.79",
]

[[package]]
name = "r2d2"
version = "0.8.10"
source = "registry+https://github.com/rust-lang/crates.io-index"
checksum = "51de85fb3fb6524929c8a2eb85e6b6d363de4e8c48f9e2c2eac4944abc181c93"
dependencies = [
 "log",
 "parking_lot 0.12.1",
 "scheduled-thread-pool",
]

[[package]]
name = "radium"
version = "0.6.2"
source = "registry+https://github.com/rust-lang/crates.io-index"
checksum = "643f8f41a8ebc4c5dc4515c82bb8abd397b527fc20fd681b7c011c2aee5d44fb"

[[package]]
name = "radium"
version = "0.7.0"
source = "registry+https://github.com/rust-lang/crates.io-index"
checksum = "dc33ff2d4973d518d823d61aa239014831e521c75da58e3df4840d3f47749d09"

[[package]]
name = "radix_trie"
version = "0.2.1"
source = "registry+https://github.com/rust-lang/crates.io-index"
checksum = "c069c179fcdc6a2fe24d8d18305cf085fdbd4f922c041943e203685d6a1c58fd"
dependencies = [
 "endian-type",
 "nibble_vec",
]

[[package]]
name = "rand"
version = "0.3.23"
source = "registry+https://github.com/rust-lang/crates.io-index"
checksum = "64ac302d8f83c0c1974bf758f6b041c6c8ada916fbb44a609158ca8b064cc76c"
dependencies = [
 "libc",
 "rand 0.4.6",
]

[[package]]
name = "rand"
version = "0.4.6"
source = "registry+https://github.com/rust-lang/crates.io-index"
checksum = "552840b97013b1a26992c11eac34bdd778e464601a4c2054b5f0bff7c6761293"
dependencies = [
 "fuchsia-cprng",
 "libc",
 "rand_core 0.3.1",
 "rdrand",
 "winapi",
]

[[package]]
name = "rand"
version = "0.7.3"
source = "registry+https://github.com/rust-lang/crates.io-index"
checksum = "6a6b1679d49b24bbfe0c803429aa1874472f50d9b363131f0e89fc356b544d03"
dependencies = [
 "getrandom 0.1.16",
 "libc",
 "rand_chacha 0.2.2",
 "rand_core 0.5.1",
 "rand_hc",
]

[[package]]
name = "rand"
version = "0.8.5"
source = "registry+https://github.com/rust-lang/crates.io-index"
checksum = "34af8d1a0e25924bc5b7c43c079c942339d8f0a8b57c39049bef581b46327404"
dependencies = [
 "libc",
 "rand_chacha 0.3.1",
 "rand_core 0.6.4",
]

[[package]]
name = "rand_chacha"
version = "0.2.2"
source = "registry+https://github.com/rust-lang/crates.io-index"
checksum = "f4c8ed856279c9737206bf725bf36935d8666ead7aa69b52be55af369d193402"
dependencies = [
 "ppv-lite86",
 "rand_core 0.5.1",
]

[[package]]
name = "rand_chacha"
version = "0.3.1"
source = "registry+https://github.com/rust-lang/crates.io-index"
checksum = "e6c10a63a0fa32252be49d21e7709d4d4baf8d231c2dbce1eaa8141b9b127d88"
dependencies = [
 "ppv-lite86",
 "rand_core 0.6.4",
]

[[package]]
name = "rand_core"
version = "0.3.1"
source = "registry+https://github.com/rust-lang/crates.io-index"
checksum = "7a6fdeb83b075e8266dcc8762c22776f6877a63111121f5f8c7411e5be7eed4b"
dependencies = [
 "rand_core 0.4.2",
]

[[package]]
name = "rand_core"
version = "0.4.2"
source = "registry+https://github.com/rust-lang/crates.io-index"
checksum = "9c33a3c44ca05fa6f1807d8e6743f3824e8509beca625669633be0acbdf509dc"

[[package]]
name = "rand_core"
version = "0.5.1"
source = "registry+https://github.com/rust-lang/crates.io-index"
checksum = "90bde5296fc891b0cef12a6d03ddccc162ce7b2aff54160af9338f8d40df6d19"
dependencies = [
 "getrandom 0.1.16",
]

[[package]]
name = "rand_core"
version = "0.6.4"
source = "registry+https://github.com/rust-lang/crates.io-index"
checksum = "ec0be4795e2f6a28069bec0b5ff3e2ac9bafc99e6a9a7dc3547996c5c816922c"
dependencies = [
 "getrandom 0.2.12",
]

[[package]]
name = "rand_hc"
version = "0.2.0"
source = "registry+https://github.com/rust-lang/crates.io-index"
checksum = "ca3129af7b92a17112d59ad498c6f81eaf463253766b90396d39ea7a39d6613c"
dependencies = [
 "rand_core 0.5.1",
]

[[package]]
name = "rand_xorshift"
version = "0.3.0"
source = "registry+https://github.com/rust-lang/crates.io-index"
checksum = "d25bf25ec5ae4a3f1b92f929810509a2f53d7dca2f50b794ff57e3face536c8f"
dependencies = [
 "rand_core 0.6.4",
]

[[package]]
name = "rand_xoshiro"
version = "0.6.0"
source = "registry+https://github.com/rust-lang/crates.io-index"
checksum = "6f97cdb2a36ed4183de61b2f824cc45c9f1037f28afe0a322e9fff4c108b5aaa"
dependencies = [
 "rand_core 0.6.4",
]

[[package]]
name = "ratatui"
version = "0.23.0"
source = "registry+https://github.com/rust-lang/crates.io-index"
checksum = "2e2e4cd95294a85c3b4446e63ef054eea43e0205b1fd60120c16b74ff7ff96ad"
dependencies = [
 "bitflags 2.5.0",
 "cassowary",
 "crossterm 0.27.0",
 "indoc",
 "itertools 0.11.0",
 "paste",
 "strum 0.25.0",
 "unicode-segmentation",
 "unicode-width",
]

[[package]]
name = "raw-cpuid"
version = "10.7.0"
source = "registry+https://github.com/rust-lang/crates.io-index"
checksum = "6c297679cb867470fa8c9f67dbba74a78d78e3e98d7cf2b08d6d71540f797332"
dependencies = [
 "bitflags 1.3.2",
]

[[package]]
name = "rayon"
version = "1.10.0"
source = "registry+https://github.com/rust-lang/crates.io-index"
checksum = "b418a60154510ca1a002a752ca9714984e21e4241e804d32555251faf8b78ffa"
dependencies = [
 "either",
 "rayon-core",
]

[[package]]
name = "rayon-core"
version = "1.12.1"
source = "registry+https://github.com/rust-lang/crates.io-index"
checksum = "1465873a3dfdaa8ae7cb14b4383657caab0b3e8a0aa9ae8e04b044854c8dfce2"
dependencies = [
 "crossbeam-deque",
 "crossbeam-utils",
]

[[package]]
name = "rcgen"
version = "0.9.3"
source = "registry+https://github.com/rust-lang/crates.io-index"
checksum = "6413f3de1edee53342e6138e75b56d32e7bc6e332b3bd62d497b1929d4cfbcdd"
dependencies = [
 "pem",
 "ring 0.16.20",
 "time 0.3.34",
 "yasna",
]

[[package]]
name = "rdrand"
version = "0.4.0"
source = "registry+https://github.com/rust-lang/crates.io-index"
checksum = "678054eb77286b51581ba43620cc911abf02758c91f93f479767aed0f90458b2"
dependencies = [
 "rand_core 0.3.1",
]

[[package]]
name = "read-write-set"
version = "0.1.0"
source = "git+https://github.com/fuzzland/ityfuzz-sui-fork.git#a88e9595de3c04a540c059d5f4b6d148419c508c"
dependencies = [
 "anyhow",
 "move-binary-format",
 "move-bytecode-utils",
 "move-core-types",
 "move-model",
 "move-read-write-set-types",
 "move-stackless-bytecode",
 "read-write-set-dynamic",
]

[[package]]
name = "read-write-set-dynamic"
version = "0.1.0"
source = "git+https://github.com/fuzzland/ityfuzz-sui-fork.git#a88e9595de3c04a540c059d5f4b6d148419c508c"
dependencies = [
 "anyhow",
 "move-binary-format",
 "move-bytecode-utils",
 "move-core-types",
 "move-read-write-set-types",
]

[[package]]
name = "readonly"
version = "0.2.12"
source = "registry+https://github.com/rust-lang/crates.io-index"
checksum = "a25d631e41bfb5fdcde1d4e2215f62f7f0afa3ff11e26563765bd6ea1d229aeb"
dependencies = [
 "proc-macro2 1.0.79",
 "quote 1.0.35",
 "syn 2.0.57",
]

[[package]]
name = "real_tokio"
version = "1.28.1"
source = "git+https://github.com/mystenmark/tokio-madsim-fork.git?rev=e4693500118d5e79ce098ee6dfc2c48f3ef19e45#e4693500118d5e79ce098ee6dfc2c48f3ef19e45"
dependencies = [
 "autocfg",
 "bytes",
 "libc",
 "mio 0.8.11",
 "num_cpus",
 "parking_lot 0.12.1",
 "pin-project-lite",
 "signal-hook-registry",
 "socket2 0.4.10",
 "tokio-macros 2.1.0",
 "windows-sys 0.48.0",
]

[[package]]
name = "redox_syscall"
version = "0.2.16"
source = "registry+https://github.com/rust-lang/crates.io-index"
checksum = "fb5a58c1855b4b6819d59012155603f0b22ad30cad752600aadfcb695265519a"
dependencies = [
 "bitflags 1.3.2",
]

[[package]]
name = "redox_syscall"
version = "0.4.1"
source = "registry+https://github.com/rust-lang/crates.io-index"
checksum = "4722d768eff46b75989dd134e5c353f0d6296e5aaa3132e776cbdb56be7731aa"
dependencies = [
 "bitflags 1.3.2",
]

[[package]]
name = "redox_users"
version = "0.4.4"
source = "registry+https://github.com/rust-lang/crates.io-index"
checksum = "a18479200779601e498ada4e8c1e1f50e3ee19deb0259c25825a98b5603b2cb4"
dependencies = [
 "getrandom 0.2.12",
 "libredox",
 "thiserror",
]

[[package]]
name = "ref-cast"
version = "1.0.22"
source = "registry+https://github.com/rust-lang/crates.io-index"
checksum = "c4846d4c50d1721b1a3bef8af76924eef20d5e723647333798c1b519b3a9473f"
dependencies = [
 "ref-cast-impl",
]

[[package]]
name = "ref-cast-impl"
version = "1.0.22"
source = "registry+https://github.com/rust-lang/crates.io-index"
checksum = "5fddb4f8d99b0a2ebafc65a87a69a7b9875e4b1ae1f00db265d300ef7f28bccc"
dependencies = [
 "proc-macro2 1.0.79",
 "quote 1.0.35",
 "syn 2.0.57",
]

[[package]]
name = "regex"
version = "1.10.4"
source = "registry+https://github.com/rust-lang/crates.io-index"
checksum = "c117dbdfde9c8308975b6a18d71f3f385c89461f7b3fb054288ecf2a2058ba4c"
dependencies = [
 "aho-corasick 1.1.3",
 "memchr",
 "regex-automata 0.4.6",
 "regex-syntax 0.8.3",
]

[[package]]
name = "regex-automata"
version = "0.1.10"
source = "registry+https://github.com/rust-lang/crates.io-index"
checksum = "6c230d73fb8d8c1b9c0b3135c5142a8acee3a0558fb8db5cf1cb65f8d7862132"
dependencies = [
 "regex-syntax 0.6.29",
]

[[package]]
name = "regex-automata"
version = "0.4.6"
source = "registry+https://github.com/rust-lang/crates.io-index"
checksum = "86b83b8b9847f9bf95ef68afb0b8e6cdb80f498442f5179a29fad448fcc1eaea"
dependencies = [
 "aho-corasick 1.1.3",
 "memchr",
 "regex-syntax 0.8.3",
]

[[package]]
name = "regex-syntax"
version = "0.6.29"
source = "registry+https://github.com/rust-lang/crates.io-index"
checksum = "f162c6dd7b008981e4d40210aca20b4bd0f9b60ca9271061b07f78537722f2e1"

[[package]]
name = "regex-syntax"
version = "0.7.5"
source = "registry+https://github.com/rust-lang/crates.io-index"
checksum = "dbb5fb1acd8a1a18b3dd5be62d25485eb770e05afb408a9627d14d451bae12da"

[[package]]
name = "regex-syntax"
version = "0.8.3"
source = "registry+https://github.com/rust-lang/crates.io-index"
checksum = "adad44e29e4c806119491a7f06f03de4d1af22c3a680dd47f1e6e179439d1f56"

[[package]]
name = "relative"
version = "0.2.2"
source = "registry+https://github.com/rust-lang/crates.io-index"
checksum = "3401c189ee92c7028ba4863f3fdb92af815789993221af2fa186eed8115da304"
dependencies = [
 "build_id",
 "serde",
 "uuid 0.8.2",
]

[[package]]
name = "reqwest"
version = "0.11.27"
source = "registry+https://github.com/rust-lang/crates.io-index"
checksum = "dd67538700a17451e7cba03ac727fb961abb7607553461627b97de0b89cf4a62"
dependencies = [
 "base64 0.21.7",
 "bytes",
 "encoding_rs",
 "futures-core",
 "futures-util",
 "h2",
 "http",
 "http-body",
 "hyper",
 "hyper-rustls 0.24.2",
 "hyper-tls",
 "ipnet",
 "js-sys",
 "log",
 "mime",
 "native-tls",
 "once_cell",
 "percent-encoding",
 "pin-project-lite",
 "rustls 0.21.10",
 "rustls-native-certs",
 "rustls-pemfile",
 "serde",
 "serde_json",
 "serde_urlencoded",
 "sync_wrapper",
 "system-configuration",
 "tokio",
 "tokio-native-tls",
 "tokio-rustls 0.24.1",
 "tower-service",
 "url",
 "wasm-bindgen",
 "wasm-bindgen-futures",
 "web-sys",
 "webpki-roots 0.25.4",
 "winreg 0.50.0",
]

[[package]]
name = "retain_mut"
version = "0.1.9"
source = "registry+https://github.com/rust-lang/crates.io-index"
checksum = "4389f1d5789befaf6029ebd9f7dac4af7f7e3d61b69d4f30e2ac02b57e7712b0"

[[package]]
name = "retry"
version = "2.0.0"
source = "registry+https://github.com/rust-lang/crates.io-index"
checksum = "9166d72162de3575f950507683fac47e30f6f2c3836b71b7fbc61aa517c9c5f4"
dependencies = [
 "rand 0.8.5",
]

[[package]]
name = "revm"
version = "3.3.0"
source = "git+https://github.com/fuzzland/revm?rev=1dead51#1dead511260119867b220b38298ddca07f406357"
dependencies = [
 "auto_impl",
 "revm-interpreter 1.1.2",
 "revm-precompile 2.0.3",
 "serde",
 "serde_json",
]

[[package]]
name = "revm"
version = "7.2.0"
source = "registry+https://github.com/rust-lang/crates.io-index"
checksum = "24fd3ed4b62dc61c647552d8b781811ae25ec74d23309055077e4dfb392444d2"
dependencies = [
 "auto_impl",
 "cfg-if 1.0.0",
 "dyn-clone",
 "revm-interpreter 3.4.0",
 "revm-precompile 5.1.0",
 "serde",
 "serde_json",
]

[[package]]
name = "revm-inspectors"
version = "0.1.0"
source = "git+https://github.com/paradigmxyz/evm-inspectors?rev=ba0b6ab#ba0b6ab695802c752601f17f5c941b62a067ad64"
dependencies = [
 "alloy-primitives",
 "alloy-rpc-trace-types",
 "alloy-rpc-types",
 "alloy-sol-types",
 "anstyle",
 "colorchoice",
 "revm 7.2.0",
 "serde",
 "serde_json",
 "thiserror",
]

[[package]]
name = "revm-interpreter"
version = "1.1.2"
source = "git+https://github.com/fuzzland/revm?rev=1dead51#1dead511260119867b220b38298ddca07f406357"
dependencies = [
 "derive_more",
 "enumn",
 "revm-primitives 1.1.2",
 "serde",
 "sha3 0.10.8",
]

[[package]]
name = "revm-interpreter"
version = "3.4.0"
source = "registry+https://github.com/rust-lang/crates.io-index"
checksum = "9f0a1818f8c876b0d71a0714217c34da7df8a42c0462750768779d55680e4554"
dependencies = [
 "revm-primitives 3.1.0",
 "serde",
]

[[package]]
name = "revm-precompile"
version = "2.0.3"
source = "git+https://github.com/fuzzland/revm?rev=1dead51#1dead511260119867b220b38298ddca07f406357"
dependencies = [
 "k256 0.13.3",
 "num 0.4.1",
 "once_cell",
 "revm-primitives 1.1.2",
 "ripemd",
 "secp256k1 0.27.0",
 "sha2 0.10.8",
 "sha3 0.10.8",
 "substrate-bn",
]

[[package]]
name = "revm-precompile"
version = "5.1.0"
source = "registry+https://github.com/rust-lang/crates.io-index"
checksum = "7a9645a70f1df1e5bd7fa8718b9ba486fac9c3f0467aa6b58e7f590d5f6fd0f7"
dependencies = [
 "aurora-engine-modexp",
 "c-kzg",
 "k256 0.13.3",
 "once_cell",
 "revm-primitives 3.1.0",
 "ripemd",
 "secp256k1 0.28.2",
 "sha2 0.10.8",
 "substrate-bn",
]

[[package]]
name = "revm-primitives"
version = "1.1.2"
source = "git+https://github.com/fuzzland/revm?rev=1dead51#1dead511260119867b220b38298ddca07f406357"
dependencies = [
 "auto_impl",
 "bitflags 2.5.0",
 "bitvec 1.0.1",
 "bytes",
 "derive_more",
 "enumn",
 "fixed-hash 0.8.0",
 "hashbrown 0.13.2",
 "hex",
 "hex-literal 0.4.1",
 "primitive-types 0.12.2",
 "rlp",
 "ruint",
 "serde",
 "sha3 0.10.8",
]

[[package]]
name = "revm-primitives"
version = "3.1.0"
source = "registry+https://github.com/rust-lang/crates.io-index"
checksum = "323ad597cf75ac9cb1d161be29fcc3562426f0278a1d04741697fca556e1ceea"
dependencies = [
 "alloy-primitives",
 "auto_impl",
 "bitflags 2.5.0",
 "bitvec 1.0.1",
 "c-kzg",
 "cfg-if 1.0.0",
 "dyn-clone",
 "enumn",
 "hashbrown 0.14.3",
 "hex",
 "serde",
]

[[package]]
name = "rfc6979"
version = "0.3.1"
source = "registry+https://github.com/rust-lang/crates.io-index"
checksum = "7743f17af12fa0b03b803ba12cd6a8d9483a587e89c69445e3909655c0b9fabb"
dependencies = [
 "crypto-bigint 0.4.9",
 "hmac 0.12.1",
 "zeroize",
]

[[package]]
name = "rfc6979"
version = "0.4.0"
source = "registry+https://github.com/rust-lang/crates.io-index"
checksum = "f8dd2a808d456c4a54e300a23e9f5a67e122c3024119acbfd73e3bf664491cb2"
dependencies = [
 "hmac 0.12.1",
 "subtle",
]

[[package]]
name = "rgb"
version = "0.8.37"
source = "registry+https://github.com/rust-lang/crates.io-index"
checksum = "05aaa8004b64fd573fc9d002f4e632d51ad4f026c2b5ba95fcb6c2f32c2c47d8"
dependencies = [
 "bytemuck",
]

[[package]]
name = "ring"
version = "0.16.20"
source = "registry+https://github.com/rust-lang/crates.io-index"
checksum = "3053cf52e236a3ed746dfc745aa9cacf1b791d846bdaf412f60a8d7d6e17c8fc"
dependencies = [
 "cc",
 "libc",
 "once_cell",
 "spin 0.5.2",
 "untrusted 0.7.1",
 "web-sys",
 "winapi",
]

[[package]]
name = "ring"
version = "0.17.8"
source = "registry+https://github.com/rust-lang/crates.io-index"
checksum = "c17fa4cb658e3583423e915b9f3acc01cceaee1860e33d59ebae66adc3a2dc0d"
dependencies = [
 "cc",
 "cfg-if 1.0.0",
 "getrandom 0.2.12",
 "libc",
 "spin 0.9.8",
 "untrusted 0.9.0",
 "windows-sys 0.52.0",
]

[[package]]
name = "ripemd"
version = "0.1.3"
source = "registry+https://github.com/rust-lang/crates.io-index"
checksum = "bd124222d17ad93a644ed9d011a40f4fb64aa54275c08cc216524a9ea82fb09f"
dependencies = [
 "digest 0.10.7",
]

[[package]]
name = "rlp"
version = "0.5.2"
source = "registry+https://github.com/rust-lang/crates.io-index"
checksum = "bb919243f34364b6bd2fc10ef797edbfa75f33c252e7998527479c6d6b47e1ec"
dependencies = [
 "bytes",
 "rlp-derive",
 "rustc-hex",
]

[[package]]
name = "rlp-derive"
version = "0.1.0"
source = "registry+https://github.com/rust-lang/crates.io-index"
checksum = "e33d7b2abe0c340d8797fe2907d3f20d3b5ea5908683618bfe80df7f621f672a"
dependencies = [
 "proc-macro2 1.0.79",
 "quote 1.0.35",
 "syn 1.0.109",
]

[[package]]
name = "roaring"
version = "0.10.3"
source = "registry+https://github.com/rust-lang/crates.io-index"
checksum = "a1c77081a55300e016cb86f2864415b7518741879db925b8d488a0ee0d2da6bf"
dependencies = [
 "bytemuck",
 "byteorder",
]

[[package]]
name = "rocksdb"
version = "0.21.0"
source = "registry+https://github.com/rust-lang/crates.io-index"
checksum = "bb6f170a4041d50a0ce04b0d2e14916d6ca863ea2e422689a5b694395d299ffe"
dependencies = [
 "libc",
 "librocksdb-sys",
]

[[package]]
name = "ron"
version = "0.8.1"
source = "registry+https://github.com/rust-lang/crates.io-index"
checksum = "b91f7eff05f748767f183df4320a63d6936e9c6107d97c9e6bdd9784f4289c94"
dependencies = [
 "base64 0.21.7",
 "bitflags 2.5.0",
 "serde",
 "serde_derive",
]

[[package]]
name = "rpassword"
version = "7.3.1"
source = "registry+https://github.com/rust-lang/crates.io-index"
checksum = "80472be3c897911d0137b2d2b9055faf6eeac5b14e324073d83bc17b191d7e3f"
dependencies = [
 "libc",
 "rtoolbox",
 "windows-sys 0.48.0",
]

[[package]]
name = "rsa"
version = "0.8.2"
source = "registry+https://github.com/rust-lang/crates.io-index"
checksum = "55a77d189da1fee555ad95b7e50e7457d91c0e089ec68ca69ad2989413bbdab4"
dependencies = [
 "byteorder",
 "digest 0.10.7",
 "num-bigint-dig",
 "num-integer",
 "num-iter",
 "num-traits",
 "pkcs1",
 "pkcs8 0.9.0",
 "rand_core 0.6.4",
 "sha2 0.10.8",
 "signature 2.2.0",
 "subtle",
 "zeroize",
]

[[package]]
name = "rstest"
version = "0.16.0"
source = "registry+https://github.com/rust-lang/crates.io-index"
checksum = "b07f2d176c472198ec1e6551dc7da28f1c089652f66a7b722676c2238ebc0edf"
dependencies = [
 "futures",
 "futures-timer",
 "rstest_macros",
 "rustc_version 0.4.0",
]

[[package]]
name = "rstest_macros"
version = "0.16.0"
source = "registry+https://github.com/rust-lang/crates.io-index"
checksum = "7229b505ae0706e64f37ffc54a9c163e11022a6636d58fe1f3f52018257ff9f7"
dependencies = [
 "cfg-if 1.0.0",
 "proc-macro2 1.0.79",
 "quote 1.0.35",
 "rustc_version 0.4.0",
 "syn 1.0.109",
 "unicode-ident",
]

[[package]]
name = "rtoolbox"
version = "0.0.2"
source = "registry+https://github.com/rust-lang/crates.io-index"
checksum = "c247d24e63230cdb56463ae328478bd5eac8b8faa8c69461a77e8e323afac90e"
dependencies = [
 "libc",
 "windows-sys 0.48.0",
]

[[package]]
name = "ruint"
version = "1.12.3"
source = "registry+https://github.com/rust-lang/crates.io-index"
checksum = "2c3cc4c2511671f327125da14133d0c5c5d137f006a1017a16f557bc85b16286"
dependencies = [
 "alloy-rlp",
 "arbitrary",
 "ark-ff 0.3.0",
 "ark-ff 0.4.2",
 "bytes",
 "fastrlp",
 "num-bigint 0.4.4",
 "num-traits",
 "parity-scale-codec 3.6.9",
 "primitive-types 0.12.2",
 "proptest",
 "rand 0.8.5",
 "rlp",
 "ruint-macro",
 "serde",
 "valuable",
 "zeroize",
]

[[package]]
name = "ruint-macro"
version = "1.2.1"
source = "registry+https://github.com/rust-lang/crates.io-index"
checksum = "48fd7bd8a6377e15ad9d42a8ec25371b94ddc67abe7c8b9127bec79bebaaae18"

[[package]]
name = "rusb"
version = "0.9.3"
source = "registry+https://github.com/rust-lang/crates.io-index"
checksum = "45fff149b6033f25e825cbb7b2c625a11ee8e6dac09264d49beb125e39aa97bf"
dependencies = [
 "libc",
 "libusb1-sys",
]

[[package]]
name = "rusoto_core"
version = "0.48.0"
source = "registry+https://github.com/rust-lang/crates.io-index"
checksum = "1db30db44ea73551326269adcf7a2169428a054f14faf9e1768f2163494f2fa2"
dependencies = [
 "async-trait",
 "base64 0.13.1",
 "bytes",
 "crc32fast",
 "futures",
 "http",
 "hyper",
 "hyper-rustls 0.23.2",
 "lazy_static",
 "log",
 "rusoto_credential",
 "rusoto_signature",
 "rustc_version 0.4.0",
 "serde",
 "serde_json",
 "tokio",
 "xml-rs",
]

[[package]]
name = "rusoto_credential"
version = "0.48.0"
source = "registry+https://github.com/rust-lang/crates.io-index"
checksum = "ee0a6c13db5aad6047b6a44ef023dbbc21a056b6dab5be3b79ce4283d5c02d05"
dependencies = [
 "async-trait",
 "chrono",
 "dirs-next",
 "futures",
 "hyper",
 "serde",
 "serde_json",
 "shlex",
 "tokio",
 "zeroize",
]

[[package]]
name = "rusoto_kms"
version = "0.48.0"
source = "registry+https://github.com/rust-lang/crates.io-index"
checksum = "3e1fc19cfcfd9f6b2f96e36d5b0dddda9004d2cbfc2d17543e3b9f10cc38fce8"
dependencies = [
 "async-trait",
 "bytes",
 "futures",
 "rusoto_core",
 "serde",
 "serde_json",
]

[[package]]
name = "rusoto_signature"
version = "0.48.0"
source = "registry+https://github.com/rust-lang/crates.io-index"
checksum = "a5ae95491c8b4847931e291b151127eccd6ff8ca13f33603eb3d0035ecb05272"
dependencies = [
 "base64 0.13.1",
 "bytes",
 "chrono",
 "digest 0.9.0",
 "futures",
 "hex",
 "hmac 0.11.0",
 "http",
 "hyper",
 "log",
 "md-5 0.9.1",
 "percent-encoding",
 "pin-project-lite",
 "rusoto_credential",
 "rustc_version 0.4.0",
 "serde",
 "sha2 0.9.9",
 "tokio",
]

[[package]]
name = "rust-crypto"
version = "0.2.36"
source = "registry+https://github.com/rust-lang/crates.io-index"
checksum = "f76d05d3993fd5f4af9434e8e436db163a12a9d40e1a58a726f27a01dfd12a2a"
dependencies = [
 "gcc",
 "libc",
 "rand 0.3.23",
 "rustc-serialize",
 "time 0.1.43",
]

[[package]]
name = "rust_decimal"
version = "1.35.0"
source = "registry+https://github.com/rust-lang/crates.io-index"
checksum = "1790d1c4c0ca81211399e0e0af16333276f375209e71a37b67698a373db5b47a"
dependencies = [
 "arrayvec 0.7.4",
 "num-traits",
]

[[package]]
name = "rustc-demangle"
version = "0.1.23"
source = "registry+https://github.com/rust-lang/crates.io-index"
checksum = "d626bb9dae77e28219937af045c257c28bfd3f69333c512553507f5f9798cb76"

[[package]]
name = "rustc-hash"
version = "1.1.0"
source = "registry+https://github.com/rust-lang/crates.io-index"
checksum = "08d43f7aa6b08d49f382cde6a7982047c3426db949b1424bc4b7ec9ae12c6ce2"

[[package]]
name = "rustc-hex"
version = "2.1.0"
source = "registry+https://github.com/rust-lang/crates.io-index"
checksum = "3e75f6a532d0fd9f7f13144f392b6ad56a32696bfcd9c78f797f16bbb6f072d6"

[[package]]
name = "rustc-serialize"
version = "0.3.25"
source = "registry+https://github.com/rust-lang/crates.io-index"
checksum = "fe834bc780604f4674073badbad26d7219cadfb4a2275802db12cbae17498401"

[[package]]
name = "rustc_version"
version = "0.2.3"
source = "registry+https://github.com/rust-lang/crates.io-index"
checksum = "138e3e0acb6c9fb258b19b67cb8abd63c00679d2851805ea151465464fe9030a"
dependencies = [
 "semver 0.9.0",
]

[[package]]
name = "rustc_version"
version = "0.3.3"
source = "registry+https://github.com/rust-lang/crates.io-index"
checksum = "f0dfe2087c51c460008730de8b57e6a320782fbfb312e1f4d520e6c6fae155ee"
dependencies = [
 "semver 0.11.0",
]

[[package]]
name = "rustc_version"
version = "0.4.0"
source = "registry+https://github.com/rust-lang/crates.io-index"
checksum = "bfa0f585226d2e68097d4f95d113b15b83a82e819ab25717ec0590d9584ef366"
dependencies = [
 "semver 1.0.22",
]

[[package]]
name = "rusticata-macros"
version = "4.1.0"
source = "registry+https://github.com/rust-lang/crates.io-index"
checksum = "faf0c4a6ece9950b9abdb62b1cfcf2a68b3b67a10ba445b3bb85be2a293d0632"
dependencies = [
 "nom 7.1.3",
]

[[package]]
name = "rustix"
version = "0.36.17"
source = "registry+https://github.com/rust-lang/crates.io-index"
checksum = "305efbd14fde4139eb501df5f136994bb520b033fa9fbdce287507dc23b8c7ed"
dependencies = [
 "bitflags 1.3.2",
 "errno 0.3.8",
 "io-lifetimes",
 "libc",
 "linux-raw-sys 0.1.4",
 "windows-sys 0.45.0",
]

[[package]]
name = "rustix"
version = "0.37.27"
source = "registry+https://github.com/rust-lang/crates.io-index"
checksum = "fea8ca367a3a01fe35e6943c400addf443c0f57670e6ec51196f71a4b8762dd2"
dependencies = [
 "bitflags 1.3.2",
 "errno 0.3.8",
 "io-lifetimes",
 "libc",
 "linux-raw-sys 0.3.8",
 "windows-sys 0.48.0",
]

[[package]]
name = "rustix"
version = "0.38.32"
source = "registry+https://github.com/rust-lang/crates.io-index"
checksum = "65e04861e65f21776e67888bfbea442b3642beaa0138fdb1dd7a84a52dffdb89"
dependencies = [
 "bitflags 2.5.0",
 "errno 0.3.8",
 "libc",
 "linux-raw-sys 0.4.13",
 "windows-sys 0.52.0",
]

[[package]]
name = "rustls"
version = "0.20.9"
source = "registry+https://github.com/rust-lang/crates.io-index"
checksum = "1b80e3dec595989ea8510028f30c408a4630db12c9cbb8de34203b89d6577e99"
dependencies = [
 "log",
 "ring 0.16.20",
 "sct",
 "webpki",
]

[[package]]
name = "rustls"
version = "0.21.10"
source = "registry+https://github.com/rust-lang/crates.io-index"
checksum = "f9d5a6813c0759e4609cd494e8e725babae6a2ca7b62a5536a13daaec6fcb7ba"
dependencies = [
 "log",
 "ring 0.17.8",
 "rustls-webpki 0.101.7",
 "sct",
]

[[package]]
name = "rustls-native-certs"
version = "0.6.3"
source = "registry+https://github.com/rust-lang/crates.io-index"
checksum = "a9aace74cb666635c918e9c12bc0d348266037aa8eb599b5cba565709a8dff00"
dependencies = [
 "openssl-probe",
 "rustls-pemfile",
 "schannel",
 "security-framework",
]

[[package]]
name = "rustls-pemfile"
version = "1.0.4"
source = "registry+https://github.com/rust-lang/crates.io-index"
checksum = "1c74cae0a4cf6ccbbf5f359f08efdf8ee7e1dc532573bf0db71968cb56b1448c"
dependencies = [
 "base64 0.21.7",
]

[[package]]
name = "rustls-webpki"
version = "0.100.3"
source = "registry+https://github.com/rust-lang/crates.io-index"
checksum = "5f6a5fc258f1c1276dfe3016516945546e2d5383911efc0fc4f1cdc5df3a4ae3"
dependencies = [
 "ring 0.16.20",
 "untrusted 0.7.1",
]

[[package]]
name = "rustls-webpki"
version = "0.101.7"
source = "registry+https://github.com/rust-lang/crates.io-index"
checksum = "8b6275d1ee7a1cd780b64aca7726599a1dbc893b1e64144529e55c3c2f745765"
dependencies = [
 "ring 0.17.8",
 "untrusted 0.9.0",
]

[[package]]
name = "rustversion"
version = "1.0.14"
source = "registry+https://github.com/rust-lang/crates.io-index"
checksum = "7ffc183a10b4478d04cbbbfc96d0873219d962dd5accaff2ffbd4ceb7df837f4"

[[package]]
name = "rusty-fork"
version = "0.3.0"
source = "registry+https://github.com/rust-lang/crates.io-index"
checksum = "cb3dcc6e454c328bb824492db107ab7c0ae8fcffe4ad210136ef014458c1bc4f"
dependencies = [
 "fnv",
 "quick-error 1.2.3",
 "tempfile",
 "wait-timeout",
]

[[package]]
name = "rustyline"
version = "9.1.2"
source = "registry+https://github.com/rust-lang/crates.io-index"
checksum = "db7826789c0e25614b03e5a54a0717a86f9ff6e6e5247f92b369472869320039"
dependencies = [
 "bitflags 1.3.2",
 "cfg-if 1.0.0",
 "clipboard-win",
 "dirs-next",
 "fd-lock",
 "libc",
 "log",
 "memchr",
 "nix 0.23.2",
 "radix_trie",
 "scopeguard",
 "smallvec",
 "unicode-segmentation",
 "unicode-width",
 "utf8parse",
 "winapi",
]

[[package]]
name = "rustyline-derive"
version = "0.7.0"
source = "registry+https://github.com/rust-lang/crates.io-index"
checksum = "107c3d5d7f370ac09efa62a78375f94d94b8a33c61d8c278b96683fb4dbf2d8d"
dependencies = [
 "proc-macro2 1.0.79",
 "quote 1.0.35",
 "syn 1.0.109",
]

[[package]]
name = "ryu"
version = "1.0.17"
source = "registry+https://github.com/rust-lang/crates.io-index"
checksum = "e86697c916019a8588c99b5fac3cead74ec0b4b819707a682fd4d23fa0ce1ba1"

[[package]]
name = "salsa20"
version = "0.10.2"
source = "registry+https://github.com/rust-lang/crates.io-index"
checksum = "97a22f5af31f73a954c10289c93e8a50cc23d971e80ee446f1f6f7137a088213"
dependencies = [
 "cipher",
]

[[package]]
name = "same-file"
version = "1.0.6"
source = "registry+https://github.com/rust-lang/crates.io-index"
checksum = "93fc1dc3aaa9bfed95e02e6eadabb4baf7e3078b0bd1b4d7b6b0b68378900502"
dependencies = [
 "winapi-util",
]

[[package]]
name = "scale-info"
version = "2.11.1"
source = "registry+https://github.com/rust-lang/crates.io-index"
checksum = "788745a868b0e751750388f4e6546eb921ef714a4317fa6954f7cde114eb2eb7"
dependencies = [
 "cfg-if 1.0.0",
 "derive_more",
 "parity-scale-codec 3.6.9",
 "scale-info-derive",
]

[[package]]
name = "scale-info-derive"
version = "2.11.1"
source = "registry+https://github.com/rust-lang/crates.io-index"
checksum = "7dc2f4e8bc344b9fc3d5f74f72c2e55bfc38d28dc2ebc69c194a3df424e4d9ac"
dependencies = [
 "proc-macro-crate 1.1.3",
 "proc-macro2 1.0.79",
 "quote 1.0.35",
 "syn 1.0.109",
]

[[package]]
name = "schannel"
version = "0.1.23"
source = "registry+https://github.com/rust-lang/crates.io-index"
checksum = "fbc91545643bcf3a0bbb6569265615222618bdf33ce4ffbbd13c4bbd4c093534"
dependencies = [
 "windows-sys 0.52.0",
]

[[package]]
name = "scheduled-thread-pool"
version = "0.2.7"
source = "registry+https://github.com/rust-lang/crates.io-index"
checksum = "3cbc66816425a074528352f5789333ecff06ca41b36b0b0efdfbb29edc391a19"
dependencies = [
 "parking_lot 0.12.1",
]

[[package]]
name = "schemars"
version = "0.8.16"
source = "registry+https://github.com/rust-lang/crates.io-index"
checksum = "45a28f4c49489add4ce10783f7911893516f15afe45d015608d41faca6bc4d29"
dependencies = [
 "dyn-clone",
 "either",
 "schemars_derive",
 "serde",
 "serde_json",
]

[[package]]
name = "schemars_derive"
version = "0.8.16"
source = "registry+https://github.com/rust-lang/crates.io-index"
checksum = "c767fd6fa65d9ccf9cf026122c1b555f2ef9a4f0cea69da4d7dbc3e258d30967"
dependencies = [
 "proc-macro2 1.0.79",
 "quote 1.0.35",
 "serde_derive_internals",
 "syn 1.0.109",
]

[[package]]
name = "scopeguard"
version = "1.2.0"
source = "registry+https://github.com/rust-lang/crates.io-index"
checksum = "94143f37725109f92c262ed2cf5e59bce7498c01bcc1502d7b9afe439a4e9f49"

[[package]]
name = "scrypt"
version = "0.10.0"
source = "registry+https://github.com/rust-lang/crates.io-index"
checksum = "9f9e24d2b632954ded8ab2ef9fea0a0c769ea56ea98bddbafbad22caeeadf45d"
dependencies = [
 "hmac 0.12.1",
 "pbkdf2 0.11.0",
 "salsa20",
 "sha2 0.10.8",
]

[[package]]
name = "sct"
version = "0.7.1"
source = "registry+https://github.com/rust-lang/crates.io-index"
checksum = "da046153aa2352493d6cb7da4b6e5c0c057d8a1d0a9aa8560baffdd945acd414"
dependencies = [
 "ring 0.17.8",
 "untrusted 0.9.0",
]

[[package]]
name = "sec1"
version = "0.3.0"
source = "registry+https://github.com/rust-lang/crates.io-index"
checksum = "3be24c1842290c45df0a7bf069e0c268a747ad05a192f2fd7dcfdbc1cba40928"
dependencies = [
 "base16ct 0.1.1",
 "der 0.6.1",
 "generic-array 0.14.7",
 "subtle",
 "zeroize",
]

[[package]]
name = "sec1"
version = "0.7.3"
source = "registry+https://github.com/rust-lang/crates.io-index"
checksum = "d3e97a565f76233a6003f9f5c54be1d9c5bdfa3eccfb189469f11ec4901c47dc"
dependencies = [
 "base16ct 0.2.0",
 "der 0.7.8",
 "generic-array 0.14.7",
 "pkcs8 0.10.2",
 "subtle",
 "zeroize",
]

[[package]]
name = "secp256k1"
version = "0.27.0"
source = "registry+https://github.com/rust-lang/crates.io-index"
checksum = "25996b82292a7a57ed3508f052cfff8640d38d32018784acd714758b43da9c8f"
dependencies = [
 "bitcoin_hashes",
 "rand 0.8.5",
 "secp256k1-sys 0.8.1",
]

[[package]]
name = "secp256k1"
version = "0.28.2"
source = "registry+https://github.com/rust-lang/crates.io-index"
checksum = "d24b59d129cdadea20aea4fb2352fa053712e5d713eee47d700cd4b2bc002f10"
dependencies = [
 "rand 0.8.5",
 "secp256k1-sys 0.9.2",
]

[[package]]
name = "secp256k1-sys"
version = "0.8.1"
source = "registry+https://github.com/rust-lang/crates.io-index"
checksum = "70a129b9e9efbfb223753b9163c4ab3b13cff7fd9c7f010fbac25ab4099fa07e"
dependencies = [
 "cc",
]

[[package]]
name = "secp256k1-sys"
version = "0.9.2"
source = "registry+https://github.com/rust-lang/crates.io-index"
checksum = "e5d1746aae42c19d583c3c1a8c646bfad910498e2051c551a7f2e3c0c9fbb7eb"
dependencies = [
 "cc",
]

[[package]]
name = "security-framework"
version = "2.10.0"
source = "registry+https://github.com/rust-lang/crates.io-index"
checksum = "770452e37cad93e0a50d5abc3990d2bc351c36d0328f86cefec2f2fb206eaef6"
dependencies = [
 "bitflags 1.3.2",
 "core-foundation",
 "core-foundation-sys",
 "libc",
 "security-framework-sys",
]

[[package]]
name = "security-framework-sys"
version = "2.10.0"
source = "registry+https://github.com/rust-lang/crates.io-index"
checksum = "41f3cc463c0ef97e11c3461a9d3787412d30e8e7eb907c79180c4a57bf7c04ef"
dependencies = [
 "core-foundation-sys",
 "libc",
]

[[package]]
name = "semver"
version = "0.9.0"
source = "registry+https://github.com/rust-lang/crates.io-index"
checksum = "1d7eb9ef2c18661902cc47e535f9bc51b78acd254da71d375c2f6720d9a40403"
dependencies = [
 "semver-parser 0.7.0",
]

[[package]]
name = "semver"
version = "0.11.0"
source = "registry+https://github.com/rust-lang/crates.io-index"
checksum = "f301af10236f6df4160f7c3f04eec6dbc70ace82d23326abad5edee88801c6b6"
dependencies = [
 "semver-parser 0.10.2",
]

[[package]]
name = "semver"
version = "1.0.22"
source = "registry+https://github.com/rust-lang/crates.io-index"
checksum = "92d43fe69e652f3df9bdc2b85b2854a0825b86e4fb76bc44d945137d053639ca"
dependencies = [
 "serde",
]

[[package]]
name = "semver-parser"
version = "0.7.0"
source = "registry+https://github.com/rust-lang/crates.io-index"
checksum = "388a1df253eca08550bef6c72392cfe7c30914bf41df5269b68cbd6ff8f570a3"

[[package]]
name = "semver-parser"
version = "0.10.2"
source = "registry+https://github.com/rust-lang/crates.io-index"
checksum = "00b0bef5b7f9e0df16536d3961cfb6e84331c065b4066afb39768d0e319411f7"
dependencies = [
 "pest",
]

[[package]]
name = "send_wrapper"
version = "0.4.0"
source = "registry+https://github.com/rust-lang/crates.io-index"
checksum = "f638d531eccd6e23b980caf34876660d38e265409d8e99b397ab71eb3612fad0"

[[package]]
name = "send_wrapper"
version = "0.6.0"
source = "registry+https://github.com/rust-lang/crates.io-index"
checksum = "cd0b0ec5f1c1ca621c432a25813d8d60c88abe6d3e08a3eb9cf37d97a0fe3d73"

[[package]]
name = "sentry"
version = "0.32.2"
source = "registry+https://github.com/rust-lang/crates.io-index"
checksum = "766448f12e44d68e675d5789a261515c46ac6ccd240abdd451a9c46c84a49523"
dependencies = [
 "httpdate",
 "native-tls",
 "reqwest",
 "sentry-backtrace",
 "sentry-contexts",
 "sentry-core",
 "sentry-debug-images",
 "sentry-panic",
 "sentry-tracing",
 "tokio",
 "ureq",
]

[[package]]
name = "sentry-backtrace"
version = "0.32.2"
source = "registry+https://github.com/rust-lang/crates.io-index"
checksum = "32701cad8b3c78101e1cd33039303154791b0ff22e7802ed8cc23212ef478b45"
dependencies = [
 "backtrace",
 "once_cell",
 "regex",
 "sentry-core",
]

[[package]]
name = "sentry-contexts"
version = "0.32.2"
source = "registry+https://github.com/rust-lang/crates.io-index"
checksum = "17ddd2a91a13805bd8dab4ebf47323426f758c35f7bf24eacc1aded9668f3824"
dependencies = [
 "hostname",
 "libc",
 "os_info",
 "rustc_version 0.4.0",
 "sentry-core",
 "uname",
]

[[package]]
name = "sentry-core"
version = "0.32.2"
source = "registry+https://github.com/rust-lang/crates.io-index"
checksum = "b1189f68d7e7e102ef7171adf75f83a59607fafd1a5eecc9dc06c026ff3bdec4"
dependencies = [
 "once_cell",
 "rand 0.8.5",
 "sentry-types",
 "serde",
 "serde_json",
]

[[package]]
name = "sentry-debug-images"
version = "0.32.2"
source = "registry+https://github.com/rust-lang/crates.io-index"
checksum = "7b4d0a615e5eeca5699030620c119a094e04c14cf6b486ea1030460a544111a7"
dependencies = [
 "findshlibs",
 "once_cell",
 "sentry-core",
]

[[package]]
name = "sentry-panic"
version = "0.32.2"
source = "registry+https://github.com/rust-lang/crates.io-index"
checksum = "d1c18d0b5fba195a4950f2f4c31023725c76f00aabb5840b7950479ece21b5ca"
dependencies = [
 "sentry-backtrace",
 "sentry-core",
]

[[package]]
name = "sentry-tracing"
version = "0.32.2"
source = "registry+https://github.com/rust-lang/crates.io-index"
checksum = "3012699a9957d7f97047fd75d116e22d120668327db6e7c59824582e16e791b2"
dependencies = [
 "sentry-backtrace",
 "sentry-core",
 "tracing-core",
 "tracing-subscriber 0.3.18",
]

[[package]]
name = "sentry-types"
version = "0.32.2"
source = "registry+https://github.com/rust-lang/crates.io-index"
checksum = "c7173fd594569091f68a7c37a886e202f4d0c1db1e1fa1d18a051ba695b2e2ec"
dependencies = [
 "debugid",
 "hex",
 "rand 0.8.5",
 "serde",
 "serde_json",
 "thiserror",
 "time 0.3.34",
 "url",
 "uuid 1.8.0",
]

[[package]]
name = "serde"
version = "1.0.197"
source = "registry+https://github.com/rust-lang/crates.io-index"
checksum = "3fb1c873e1b9b056a4dc4c0c198b24c3ffa059243875552b2bd0933b1aee4ce2"
dependencies = [
 "serde_derive",
]

[[package]]
name = "serde-name"
version = "0.2.1"
source = "registry+https://github.com/rust-lang/crates.io-index"
checksum = "3b5b14ebbcc4e4f2b3642fa99c388649da58d1dc3308c7d109f39f565d1710f0"
dependencies = [
 "serde",
 "thiserror",
]

[[package]]
name = "serde-reflection"
version = "0.3.6"
source = "registry+https://github.com/rust-lang/crates.io-index"
checksum = "f05a5f801ac62a51a49d378fdb3884480041b99aced450b28990673e8ff99895"
dependencies = [
 "once_cell",
 "serde",
 "thiserror",
]

[[package]]
name = "serde_bytes"
version = "0.11.14"
source = "registry+https://github.com/rust-lang/crates.io-index"
checksum = "8b8497c313fd43ab992087548117643f6fcd935cbf36f176ffda0aacf9591734"
dependencies = [
 "serde",
]

[[package]]
name = "serde_cbor"
version = "0.11.2"
source = "registry+https://github.com/rust-lang/crates.io-index"
checksum = "2bef2ebfde456fb76bbcf9f59315333decc4fda0b2b44b420243c11e0f5ec1f5"
dependencies = [
 "half 1.8.3",
 "serde",
]

[[package]]
name = "serde_derive"
version = "1.0.197"
source = "registry+https://github.com/rust-lang/crates.io-index"
checksum = "7eb0b34b42edc17f6b7cac84a52a1c5f0e1bb2227e997ca9011ea3dd34e8610b"
dependencies = [
 "proc-macro2 1.0.79",
 "quote 1.0.35",
 "syn 2.0.57",
]

[[package]]
name = "serde_derive_internals"
version = "0.26.0"
source = "registry+https://github.com/rust-lang/crates.io-index"
checksum = "85bf8229e7920a9f636479437026331ce11aa132b4dde37d121944a44d6e5f3c"
dependencies = [
 "proc-macro2 1.0.79",
 "quote 1.0.35",
 "syn 1.0.109",
]

[[package]]
name = "serde_json"
version = "1.0.115"
source = "registry+https://github.com/rust-lang/crates.io-index"
checksum = "12dc5c46daa8e9fdf4f5e71b6cf9a53f2487da0e86e55808e2d35539666497dd"
dependencies = [
 "indexmap 2.2.6",
 "itoa",
 "ryu",
 "serde",
]

[[package]]
name = "serde_path_to_error"
version = "0.1.16"
source = "registry+https://github.com/rust-lang/crates.io-index"
checksum = "af99884400da37c88f5e9146b7f1fd0fbcae8f6eec4e9da38b67d05486f814a6"
dependencies = [
 "itoa",
 "serde",
]

[[package]]
name = "serde_regex"
version = "1.1.0"
source = "registry+https://github.com/rust-lang/crates.io-index"
checksum = "a8136f1a4ea815d7eac4101cfd0b16dc0cb5e1fe1b8609dfd728058656b7badf"
dependencies = [
 "regex",
 "serde",
]

[[package]]
name = "serde_repr"
version = "0.1.18"
source = "registry+https://github.com/rust-lang/crates.io-index"
checksum = "0b2e6b945e9d3df726b65d6ee24060aff8e3533d431f677a9695db04eff9dfdb"
dependencies = [
 "proc-macro2 1.0.79",
 "quote 1.0.35",
 "syn 2.0.57",
]

[[package]]
name = "serde_spanned"
version = "0.6.5"
source = "registry+https://github.com/rust-lang/crates.io-index"
checksum = "eb3622f419d1296904700073ea6cc23ad690adbd66f13ea683df73298736f0c1"
dependencies = [
 "serde",
]

[[package]]
name = "serde_test"
version = "1.0.176"
source = "registry+https://github.com/rust-lang/crates.io-index"
checksum = "5a2f49ace1498612d14f7e0b8245519584db8299541dfe31a06374a828d620ab"
dependencies = [
 "serde",
]

[[package]]
name = "serde_traitobject"
version = "0.2.8"
source = "registry+https://github.com/rust-lang/crates.io-index"
checksum = "9c5ae15a5d31f7c57875a480ddd7be02314d264617d0294d961314a6d502e6b1"
dependencies = [
 "erased-serde 0.3.31",
 "metatype",
 "relative",
 "serde",
]

[[package]]
name = "serde_urlencoded"
version = "0.7.1"
source = "registry+https://github.com/rust-lang/crates.io-index"
checksum = "d3491c14715ca2294c4d6a88f15e84739788c1d030eed8c110436aafdaa2f3fd"
dependencies = [
 "form_urlencoded",
 "itoa",
 "ryu",
 "serde",
]

[[package]]
name = "serde_with"
version = "2.3.3"
source = "registry+https://github.com/rust-lang/crates.io-index"
checksum = "07ff71d2c147a7b57362cead5e22f772cd52f6ab31cfcd9edcd7f6aeb2a0afbe"
dependencies = [
 "base64 0.13.1",
 "chrono",
 "hex",
 "indexmap 1.9.3",
 "serde",
 "serde_json",
 "serde_with_macros",
 "time 0.3.34",
]

[[package]]
name = "serde_with_macros"
version = "2.3.3"
source = "registry+https://github.com/rust-lang/crates.io-index"
checksum = "881b6f881b17d13214e5d494c939ebab463d01264ce1811e9d4ac3a882e7695f"
dependencies = [
 "darling 0.20.8",
 "proc-macro2 1.0.79",
 "quote 1.0.35",
 "syn 2.0.57",
]

[[package]]
name = "serde_yaml"
version = "0.8.26"
source = "registry+https://github.com/rust-lang/crates.io-index"
checksum = "578a7433b776b56a35785ed5ce9a7e777ac0598aac5a6dd1b4b18a307c7fc71b"
dependencies = [
 "indexmap 1.9.3",
 "ryu",
 "serde",
 "yaml-rust",
]

[[package]]
name = "serde_yaml"
version = "0.9.34+deprecated"
source = "registry+https://github.com/rust-lang/crates.io-index"
checksum = "6a8b1a1a2ebf674015cc02edccce75287f1a0130d394307b36743c2f5d504b47"
dependencies = [
 "indexmap 2.2.6",
 "itoa",
 "ryu",
 "serde",
 "unsafe-libyaml",
]

[[package]]
name = "serial_test"
version = "2.0.0"
source = "registry+https://github.com/rust-lang/crates.io-index"
checksum = "0e56dd856803e253c8f298af3f4d7eb0ae5e23a737252cd90bb4f3b435033b2d"
dependencies = [
 "dashmap",
 "lazy_static",
 "log",
 "parking_lot 0.12.1",
 "serial_test_derive",
]

[[package]]
name = "serial_test_derive"
version = "2.0.0"
source = "registry+https://github.com/rust-lang/crates.io-index"
checksum = "91d129178576168c589c9ec973feedf7d3126c01ac2bf08795109aa35b69fb8f"
dependencies = [
 "proc-macro2 1.0.79",
 "quote 1.0.35",
 "syn 2.0.57",
]

[[package]]
name = "sha-1"
version = "0.9.8"
source = "registry+https://github.com/rust-lang/crates.io-index"
checksum = "99cd6713db3cf16b6c84e06321e049a9b9f699826e16096d23bbcc44d15d51a6"
dependencies = [
 "block-buffer 0.9.0",
 "cfg-if 1.0.0",
 "cpufeatures",
 "digest 0.9.0",
 "opaque-debug",
]

[[package]]
name = "sha-1"
version = "0.10.1"
source = "registry+https://github.com/rust-lang/crates.io-index"
checksum = "f5058ada175748e33390e40e872bd0fe59a19f265d0158daa551c5a88a76009c"
dependencies = [
 "cfg-if 1.0.0",
 "cpufeatures",
 "digest 0.10.7",
]

[[package]]
name = "sha1"
version = "0.10.6"
source = "registry+https://github.com/rust-lang/crates.io-index"
checksum = "e3bf829a2d51ab4a5ddf1352d8470c140cadc8301b2ae1789db023f01cedd6ba"
dependencies = [
 "cfg-if 1.0.0",
 "cpufeatures",
 "digest 0.10.7",
]

[[package]]
name = "sha2"
version = "0.9.9"
source = "registry+https://github.com/rust-lang/crates.io-index"
checksum = "4d58a1e1bf39749807d89cf2d98ac2dfa0ff1cb3faa38fbb64dd88ac8013d800"
dependencies = [
 "block-buffer 0.9.0",
 "cfg-if 1.0.0",
 "cpufeatures",
 "digest 0.9.0",
 "opaque-debug",
]

[[package]]
name = "sha2"
version = "0.10.8"
source = "registry+https://github.com/rust-lang/crates.io-index"
checksum = "793db75ad2bcafc3ffa7c68b215fee268f537982cd901d132f89c6343f3a3dc8"
dependencies = [
 "cfg-if 1.0.0",
 "cpufeatures",
 "digest 0.10.7",
]

[[package]]
name = "sha3"
version = "0.9.1"
source = "registry+https://github.com/rust-lang/crates.io-index"
checksum = "f81199417d4e5de3f04b1e871023acea7389672c4135918f05aa9cbf2f2fa809"
dependencies = [
 "block-buffer 0.9.0",
 "digest 0.9.0",
 "keccak",
 "opaque-debug",
]

[[package]]
name = "sha3"
version = "0.10.8"
source = "registry+https://github.com/rust-lang/crates.io-index"
checksum = "75872d278a8f37ef87fa0ddbda7802605cb18344497949862c0d4dcb291eba60"
dependencies = [
 "digest 0.10.7",
 "keccak",
]

[[package]]
name = "sha3-asm"
version = "0.1.0"
source = "registry+https://github.com/rust-lang/crates.io-index"
checksum = "bac61da6b35ad76b195eb4771210f947734321a8d81d7738e1580d953bc7a15e"
dependencies = [
 "cc",
 "cfg-if 1.0.0",
]

[[package]]
name = "sharded-slab"
version = "0.1.7"
source = "registry+https://github.com/rust-lang/crates.io-index"
checksum = "f40ca3c46823713e0d4209592e8d6e826aa57e928f09752619fc696c499637f6"
dependencies = [
 "lazy_static",
]

[[package]]
name = "shared-crypto"
version = "0.0.0"
source = "git+https://github.com/fuzzland/ityfuzz-sui-fork.git#a88e9595de3c04a540c059d5f4b6d148419c508c"
dependencies = [
 "bcs",
 "eyre",
 "fastcrypto",
 "serde",
 "serde_repr",
 "workspace-hack",
]

[[package]]
name = "shell-words"
version = "1.1.0"
source = "registry+https://github.com/rust-lang/crates.io-index"
checksum = "24188a676b6ae68c3b2cb3a01be17fbf7240ce009799bb56d5b1409051e78fde"

[[package]]
name = "shellexpand"
version = "3.1.0"
source = "registry+https://github.com/rust-lang/crates.io-index"
checksum = "da03fa3b94cc19e3ebfc88c4229c49d8f08cdbd1228870a45f0ffdf84988e14b"
dependencies = [
 "dirs 5.0.1",
]

[[package]]
name = "shlex"
version = "1.3.0"
source = "registry+https://github.com/rust-lang/crates.io-index"
checksum = "0fda2ff0d084019ba4d7c6f371c95d8fd75ce3524c3cb8fb653a3023f6323e64"

[[package]]
name = "signal-hook"
version = "0.3.17"
source = "registry+https://github.com/rust-lang/crates.io-index"
checksum = "8621587d4798caf8eb44879d42e56b9a93ea5dcd315a6487c357130095b62801"
dependencies = [
 "libc",
 "signal-hook-registry",
]

[[package]]
name = "signal-hook-mio"
version = "0.2.3"
source = "registry+https://github.com/rust-lang/crates.io-index"
checksum = "29ad2e15f37ec9a6cc544097b78a1ec90001e9f71b81338ca39f430adaca99af"
dependencies = [
 "libc",
 "mio 0.7.14",
 "mio 0.8.11",
 "signal-hook",
]

[[package]]
name = "signal-hook-registry"
version = "1.4.1"
source = "registry+https://github.com/rust-lang/crates.io-index"
checksum = "d8229b473baa5980ac72ef434c4415e70c4b5e71b423043adb4ba059f89c99a1"
dependencies = [
 "libc",
]

[[package]]
name = "signature"
version = "1.6.4"
source = "registry+https://github.com/rust-lang/crates.io-index"
checksum = "74233d3b3b2f6d4b006dc19dee745e73e2a6bfb6f93607cd3b02bd5b00797d7c"
dependencies = [
 "digest 0.10.7",
 "rand_core 0.6.4",
]

[[package]]
name = "signature"
version = "2.2.0"
source = "registry+https://github.com/rust-lang/crates.io-index"
checksum = "77549399552de45a898a580c1b41d445bf730df867cc44e6c0233bbc4b8329de"
dependencies = [
 "digest 0.10.7",
 "rand_core 0.6.4",
]

[[package]]
name = "similar"
version = "2.5.0"
source = "registry+https://github.com/rust-lang/crates.io-index"
checksum = "fa42c91313f1d05da9b26f267f931cf178d4aba455b4c4622dd7355eb80c6640"

[[package]]
name = "simple_asn1"
version = "0.6.2"
source = "registry+https://github.com/rust-lang/crates.io-index"
checksum = "adc4e5204eb1910f40f9cfa375f6f05b68c3abac4b6fd879c8ff5e7ae8a0a085"
dependencies = [
 "num-bigint 0.4.4",
 "num-traits",
 "thiserror",
 "time 0.3.34",
]

[[package]]
name = "simplelog"
version = "0.9.0"
source = "registry+https://github.com/rust-lang/crates.io-index"
checksum = "4bc0ffd69814a9b251d43afcabf96dad1b29f5028378056257be9e3fecc9f720"
dependencies = [
 "chrono",
 "log",
 "termcolor",
]

[[package]]
name = "siphasher"
version = "0.3.11"
source = "registry+https://github.com/rust-lang/crates.io-index"
checksum = "38b58827f4464d87d377d175e90bf58eb00fd8716ff0a62f80356b5e61555d0d"

[[package]]
name = "sized-chunks"
version = "0.6.5"
source = "registry+https://github.com/rust-lang/crates.io-index"
checksum = "16d69225bde7a69b235da73377861095455d298f2b970996eec25ddbb42b3d1e"
dependencies = [
 "bitmaps",
 "typenum",
]

[[package]]
name = "slab"
version = "0.4.9"
source = "registry+https://github.com/rust-lang/crates.io-index"
checksum = "8f92a496fb766b417c996b9c5e57daf2f7ad3b0bebe1ccfca4856390e3d3bb67"
dependencies = [
 "autocfg",
]

[[package]]
name = "slip10_ed25519"
version = "0.1.3"
source = "registry+https://github.com/rust-lang/crates.io-index"
checksum = "4be0ff28bf14f9610a342169084e87a4f435ad798ec528dc7579a3678fa9dc9a"
dependencies = [
 "hmac-sha512",
]

[[package]]
name = "slug"
version = "0.1.5"
source = "registry+https://github.com/rust-lang/crates.io-index"
checksum = "3bd94acec9c8da640005f8e135a39fc0372e74535e6b368b7a04b875f784c8c4"
dependencies = [
 "deunicode 1.4.3",
 "wasm-bindgen",
]

[[package]]
name = "smallvec"
version = "1.13.2"
source = "registry+https://github.com/rust-lang/crates.io-index"
checksum = "3c5e1a9a646d36c3599cd173a41282daf47c44583ad367b8e6837255952e5c67"

[[package]]
name = "snap"
version = "1.1.1"
source = "registry+https://github.com/rust-lang/crates.io-index"
checksum = "1b6b67fb9a61334225b5b790716f609cd58395f895b3fe8b328786812a40bc3b"

[[package]]
name = "socket2"
version = "0.4.10"
source = "registry+https://github.com/rust-lang/crates.io-index"
checksum = "9f7916fc008ca5542385b89a3d3ce689953c143e9304a9bf8beec1de48994c0d"
dependencies = [
 "libc",
 "winapi",
]

[[package]]
name = "socket2"
version = "0.5.6"
source = "registry+https://github.com/rust-lang/crates.io-index"
checksum = "05ffd9c0a93b7543e062e759284fcf5f5e3b098501104bfbdde4d404db792871"
dependencies = [
 "libc",
 "windows-sys 0.52.0",
]

[[package]]
name = "soketto"
version = "0.7.1"
source = "registry+https://github.com/rust-lang/crates.io-index"
checksum = "41d1c5305e39e09653383c2c7244f2f78b3bcae37cf50c64cb4789c9f5096ec2"
dependencies = [
 "base64 0.13.1",
 "bytes",
 "futures",
 "http",
 "httparse",
 "log",
 "rand 0.8.5",
 "sha-1 0.9.8",
]

[[package]]
name = "solang-parser"
version = "0.3.3"
source = "registry+https://github.com/rust-lang/crates.io-index"
checksum = "c425ce1c59f4b154717592f0bdf4715c3a1d55058883622d3157e1f0908a5b26"
dependencies = [
 "itertools 0.11.0",
 "lalrpop",
 "lalrpop-util",
 "phf",
 "thiserror",
 "unicode-xid 0.2.4",
]

[[package]]
name = "spin"
version = "0.5.2"
source = "registry+https://github.com/rust-lang/crates.io-index"
checksum = "6e63cff320ae2c57904679ba7cb63280a3dc4613885beafb148ee7bf9aa9042d"

[[package]]
name = "spin"
version = "0.9.8"
source = "registry+https://github.com/rust-lang/crates.io-index"
checksum = "6980e8d7511241f8acf4aebddbb1ff938df5eebe98691418c4468d0b72a96a67"

[[package]]
name = "spinning"
version = "0.1.0"
source = "registry+https://github.com/rust-lang/crates.io-index"
checksum = "2d4f0e86297cad2658d92a707320d87bf4e6ae1050287f51d19b67ef3f153a7b"
dependencies = [
 "lock_api",
]

[[package]]
name = "spki"
version = "0.6.0"
source = "registry+https://github.com/rust-lang/crates.io-index"
checksum = "67cf02bbac7a337dc36e4f5a693db6c21e7863f45070f7064577eb4367a3212b"
dependencies = [
 "base64ct",
 "der 0.6.1",
]

[[package]]
name = "spki"
version = "0.7.3"
source = "registry+https://github.com/rust-lang/crates.io-index"
checksum = "d91ed6c858b01f942cd56b37a94b3e0a1798290327d1236e4d9cf4eaca44d29d"
dependencies = [
 "base64ct",
 "der 0.7.8",
]

[[package]]
name = "stable_deref_trait"
version = "1.2.0"
source = "registry+https://github.com/rust-lang/crates.io-index"
checksum = "a8f112729512f8e442d81f95a8a7ddf2b7c6b8a1a6f509a95864142b30cab2d3"

[[package]]
name = "static_assertions"
version = "1.1.0"
source = "registry+https://github.com/rust-lang/crates.io-index"
checksum = "a2eb9349b6444b326872e140eb1cf5e7c522154d69e7a0ffb0fb81c06b37543f"

[[package]]
name = "str-buf"
version = "1.0.6"
source = "registry+https://github.com/rust-lang/crates.io-index"
checksum = "9e08d8363704e6c71fc928674353e6b7c23dcea9d82d7012c8faf2a3a025f8d0"

[[package]]
name = "str_stack"
version = "0.1.0"
source = "registry+https://github.com/rust-lang/crates.io-index"
checksum = "9091b6114800a5f2141aee1d1b9d6ca3592ac062dc5decb3764ec5895a47b4eb"

[[package]]
name = "string_cache"
version = "0.8.7"
source = "registry+https://github.com/rust-lang/crates.io-index"
checksum = "f91138e76242f575eb1d3b38b4f1362f10d3a43f47d182a5b359af488a02293b"
dependencies = [
 "new_debug_unreachable",
 "once_cell",
 "parking_lot 0.12.1",
 "phf_shared 0.10.0",
 "precomputed-hash",
]

[[package]]
name = "strip-ansi-escapes"
version = "0.1.1"
source = "registry+https://github.com/rust-lang/crates.io-index"
checksum = "011cbb39cf7c1f62871aea3cc46e5817b0937b49e9447370c93cacbe93a766d8"
dependencies = [
 "vte",
]

[[package]]
name = "strsim"
version = "0.8.0"
source = "registry+https://github.com/rust-lang/crates.io-index"
checksum = "8ea5119cdb4c55b55d432abb513a0429384878c15dde60cc77b1c99de1a95a6a"

[[package]]
name = "strsim"
version = "0.10.0"
source = "registry+https://github.com/rust-lang/crates.io-index"
checksum = "73473c0e59e6d5812c5dfe2a064a6444949f089e20eec9a2e5506596494e4623"

[[package]]
name = "strsim"
version = "0.11.0"
source = "registry+https://github.com/rust-lang/crates.io-index"
checksum = "5ee073c9e4cd00e28217186dbe12796d692868f432bf2e97ee73bed0c56dfa01"

[[package]]
name = "structopt"
version = "0.3.26"
source = "registry+https://github.com/rust-lang/crates.io-index"
checksum = "0c6b5c64445ba8094a6ab0c3cd2ad323e07171012d9c98b0b15651daf1787a10"
dependencies = [
 "clap 2.34.0",
 "lazy_static",
 "structopt-derive",
]

[[package]]
name = "structopt-derive"
version = "0.4.18"
source = "registry+https://github.com/rust-lang/crates.io-index"
checksum = "dcb5ae327f9cc13b68763b5749770cb9e048a99bd9dfdfa58d0cf05d5f64afe0"
dependencies = [
 "heck 0.3.3",
 "proc-macro-error",
 "proc-macro2 1.0.79",
 "quote 1.0.35",
 "syn 1.0.109",
]

[[package]]
name = "strum"
version = "0.24.1"
source = "registry+https://github.com/rust-lang/crates.io-index"
checksum = "063e6045c0e62079840579a7e47a355ae92f60eb74daaf156fb1e84ba164e63f"
dependencies = [
 "strum_macros 0.24.3",
]

[[package]]
name = "strum"
version = "0.25.0"
source = "registry+https://github.com/rust-lang/crates.io-index"
checksum = "290d54ea6f91c969195bdbcd7442c8c2a2ba87da8bf60a7ee86a235d4bc1e125"
dependencies = [
 "strum_macros 0.25.3",
]

[[package]]
name = "strum"
version = "0.26.2"
source = "registry+https://github.com/rust-lang/crates.io-index"
checksum = "5d8cec3501a5194c432b2b7976db6b7d10ec95c253208b45f83f7136aa985e29"
dependencies = [
 "strum_macros 0.26.2",
]

[[package]]
name = "strum_macros"
version = "0.24.3"
source = "registry+https://github.com/rust-lang/crates.io-index"
checksum = "1e385be0d24f186b4ce2f9982191e7101bb737312ad61c1f2f984f34bcf85d59"
dependencies = [
 "heck 0.4.1",
 "proc-macro2 1.0.79",
 "quote 1.0.35",
 "rustversion",
 "syn 1.0.109",
]

[[package]]
name = "strum_macros"
version = "0.25.3"
source = "registry+https://github.com/rust-lang/crates.io-index"
checksum = "23dc1fa9ac9c169a78ba62f0b841814b7abae11bdd047b9c58f893439e309ea0"
dependencies = [
 "heck 0.4.1",
 "proc-macro2 1.0.79",
 "quote 1.0.35",
 "rustversion",
 "syn 2.0.57",
]

[[package]]
name = "strum_macros"
version = "0.26.2"
source = "registry+https://github.com/rust-lang/crates.io-index"
checksum = "c6cf59daf282c0a494ba14fd21610a0325f9f90ec9d1231dea26bcb1d696c946"
dependencies = [
 "heck 0.4.1",
 "proc-macro2 1.0.79",
 "quote 1.0.35",
 "rustversion",
 "syn 2.0.57",
]

[[package]]
name = "subprocess"
version = "0.2.9"
source = "registry+https://github.com/rust-lang/crates.io-index"
checksum = "0c2e86926081dda636c546d8c5e641661049d7562a68f5488be4a1f7f66f6086"
dependencies = [
 "libc",
 "winapi",
]

[[package]]
name = "substrate-bn"
version = "0.6.0"
source = "registry+https://github.com/rust-lang/crates.io-index"
checksum = "72b5bbfa79abbae15dd642ea8176a21a635ff3c00059961d1ea27ad04e5b441c"
dependencies = [
 "byteorder",
 "crunchy",
 "lazy_static",
 "rand 0.8.5",
 "rustc-hex",
]

[[package]]
name = "subtle"
version = "2.4.1"
source = "registry+https://github.com/rust-lang/crates.io-index"
checksum = "6bdef32e8150c2a081110b42772ffe7d7c9032b606bc226c8260fd97e0976601"

[[package]]
name = "subtle-ng"
version = "2.5.0"
source = "registry+https://github.com/rust-lang/crates.io-index"
checksum = "734676eb262c623cec13c3155096e08d1f8f29adce39ba17948b18dad1e54142"

[[package]]
name = "sui-enum-compat-util"
version = "0.1.0"
source = "git+https://github.com/fuzzland/ityfuzz-sui-fork.git#a88e9595de3c04a540c059d5f4b6d148419c508c"
dependencies = [
 "serde_yaml 0.8.26",
 "workspace-hack",
]

[[package]]
name = "sui-macros"
version = "0.7.0"
source = "git+https://github.com/fuzzland/ityfuzz-sui-fork.git#a88e9595de3c04a540c059d5f4b6d148419c508c"
dependencies = [
 "futures",
 "once_cell",
 "sui-proc-macros",
 "tracing",
 "workspace-hack",
]

[[package]]
name = "sui-move-natives-latest"
version = "0.1.0"
source = "git+https://github.com/fuzzland/ityfuzz-sui-fork.git#a88e9595de3c04a540c059d5f4b6d148419c508c"
dependencies = [
 "bcs",
 "better_any",
 "fastcrypto",
 "fastcrypto-zkp",
 "linked-hash-map",
 "move-binary-format",
 "move-core-types",
 "move-stdlib",
 "move-vm-runtime",
 "move-vm-types",
 "smallvec",
 "sui-protocol-config",
 "sui-types",
 "workspace-hack",
]

[[package]]
name = "sui-proc-macros"
version = "0.7.0"
source = "git+https://github.com/fuzzland/ityfuzz-sui-fork.git#a88e9595de3c04a540c059d5f4b6d148419c508c"
dependencies = [
 "msim-macros",
 "proc-macro2 1.0.79",
 "quote 1.0.35",
 "sui-enum-compat-util",
 "syn 2.0.57",
 "workspace-hack",
]

[[package]]
name = "sui-protocol-config"
version = "0.1.0"
source = "git+https://github.com/fuzzland/ityfuzz-sui-fork.git#a88e9595de3c04a540c059d5f4b6d148419c508c"
dependencies = [
 "insta",
 "schemars",
 "serde",
 "serde_with",
 "sui-protocol-config-macros",
 "tracing",
 "workspace-hack",
]

[[package]]
name = "sui-protocol-config-macros"
version = "0.1.0"
source = "git+https://github.com/fuzzland/ityfuzz-sui-fork.git#a88e9595de3c04a540c059d5f4b6d148419c508c"
dependencies = [
 "proc-macro2 1.0.79",
 "quote 1.0.35",
 "syn 1.0.109",
 "workspace-hack",
]

[[package]]
name = "sui-types"
version = "0.1.0"
source = "git+https://github.com/fuzzland/ityfuzz-sui-fork.git#a88e9595de3c04a540c059d5f4b6d148419c508c"
dependencies = [
 "anemo",
 "anyhow",
 "bcs",
 "bincode",
 "byteorder",
 "derivative",
 "derive_more",
 "enum_dispatch",
 "eyre",
 "fastcrypto",
 "fastcrypto-zkp",
 "im",
 "indexmap 1.9.3",
 "itertools 0.10.5",
 "move-binary-format",
 "move-bytecode-utils",
 "move-command-line-common",
 "move-core-types",
 "move-disassembler",
 "move-ir-types",
 "move-vm-profiler",
 "move-vm-test-utils",
 "move-vm-types",
 "mysten-metrics",
 "mysten-network",
 "narwhal-config",
 "narwhal-crypto",
 "once_cell",
 "parking_lot 0.12.1",
 "prometheus",
 "proptest",
 "proptest-derive 0.3.0",
 "rand 0.8.5",
 "roaring",
 "schemars",
 "serde",
 "serde-name",
 "serde_json",
 "serde_with",
 "shared-crypto",
 "signature 1.6.4",
 "static_assertions",
 "strum 0.24.1",
 "strum_macros 0.24.3",
 "sui-enum-compat-util",
 "sui-macros",
 "sui-protocol-config",
 "tap",
 "thiserror",
 "tonic 0.8.3",
 "tracing",
 "typed-store",
 "workspace-hack",
]

[[package]]
name = "svm-rs"
version = "0.3.5"
source = "registry+https://github.com/rust-lang/crates.io-index"
checksum = "11297baafe5fa0c99d5722458eac6a5e25c01eb1b8e5cd137f54079093daa7a4"
dependencies = [
 "dirs 5.0.1",
 "fs2",
 "hex",
 "once_cell",
 "reqwest",
 "semver 1.0.22",
 "serde",
 "serde_json",
 "sha2 0.10.8",
 "thiserror",
 "url",
 "zip",
]

[[package]]
name = "svm-rs"
version = "0.4.1"
source = "registry+https://github.com/rust-lang/crates.io-index"
checksum = "9bd5e919f01c9280dce59ab66296449d0e9144b8472b8892fbacf9612998b653"
dependencies = [
 "const-hex",
 "dirs 5.0.1",
 "fs4",
 "once_cell",
 "reqwest",
 "semver 1.0.22",
 "serde",
 "serde_json",
 "sha2 0.10.8",
 "thiserror",
 "url",
 "zip",
]

[[package]]
name = "svm-rs-builds"
version = "0.4.1"
source = "registry+https://github.com/rust-lang/crates.io-index"
checksum = "5bcf7abc816dd67daf88fccfb835118b0e71cf8cc3e1d0e120893e139799df6c"
dependencies = [
 "build_const",
 "const-hex",
 "semver 1.0.22",
 "serde_json",
 "svm-rs 0.4.1",
]

[[package]]
name = "symbolic-common"
version = "10.2.1"
source = "registry+https://github.com/rust-lang/crates.io-index"
checksum = "1b55cdc318ede251d0957f07afe5fed912119b8c1bc5a7804151826db999e737"
dependencies = [
 "debugid",
 "memmap2 0.5.10",
 "stable_deref_trait",
 "uuid 1.8.0",
]

[[package]]
name = "symbolic-demangle"
version = "10.2.1"
source = "registry+https://github.com/rust-lang/crates.io-index"
checksum = "79be897be8a483a81fff6a3a4e195b4ac838ef73ca42d348b3f722da9902e489"
dependencies = [
 "cpp_demangle",
 "rustc-demangle",
 "symbolic-common",
]

[[package]]
name = "syn"
version = "0.15.44"
source = "registry+https://github.com/rust-lang/crates.io-index"
checksum = "9ca4b3b69a77cbe1ffc9e198781b7acb0c7365a883670e8f1c1bc66fba79a5c5"
dependencies = [
 "proc-macro2 0.4.30",
 "quote 0.6.13",
 "unicode-xid 0.1.0",
]

[[package]]
name = "syn"
version = "1.0.109"
source = "registry+https://github.com/rust-lang/crates.io-index"
checksum = "72b64191b275b66ffe2469e8af2c1cfe3bafa67b529ead792a6d0160888b4237"
dependencies = [
 "proc-macro2 1.0.79",
 "quote 1.0.35",
 "unicode-ident",
]

[[package]]
name = "syn"
version = "2.0.57"
source = "registry+https://github.com/rust-lang/crates.io-index"
checksum = "11a6ae1e52eb25aab8f3fb9fca13be982a373b8f1157ca14b897a825ba4a2d35"
dependencies = [
 "proc-macro2 1.0.79",
 "quote 1.0.35",
 "unicode-ident",
]

[[package]]
name = "syn-solidity"
version = "0.6.4"
source = "registry+https://github.com/rust-lang/crates.io-index"
checksum = "cb3d0961cd53c23ea94eeec56ba940f636f6394788976e9f16ca5ee0aca7464a"
dependencies = [
 "paste",
 "proc-macro2 1.0.79",
 "quote 1.0.35",
 "syn 2.0.57",
]

[[package]]
name = "sync_wrapper"
version = "0.1.2"
source = "registry+https://github.com/rust-lang/crates.io-index"
checksum = "2047c6ded9c721764247e62cd3b03c09ffc529b2ba5b10ec482ae507a4a70160"

[[package]]
name = "synstructure"
version = "0.12.6"
source = "registry+https://github.com/rust-lang/crates.io-index"
checksum = "f36bdaa60a83aca3921b5259d5400cbf5e90fc51931376a9bd4a0eb79aa7210f"
dependencies = [
 "proc-macro2 1.0.79",
 "quote 1.0.35",
 "syn 1.0.109",
 "unicode-xid 0.2.4",
]

[[package]]
name = "sysinfo"
version = "0.27.8"
source = "registry+https://github.com/rust-lang/crates.io-index"
checksum = "a902e9050fca0a5d6877550b769abd2bd1ce8c04634b941dbe2809735e1a1e33"
dependencies = [
 "cfg-if 1.0.0",
 "core-foundation-sys",
 "libc",
 "ntapi 0.4.1",
 "once_cell",
 "rayon",
 "winapi",
]

[[package]]
name = "system-configuration"
version = "0.5.1"
source = "registry+https://github.com/rust-lang/crates.io-index"
checksum = "ba3a3adc5c275d719af8cb4272ea1c4a6d668a777f37e115f6d11ddbc1c8e0e7"
dependencies = [
 "bitflags 1.3.2",
 "core-foundation",
 "system-configuration-sys",
]

[[package]]
name = "system-configuration-sys"
version = "0.5.0"
source = "registry+https://github.com/rust-lang/crates.io-index"
checksum = "a75fb188eb626b924683e3b95e3a48e63551fcfb51949de2f06a9d91dbee93c9"
dependencies = [
 "core-foundation-sys",
 "libc",
]

[[package]]
name = "tabled"
version = "0.12.2"
source = "registry+https://github.com/rust-lang/crates.io-index"
checksum = "0ce69a5028cd9576063ec1f48edb2c75339fd835e6094ef3e05b3a079bf594a6"
dependencies = [
 "papergrid",
 "tabled_derive",
 "unicode-width",
]

[[package]]
name = "tabled_derive"
version = "0.6.0"
source = "registry+https://github.com/rust-lang/crates.io-index"
checksum = "99f688a08b54f4f02f0a3c382aefdb7884d3d69609f785bd253dc033243e3fe4"
dependencies = [
 "heck 0.4.1",
 "proc-macro-error",
 "proc-macro2 1.0.79",
 "quote 1.0.35",
 "syn 1.0.109",
]

[[package]]
name = "tabular"
version = "0.2.0"
source = "registry+https://github.com/rust-lang/crates.io-index"
checksum = "d9a2882c514780a1973df90de9d68adcd8871bacc9a6331c3f28e6d2ff91a3d1"
dependencies = [
 "strip-ansi-escapes",
 "unicode-width",
]

[[package]]
name = "tap"
version = "1.0.1"
source = "registry+https://github.com/rust-lang/crates.io-index"
checksum = "55937e1799185b12863d447f42597ed69d9928686b8d88a1df17376a097d8369"

[[package]]
name = "target-lexicon"
version = "0.12.14"
source = "registry+https://github.com/rust-lang/crates.io-index"
checksum = "e1fc403891a21bcfb7c37834ba66a547a8f402146eba7265b5a6d88059c9ff2f"

[[package]]
name = "target-spec"
version = "1.4.0"
source = "registry+https://github.com/rust-lang/crates.io-index"
checksum = "0bf4306559bd50cb358e7af5692694d6f6fad95cf2c0bea2571dd419f5298e12"
dependencies = [
 "cfg-expr 0.15.7",
 "guppy-workspace-hack",
 "serde",
 "target-lexicon",
]

[[package]]
name = "tempfile"
version = "3.10.1"
source = "registry+https://github.com/rust-lang/crates.io-index"
checksum = "85b77fafb263dd9d05cbeac119526425676db3784113aa9295c88498cbf8bff1"
dependencies = [
 "cfg-if 1.0.0",
 "fastrand 2.0.2",
 "rustix 0.38.32",
 "windows-sys 0.52.0",
]

[[package]]
name = "tera"
version = "1.19.1"
source = "registry+https://github.com/rust-lang/crates.io-index"
checksum = "970dff17c11e884a4a09bc76e3a17ef71e01bb13447a11e85226e254fe6d10b8"
dependencies = [
 "chrono",
 "chrono-tz 0.8.6",
 "globwalk",
 "humansize 2.1.3",
 "lazy_static",
 "percent-encoding",
 "pest",
 "pest_derive",
 "rand 0.8.5",
 "regex",
 "serde",
 "serde_json",
 "slug",
 "unic-segment",
]

[[package]]
name = "term"
version = "0.7.0"
source = "registry+https://github.com/rust-lang/crates.io-index"
checksum = "c59df8ac95d96ff9bede18eb7300b0fda5e5d8d90960e76f8e14ae765eedbf1f"
dependencies = [
 "dirs-next",
 "rustversion",
 "winapi",
]

[[package]]
name = "termcolor"
version = "1.1.3"
source = "registry+https://github.com/rust-lang/crates.io-index"
checksum = "bab24d30b911b2376f3a13cc2cd443142f0c81dda04c118693e35b3835757755"
dependencies = [
 "winapi-util",
]

[[package]]
name = "terminal_size"
version = "0.3.0"
source = "registry+https://github.com/rust-lang/crates.io-index"
checksum = "21bebf2b7c9e0a515f6e0f8c51dc0f8e4696391e6f1ff30379559f8365fb0df7"
dependencies = [
 "rustix 0.38.32",
 "windows-sys 0.48.0",
]

[[package]]
name = "termtree"
version = "0.4.1"
source = "registry+https://github.com/rust-lang/crates.io-index"
checksum = "3369f5ac52d5eb6ab48c6b4ffdc8efbcad6b89c765749064ba298f2c68a16a76"

[[package]]
name = "test-fuzz"
version = "3.1.0"
source = "registry+https://github.com/rust-lang/crates.io-index"
checksum = "857e884d611b1f26e63f00559975d348491e66b1271a8144a9806c9bd4a791cf"
dependencies = [
 "serde",
 "test-fuzz-internal",
 "test-fuzz-macro",
 "test-fuzz-runtime",
]

[[package]]
name = "test-fuzz-internal"
version = "3.1.0"
source = "registry+https://github.com/rust-lang/crates.io-index"
checksum = "48db3bbc562408b2111f3a0c96ec416ffa3ab66f8a6ab42579b608b9f74744e1"
dependencies = [
 "cargo_metadata 0.15.4",
 "proc-macro2 1.0.79",
 "quote 1.0.35",
 "serde",
 "strum_macros 0.24.3",
]

[[package]]
name = "test-fuzz-macro"
version = "3.1.0"
source = "registry+https://github.com/rust-lang/crates.io-index"
checksum = "e17cd05c077c7b9c5d0045c539f9c5e911187bf65cd1b407d28239efc7b54880"
dependencies = [
 "darling 0.20.8",
 "if_chain",
 "itertools 0.10.5",
 "lazy_static",
 "proc-macro2 1.0.79",
 "quote 1.0.35",
 "subprocess",
 "syn 2.0.57",
 "test-fuzz-internal",
 "toolchain_find 0.3.0",
]

[[package]]
name = "test-fuzz-runtime"
version = "3.1.0"
source = "registry+https://github.com/rust-lang/crates.io-index"
checksum = "53d68728ca22d1a96a71645fd2327e37821b8979a7e75e44ad24a72e8051513d"
dependencies = [
 "bincode",
 "hex",
 "num-traits",
 "serde",
 "sha-1 0.10.1",
 "test-fuzz-internal",
]

[[package]]
name = "textwrap"
version = "0.11.0"
source = "registry+https://github.com/rust-lang/crates.io-index"
checksum = "d326610f408c7a4eb6f51c37c330e496b08506c9457c9d34287ecc38809fb060"
dependencies = [
 "unicode-width",
]

[[package]]
name = "textwrap"
version = "0.16.1"
source = "registry+https://github.com/rust-lang/crates.io-index"
checksum = "23d434d3f8967a09480fb04132ebe0a3e088c173e6d0ee7897abbdf4eab0f8b9"

[[package]]
name = "thiserror"
version = "1.0.58"
source = "registry+https://github.com/rust-lang/crates.io-index"
checksum = "03468839009160513471e86a034bb2c5c0e4baae3b43f79ffc55c4a5427b3297"
dependencies = [
 "thiserror-impl",
]

[[package]]
name = "thiserror-impl"
version = "1.0.58"
source = "registry+https://github.com/rust-lang/crates.io-index"
checksum = "c61f3ba182994efc43764a46c018c347bc492c79f024e705f46567b418f6d4f7"
dependencies = [
 "proc-macro2 1.0.79",
 "quote 1.0.35",
 "syn 2.0.57",
]

[[package]]
name = "thread_local"
version = "1.1.8"
source = "registry+https://github.com/rust-lang/crates.io-index"
checksum = "8b9ef9bad013ada3808854ceac7b46812a6465ba368859a37e2100283d2d719c"
dependencies = [
 "cfg-if 1.0.0",
 "once_cell",
]

[[package]]
name = "threadpool"
version = "1.8.1"
source = "registry+https://github.com/rust-lang/crates.io-index"
checksum = "d050e60b33d41c19108b32cea32164033a9013fe3b46cbd4457559bfbf77afaa"
dependencies = [
 "num_cpus",
]

[[package]]
name = "time"
version = "0.1.43"
source = "registry+https://github.com/rust-lang/crates.io-index"
checksum = "ca8a50ef2360fbd1eeb0ecd46795a87a19024eb4b53c5dc916ca1fd95fe62438"
dependencies = [
 "libc",
 "winapi",
]

[[package]]
name = "time"
version = "0.3.34"
source = "registry+https://github.com/rust-lang/crates.io-index"
checksum = "c8248b6521bb14bc45b4067159b9b6ad792e2d6d754d6c41fb50e29fefe38749"
dependencies = [
 "deranged",
 "itoa",
 "num-conv",
 "powerfmt",
 "serde",
 "time-core",
 "time-macros",
]

[[package]]
name = "time-core"
version = "0.1.2"
source = "registry+https://github.com/rust-lang/crates.io-index"
checksum = "ef927ca75afb808a4d64dd374f00a2adf8d0fcff8e7b184af886c3c87ec4a3f3"

[[package]]
name = "time-macros"
version = "0.2.17"
source = "registry+https://github.com/rust-lang/crates.io-index"
checksum = "7ba3a3ef41e6672a2f0f001392bb5dcd3ff0a9992d618ca761a11c3121547774"
dependencies = [
 "num-conv",
 "time-core",
]

[[package]]
name = "tiny-bip39"
version = "1.0.0"
source = "registry+https://github.com/rust-lang/crates.io-index"
checksum = "62cc94d358b5a1e84a5cb9109f559aa3c4d634d2b1b4de3d0fa4adc7c78e2861"
dependencies = [
 "anyhow",
 "hmac 0.12.1",
 "once_cell",
 "pbkdf2 0.11.0",
 "rand 0.8.5",
 "rustc-hash",
 "sha2 0.10.8",
 "thiserror",
 "unicode-normalization",
 "wasm-bindgen",
 "zeroize",
]

[[package]]
name = "tiny-keccak"
version = "2.0.2"
source = "registry+https://github.com/rust-lang/crates.io-index"
checksum = "2c9d3793400a45f954c52e73d068316d76b6f4e36977e3fcebb13a2721e80237"
dependencies = [
 "crunchy",
]

[[package]]
name = "tinytemplate"
version = "1.2.1"
source = "registry+https://github.com/rust-lang/crates.io-index"
checksum = "be4d6b5f19ff7664e8c98d03e2139cb510db9b0a60b55f8e8709b689d939b6bc"
dependencies = [
 "serde",
 "serde_json",
]

[[package]]
name = "tinyvec"
version = "1.6.0"
source = "registry+https://github.com/rust-lang/crates.io-index"
checksum = "87cc5ceb3875bb20c2890005a4e226a4651264a5c75edb2421b52861a0a0cb50"
dependencies = [
 "tinyvec_macros",
]

[[package]]
name = "tinyvec_macros"
version = "0.1.1"
source = "registry+https://github.com/rust-lang/crates.io-index"
checksum = "1f3ccbac311fea05f86f61904b462b55fb3df8837a366dfc601a0161d0532f20"

[[package]]
name = "to_method"
version = "1.1.0"
source = "registry+https://github.com/rust-lang/crates.io-index"
checksum = "c7c4ceeeca15c8384bbc3e011dbd8fccb7f068a440b752b7d9b32ceb0ca0e2e8"

[[package]]
name = "tokio"
version = "1.39.2"
source = "registry+https://github.com/rust-lang/crates.io-index"
checksum = "daa4fb1bc778bd6f04cbfc4bb2d06a7396a8f299dc33ea1900cedaa316f467b1"
dependencies = [
 "backtrace",
 "bytes",
 "libc",
 "mio 1.0.1",
 "parking_lot 0.12.1",
 "pin-project-lite",
 "signal-hook-registry",
 "socket2 0.5.6",
 "tokio-macros 2.4.0",
 "tracing",
 "windows-sys 0.52.0",
]

[[package]]
name = "tokio-io-timeout"
version = "1.2.0"
source = "registry+https://github.com/rust-lang/crates.io-index"
checksum = "30b74022ada614a1b4834de765f9bb43877f910cc8ce4be40e89042c9223a8bf"
dependencies = [
 "pin-project-lite",
 "tokio",
]

[[package]]
name = "tokio-macros"
version = "2.1.0"
source = "git+https://github.com/mystenmark/tokio-madsim-fork.git?rev=e4693500118d5e79ce098ee6dfc2c48f3ef19e45#e4693500118d5e79ce098ee6dfc2c48f3ef19e45"
dependencies = [
 "proc-macro2 1.0.79",
 "quote 1.0.35",
 "syn 2.0.57",
]

[[package]]
name = "tokio-macros"
version = "2.4.0"
source = "registry+https://github.com/rust-lang/crates.io-index"
checksum = "693d596312e88961bc67d7f1f97af8a70227d9f90c31bba5806eec004978d752"
dependencies = [
 "proc-macro2 1.0.79",
 "quote 1.0.35",
 "syn 2.0.57",
]

[[package]]
name = "tokio-native-tls"
version = "0.3.1"
source = "registry+https://github.com/rust-lang/crates.io-index"
checksum = "bbae76ab933c85776efabc971569dd6119c580d8f5d448769dec1764bf796ef2"
dependencies = [
 "native-tls",
 "tokio",
]

[[package]]
name = "tokio-retry"
version = "0.3.0"
source = "registry+https://github.com/rust-lang/crates.io-index"
checksum = "7f57eb36ecbe0fc510036adff84824dd3c24bb781e21bfa67b69d556aa85214f"
dependencies = [
 "pin-project",
 "rand 0.8.5",
 "tokio",
]

[[package]]
name = "tokio-rustls"
version = "0.23.4"
source = "registry+https://github.com/rust-lang/crates.io-index"
checksum = "c43ee83903113e03984cb9e5cebe6c04a5116269e900e3ddba8f068a62adda59"
dependencies = [
 "rustls 0.20.9",
 "tokio",
 "webpki",
]

[[package]]
name = "tokio-rustls"
version = "0.24.1"
source = "registry+https://github.com/rust-lang/crates.io-index"
checksum = "c28327cf380ac148141087fbfb9de9d7bd4e84ab5d2c28fbc911d753de8a7081"
dependencies = [
 "rustls 0.21.10",
 "tokio",
]

[[package]]
name = "tokio-stream"
version = "0.1.15"
source = "registry+https://github.com/rust-lang/crates.io-index"
checksum = "267ac89e0bec6e691e5813911606935d77c476ff49024f98abcea3e7b15e37af"
dependencies = [
 "futures-core",
 "pin-project-lite",
 "tokio",
 "tokio-util 0.7.10",
]

[[package]]
name = "tokio-tungstenite"
version = "0.20.1"
source = "registry+https://github.com/rust-lang/crates.io-index"
checksum = "212d5dcb2a1ce06d81107c3d0ffa3121fe974b73f068c8282cb1c32328113b6c"
dependencies = [
 "futures-util",
 "log",
 "rustls 0.21.10",
 "tokio",
 "tokio-rustls 0.24.1",
 "tungstenite",
 "webpki-roots 0.25.4",
]

[[package]]
name = "tokio-util"
version = "0.7.7"
source = "git+https://github.com/mystenmark/tokio-madsim-fork.git?rev=e4693500118d5e79ce098ee6dfc2c48f3ef19e45#e4693500118d5e79ce098ee6dfc2c48f3ef19e45"
dependencies = [
 "bytes",
 "futures-core",
 "futures-io",
 "futures-sink",
 "futures-util",
 "hashbrown 0.12.3",
 "pin-project-lite",
 "real_tokio",
 "slab",
 "tracing",
]

[[package]]
name = "tokio-util"
version = "0.7.10"
source = "registry+https://github.com/rust-lang/crates.io-index"
checksum = "5419f34732d9eb6ee4c3578b7989078579b7f039cbbb9ca2c4da015749371e15"
dependencies = [
 "bytes",
 "futures-core",
 "futures-io",
 "futures-sink",
 "pin-project-lite",
 "tokio",
 "tracing",
]

[[package]]
name = "toml"
version = "0.5.11"
source = "registry+https://github.com/rust-lang/crates.io-index"
checksum = "f4f7f0dd8d50a853a531c426359045b1998f04219d88799810762cd4ad314234"
dependencies = [
 "indexmap 1.9.3",
 "serde",
]

[[package]]
name = "toml"
version = "0.7.8"
source = "registry+https://github.com/rust-lang/crates.io-index"
checksum = "dd79e69d3b627db300ff956027cc6c3798cef26d22526befdfcd12feeb6d2257"
dependencies = [
 "indexmap 2.2.6",
 "serde",
 "serde_spanned",
 "toml_datetime 0.6.5",
 "toml_edit 0.19.15",
]

[[package]]
name = "toml"
version = "0.8.12"
source = "registry+https://github.com/rust-lang/crates.io-index"
checksum = "e9dd1545e8208b4a5af1aa9bbd0b4cf7e9ea08fabc5d0a5c67fcaafa17433aa3"
dependencies = [
 "indexmap 2.2.6",
 "serde",
 "serde_spanned",
 "toml_datetime 0.6.5",
 "toml_edit 0.22.9",
]

[[package]]
name = "toml_datetime"
version = "0.5.1"
source = "registry+https://github.com/rust-lang/crates.io-index"
checksum = "4553f467ac8e3d374bc9a177a26801e5d0f9b211aa1673fb137a403afd1c9cf5"

[[package]]
name = "toml_datetime"
version = "0.6.5"
source = "registry+https://github.com/rust-lang/crates.io-index"
checksum = "3550f4e9685620ac18a50ed434eb3aec30db8ba93b0287467bca5826ea25baf1"
dependencies = [
 "serde",
]

[[package]]
name = "toml_edit"
version = "0.14.4"
source = "registry+https://github.com/rust-lang/crates.io-index"
checksum = "5376256e44f2443f8896ac012507c19a012df0fe8758b55246ae51a2279db51f"
dependencies = [
 "combine",
 "indexmap 1.9.3",
 "itertools 0.10.5",
 "serde",
]

[[package]]
name = "toml_edit"
version = "0.15.0"
source = "registry+https://github.com/rust-lang/crates.io-index"
checksum = "b1541ba70885967e662f69d31ab3aeca7b1aaecfcd58679590b893e9239c3646"
dependencies = [
 "combine",
 "indexmap 1.9.3",
 "itertools 0.10.5",
 "toml_datetime 0.5.1",
]

[[package]]
name = "toml_edit"
version = "0.17.1"
source = "registry+https://github.com/rust-lang/crates.io-index"
checksum = "a34cc558345efd7e88b9eda9626df2138b80bb46a7606f695e751c892bc7dac6"
dependencies = [
 "indexmap 1.9.3",
 "itertools 0.10.5",
 "nom8",
 "toml_datetime 0.5.1",
]

[[package]]
name = "toml_edit"
version = "0.19.15"
source = "registry+https://github.com/rust-lang/crates.io-index"
checksum = "1b5bb770da30e5cbfde35a2d7b9b8a2c4b8ef89548a7a6aeab5c9a576e3e7421"
dependencies = [
 "indexmap 2.2.6",
 "serde",
 "serde_spanned",
 "toml_datetime 0.6.5",
 "winnow 0.5.40",
]

[[package]]
name = "toml_edit"
version = "0.20.7"
source = "registry+https://github.com/rust-lang/crates.io-index"
checksum = "70f427fce4d84c72b5b732388bf4a9f4531b53f74e2887e3ecb2481f68f66d81"
dependencies = [
 "indexmap 2.2.6",
 "toml_datetime 0.6.5",
 "winnow 0.5.40",
]

[[package]]
name = "toml_edit"
version = "0.21.1"
source = "registry+https://github.com/rust-lang/crates.io-index"
checksum = "6a8534fd7f78b5405e860340ad6575217ce99f38d4d5c8f2442cb5ecb50090e1"
dependencies = [
 "indexmap 2.2.6",
 "toml_datetime 0.6.5",
 "winnow 0.5.40",
]

[[package]]
name = "toml_edit"
version = "0.22.9"
source = "registry+https://github.com/rust-lang/crates.io-index"
checksum = "8e40bb779c5187258fd7aad0eb68cb8706a0a81fa712fbea808ab43c4b8374c4"
dependencies = [
 "indexmap 2.2.6",
 "serde",
 "serde_spanned",
 "toml_datetime 0.6.5",
 "winnow 0.6.5",
]

[[package]]
name = "tonic"
version = "0.8.3"
source = "registry+https://github.com/rust-lang/crates.io-index"
checksum = "8f219fad3b929bef19b1f86fbc0358d35daed8f2cac972037ac0dc10bbb8d5fb"
dependencies = [
 "async-stream",
 "async-trait",
 "axum",
 "base64 0.13.1",
 "bytes",
 "futures-core",
 "futures-util",
 "h2",
 "http",
 "http-body",
 "hyper",
 "hyper-timeout",
 "percent-encoding",
 "pin-project",
 "prost",
 "prost-derive",
 "rustls-pemfile",
 "tokio",
 "tokio-rustls 0.23.4",
 "tokio-stream",
 "tokio-util 0.7.10",
 "tower",
 "tower-layer",
 "tower-service",
 "tracing",
 "tracing-futures",
]

[[package]]
name = "tonic"
version = "0.9.2"
source = "registry+https://github.com/rust-lang/crates.io-index"
checksum = "3082666a3a6433f7f511c7192923fa1fe07c69332d3c6a2e6bb040b569199d5a"
dependencies = [
 "async-trait",
 "axum",
 "base64 0.21.7",
 "bytes",
 "futures-core",
 "futures-util",
 "h2",
 "http",
 "http-body",
 "hyper",
 "hyper-timeout",
 "percent-encoding",
 "pin-project",
 "prost",
 "tokio",
 "tokio-stream",
 "tower",
 "tower-layer",
 "tower-service",
 "tracing",
]

[[package]]
name = "tonic-build"
version = "0.8.4"
source = "registry+https://github.com/rust-lang/crates.io-index"
checksum = "5bf5e9b9c0f7e0a7c027dcfaba7b2c60816c7049171f679d99ee2ff65d0de8c4"
dependencies = [
 "prettyplease 0.1.25",
 "proc-macro2 1.0.79",
 "prost-build",
 "quote 1.0.35",
 "syn 1.0.109",
]

[[package]]
name = "tonic-health"
version = "0.8.0"
source = "registry+https://github.com/rust-lang/crates.io-index"
checksum = "a88aee666ef3a4d1ee46218bbc8e5f69bcf9cc27bf2e871d6b724d83f56d179f"
dependencies = [
 "async-stream",
 "bytes",
 "prost",
 "tokio",
 "tokio-stream",
 "tonic 0.8.3",
]

[[package]]
name = "toolchain_find"
version = "0.2.0"
source = "registry+https://github.com/rust-lang/crates.io-index"
checksum = "5e85654a10e7a07a47c6f19d93818f3f343e22927f2fa280c84f7c8042743413"
dependencies = [
 "home",
 "lazy_static",
 "regex",
 "semver 0.11.0",
 "walkdir",
]

[[package]]
name = "toolchain_find"
version = "0.3.0"
source = "registry+https://github.com/rust-lang/crates.io-index"
checksum = "fa8c746c4e8d786ff304a6a73d7b406420d9a7c7d8292e090979dc85213113ed"
dependencies = [
 "home",
 "once_cell",
 "regex",
 "semver 1.0.22",
 "walkdir",
]

[[package]]
name = "tower"
version = "0.4.13"
source = "registry+https://github.com/rust-lang/crates.io-index"
checksum = "b8fa9be0de6cf49e536ce1851f987bd21a43b771b09473c3549a6c853db37c1c"
dependencies = [
 "futures-core",
 "futures-util",
 "hdrhistogram",
 "indexmap 1.9.3",
 "pin-project",
 "pin-project-lite",
 "rand 0.8.5",
 "slab",
 "tokio",
 "tokio-util 0.7.10",
 "tower-layer",
 "tower-service",
 "tracing",
]

[[package]]
name = "tower-http"
version = "0.3.5"
source = "registry+https://github.com/rust-lang/crates.io-index"
checksum = "f873044bf02dd1e8239e9c1293ea39dad76dc594ec16185d0a1bf31d8dc8d858"
dependencies = [
 "async-compression",
 "base64 0.13.1",
 "bitflags 1.3.2",
 "bytes",
 "futures-core",
 "futures-util",
 "http",
 "http-body",
 "http-range-header",
 "httpdate",
 "iri-string",
 "mime",
 "mime_guess",
 "percent-encoding",
 "pin-project-lite",
 "tokio",
 "tokio-util 0.7.10",
 "tower",
 "tower-layer",
 "tower-service",
 "tracing",
 "uuid 1.8.0",
]

[[package]]
name = "tower-layer"
version = "0.3.2"
source = "registry+https://github.com/rust-lang/crates.io-index"
checksum = "c20c8dbed6283a09604c3e69b4b7eeb54e298b8a600d4d5ecb5ad39de609f1d0"

[[package]]
name = "tower-service"
version = "0.3.2"
source = "registry+https://github.com/rust-lang/crates.io-index"
checksum = "b6bc1c9ce2b5135ac7f93c72918fc37feb872bdc6a5533a8b85eb4b86bfdae52"

[[package]]
name = "tracing"
version = "0.1.40"
source = "registry+https://github.com/rust-lang/crates.io-index"
checksum = "c3523ab5a71916ccf420eebdf5521fcef02141234bbc0b8a49f2fdc4544364ef"
dependencies = [
 "log",
 "pin-project-lite",
 "tracing-attributes",
 "tracing-core",
]

[[package]]
name = "tracing-appender"
version = "0.2.3"
source = "registry+https://github.com/rust-lang/crates.io-index"
checksum = "3566e8ce28cc0a3fe42519fc80e6b4c943cc4c8cef275620eb8dac2d3d4e06cf"
dependencies = [
 "crossbeam-channel",
 "thiserror",
 "time 0.3.34",
 "tracing-subscriber 0.3.18",
]

[[package]]
name = "tracing-attributes"
version = "0.1.27"
source = "registry+https://github.com/rust-lang/crates.io-index"
checksum = "34704c8d6ebcbc939824180af020566b01a7c01f80641264eba0999f6c2b6be7"
dependencies = [
 "proc-macro2 1.0.79",
 "quote 1.0.35",
 "syn 2.0.57",
]

[[package]]
name = "tracing-core"
version = "0.1.32"
source = "registry+https://github.com/rust-lang/crates.io-index"
checksum = "c06d3da6113f116aaee68e4d601191614c9053067f9ab7f6edbcb161237daa54"
dependencies = [
 "once_cell",
 "valuable",
]

[[package]]
name = "tracing-futures"
version = "0.2.5"
source = "registry+https://github.com/rust-lang/crates.io-index"
checksum = "97d095ae15e245a057c8e8451bab9b3ee1e1f68e9ba2b4fbc18d0ac5237835f2"
dependencies = [
 "pin-project",
 "tracing",
]

[[package]]
name = "tracing-log"
version = "0.2.0"
source = "registry+https://github.com/rust-lang/crates.io-index"
checksum = "ee855f1f400bd0e5c02d150ae5de3840039a3f54b025156404e34c23c03f47c3"
dependencies = [
 "log",
 "once_cell",
 "tracing-core",
]

[[package]]
name = "tracing-serde"
version = "0.1.3"
source = "registry+https://github.com/rust-lang/crates.io-index"
checksum = "bc6b213177105856957181934e4920de57730fc69bf42c37ee5bb664d406d9e1"
dependencies = [
 "serde",
 "tracing-core",
]

[[package]]
name = "tracing-subscriber"
version = "0.2.25"
source = "registry+https://github.com/rust-lang/crates.io-index"
checksum = "0e0d2eaa99c3c2e41547cfa109e910a68ea03823cccad4a0525dcbc9b01e8c71"
dependencies = [
 "tracing-core",
]

[[package]]
name = "tracing-subscriber"
version = "0.3.18"
source = "registry+https://github.com/rust-lang/crates.io-index"
checksum = "ad0f048c97dbd9faa9b7df56362b8ebcaa52adb06b498c050d2f4e32f90a7a8b"
dependencies = [
 "matchers",
 "nu-ansi-term",
 "once_cell",
 "regex",
 "serde",
 "serde_json",
 "sharded-slab",
 "smallvec",
 "thread_local",
 "time 0.3.34",
 "tracing",
 "tracing-core",
 "tracing-log",
 "tracing-serde",
]

[[package]]
name = "treeline"
version = "0.1.0"
source = "registry+https://github.com/rust-lang/crates.io-index"
checksum = "a7f741b240f1a48843f9b8e0444fb55fb2a4ff67293b50a9179dfd5ea67f8d41"

[[package]]
name = "trezor-client"
version = "0.1.3"
source = "registry+https://github.com/rust-lang/crates.io-index"
checksum = "f62c95b37f6c769bd65a0d0beb8b2b003e72998003b896a616a6777c645c05ed"
dependencies = [
 "byteorder",
 "hex",
 "protobuf 3.3.0",
 "rusb",
 "thiserror",
 "tracing",
]

[[package]]
name = "try-lock"
version = "0.2.5"
source = "registry+https://github.com/rust-lang/crates.io-index"
checksum = "e421abadd41a4225275504ea4d6566923418b7f05506fbc9c0fe86ba7396114b"

[[package]]
name = "ttl_cache"
version = "0.5.1"
source = "registry+https://github.com/rust-lang/crates.io-index"
checksum = "4189890526f0168710b6ee65ceaedf1460c48a14318ceec933cb26baa492096a"
dependencies = [
 "linked-hash-map",
]

[[package]]
name = "tui"
version = "0.17.0"
source = "registry+https://github.com/rust-lang/crates.io-index"
checksum = "23ed0a32c88b039b73f1b6c5acbd0554bfa5b6be94467375fd947c4de3a02271"
dependencies = [
 "bitflags 1.3.2",
 "cassowary",
 "crossterm 0.22.1",
 "unicode-segmentation",
 "unicode-width",
]

[[package]]
name = "tungstenite"
version = "0.20.1"
source = "registry+https://github.com/rust-lang/crates.io-index"
checksum = "9e3dac10fd62eaf6617d3a904ae222845979aec67c615d1c842b4002c7666fb9"
dependencies = [
 "byteorder",
 "bytes",
 "data-encoding",
 "http",
 "httparse",
 "log",
 "rand 0.8.5",
 "rustls 0.21.10",
 "sha1",
 "thiserror",
 "url",
 "utf-8",
]

[[package]]
name = "tuple_list"
version = "0.1.3"
source = "registry+https://github.com/rust-lang/crates.io-index"
checksum = "141fb9f71ee586d956d7d6e4d5a9ef8e946061188520140f7591b668841d502e"

[[package]]
name = "twox-hash"
version = "1.6.3"
source = "registry+https://github.com/rust-lang/crates.io-index"
checksum = "97fee6b57c6a41524a810daee9286c02d7752c4253064d0b05472833a438f675"
dependencies = [
 "cfg-if 1.0.0",
 "static_assertions",
]

[[package]]
name = "typed-arena"
version = "2.0.2"
source = "registry+https://github.com/rust-lang/crates.io-index"
checksum = "6af6ae20167a9ece4bcb41af5b80f8a1f1df981f6391189ce00fd257af04126a"

[[package]]
name = "typed-builder"
version = "0.16.2"
source = "registry+https://github.com/rust-lang/crates.io-index"
checksum = "34085c17941e36627a879208083e25d357243812c30e7d7387c3b954f30ade16"
dependencies = [
 "typed-builder-macro",
]

[[package]]
name = "typed-builder-macro"
version = "0.16.2"
source = "registry+https://github.com/rust-lang/crates.io-index"
checksum = "f03ca4cb38206e2bef0700092660bb74d696f808514dae47fa1467cbfe26e96e"
dependencies = [
 "proc-macro2 1.0.79",
 "quote 1.0.35",
 "syn 2.0.57",
]

[[package]]
name = "typed-store"
version = "0.4.0"
source = "git+https://github.com/fuzzland/ityfuzz-sui-fork.git#a88e9595de3c04a540c059d5f4b6d148419c508c"
dependencies = [
 "async-trait",
 "bcs",
 "bincode",
 "collectable",
 "eyre",
 "fdlimit",
 "hdrhistogram",
 "itertools 0.10.5",
 "msim",
 "num_cpus",
 "once_cell",
 "ouroboros 0.15.6",
 "prometheus",
 "rand 0.8.5",
 "rocksdb",
 "serde",
 "sui-macros",
 "tap",
 "thiserror",
 "tokio",
 "tracing",
 "workspace-hack",
]

[[package]]
name = "typenum"
version = "1.17.0"
source = "registry+https://github.com/rust-lang/crates.io-index"
checksum = "42ff0bf0c66b8238c6f3b578df37d0b7848e55df8577b3f74f92a69acceeb825"

[[package]]
name = "typetag"
version = "0.2.16"
source = "registry+https://github.com/rust-lang/crates.io-index"
checksum = "661d18414ec032a49ece2d56eee03636e43c4e8d577047ab334c0ba892e29aaf"
dependencies = [
 "erased-serde 0.4.4",
 "inventory",
 "once_cell",
 "serde",
 "typetag-impl",
]

[[package]]
name = "typetag-impl"
version = "0.2.16"
source = "registry+https://github.com/rust-lang/crates.io-index"
checksum = "ac73887f47b9312552aa90ef477927ff014d63d1920ca8037c6c1951eab64bb1"
dependencies = [
 "proc-macro2 1.0.79",
 "quote 1.0.35",
 "syn 2.0.57",
]

[[package]]
name = "ucd-trie"
version = "0.1.6"
source = "registry+https://github.com/rust-lang/crates.io-index"
checksum = "ed646292ffc8188ef8ea4d1e0e0150fb15a5c2e12ad9b8fc191ae7a8a7f3c4b9"

[[package]]
name = "uds"
version = "0.4.2"
source = "registry+https://github.com/rust-lang/crates.io-index"
checksum = "885c31f06fce836457fe3ef09a59f83fe8db95d270b11cd78f40a4666c4d1661"
dependencies = [
 "libc",
]

[[package]]
name = "uint"
version = "0.9.5"
source = "registry+https://github.com/rust-lang/crates.io-index"
checksum = "76f64bba2c53b04fcab63c01a7d7427eadc821e3bc48c34dc9ba29c501164b52"
dependencies = [
 "arbitrary",
 "byteorder",
 "crunchy",
 "hex",
 "static_assertions",
]

[[package]]
name = "uname"
version = "0.1.1"
source = "registry+https://github.com/rust-lang/crates.io-index"
checksum = "b72f89f0ca32e4db1c04e2a72f5345d59796d4866a1ee0609084569f73683dc8"
dependencies = [
 "libc",
]

[[package]]
name = "unarray"
version = "0.1.4"
source = "registry+https://github.com/rust-lang/crates.io-index"
checksum = "eaea85b334db583fe3274d12b4cd1880032beab409c0d774be044d4480ab9a94"

[[package]]
name = "uncased"
version = "0.9.10"
source = "registry+https://github.com/rust-lang/crates.io-index"
checksum = "e1b88fcfe09e89d3866a5c11019378088af2d24c3fbd4f0543f96b479ec90697"
dependencies = [
 "version_check",
]

[[package]]
name = "unescape"
version = "0.1.0"
source = "registry+https://github.com/rust-lang/crates.io-index"
checksum = "ccb97dac3243214f8d8507998906ca3e2e0b900bf9bf4870477f125b82e68f6e"

[[package]]
name = "unic-char-property"
version = "0.9.0"
source = "registry+https://github.com/rust-lang/crates.io-index"
checksum = "a8c57a407d9b6fa02b4795eb81c5b6652060a15a7903ea981f3d723e6c0be221"
dependencies = [
 "unic-char-range",
]

[[package]]
name = "unic-char-range"
version = "0.9.0"
source = "registry+https://github.com/rust-lang/crates.io-index"
checksum = "0398022d5f700414f6b899e10b8348231abf9173fa93144cbc1a43b9793c1fbc"

[[package]]
name = "unic-common"
version = "0.9.0"
source = "registry+https://github.com/rust-lang/crates.io-index"
checksum = "80d7ff825a6a654ee85a63e80f92f054f904f21e7d12da4e22f9834a4aaa35bc"

[[package]]
name = "unic-segment"
version = "0.9.0"
source = "registry+https://github.com/rust-lang/crates.io-index"
checksum = "e4ed5d26be57f84f176157270c112ef57b86debac9cd21daaabbe56db0f88f23"
dependencies = [
 "unic-ucd-segment",
]

[[package]]
name = "unic-ucd-segment"
version = "0.9.0"
source = "registry+https://github.com/rust-lang/crates.io-index"
checksum = "2079c122a62205b421f499da10f3ee0f7697f012f55b675e002483c73ea34700"
dependencies = [
 "unic-char-property",
 "unic-char-range",
 "unic-ucd-version",
]

[[package]]
name = "unic-ucd-version"
version = "0.9.0"
source = "registry+https://github.com/rust-lang/crates.io-index"
checksum = "96bd2f2237fe450fcd0a1d2f5f4e91711124f7857ba2e964247776ebeeb7b0c4"
dependencies = [
 "unic-common",
]

[[package]]
name = "unicase"
version = "2.7.0"
source = "registry+https://github.com/rust-lang/crates.io-index"
checksum = "f7d2d4dafb69621809a81864c9c1b864479e1235c0dd4e199924b9742439ed89"
dependencies = [
 "version_check",
]

[[package]]
name = "unicode-bidi"
version = "0.3.15"
source = "registry+https://github.com/rust-lang/crates.io-index"
checksum = "08f95100a766bf4f8f28f90d77e0a5461bbdb219042e7679bebe79004fed8d75"

[[package]]
name = "unicode-ident"
version = "1.0.12"
source = "registry+https://github.com/rust-lang/crates.io-index"
checksum = "3354b9ac3fae1ff6755cb6db53683adb661634f67557942dea4facebec0fee4b"

[[package]]
name = "unicode-normalization"
version = "0.1.23"
source = "registry+https://github.com/rust-lang/crates.io-index"
checksum = "a56d1686db2308d901306f92a263857ef59ea39678a5458e7cb17f01415101f5"
dependencies = [
 "tinyvec",
]

[[package]]
name = "unicode-segmentation"
version = "1.11.0"
source = "registry+https://github.com/rust-lang/crates.io-index"
checksum = "d4c87d22b6e3f4a18d4d40ef354e97c90fcb14dd91d7dc0aa9d8a1172ebf7202"

[[package]]
name = "unicode-width"
version = "0.1.11"
source = "registry+https://github.com/rust-lang/crates.io-index"
checksum = "e51733f11c9c4f72aa0c160008246859e340b00807569a0da0e7a1079b27ba85"

[[package]]
name = "unicode-xid"
version = "0.1.0"
source = "registry+https://github.com/rust-lang/crates.io-index"
checksum = "fc72304796d0818e357ead4e000d19c9c174ab23dc11093ac919054d20a6a7fc"

[[package]]
name = "unicode-xid"
version = "0.2.4"
source = "registry+https://github.com/rust-lang/crates.io-index"
checksum = "f962df74c8c05a667b5ee8bcf162993134c104e96440b663c8daa176dc772d8c"

[[package]]
name = "universal-hash"
version = "0.5.1"
source = "registry+https://github.com/rust-lang/crates.io-index"
checksum = "fc1de2c688dc15305988b563c3854064043356019f97a4b46276fe734c4f07ea"
dependencies = [
 "crypto-common",
 "subtle",
]

[[package]]
name = "unsafe-libyaml"
version = "0.2.11"
source = "registry+https://github.com/rust-lang/crates.io-index"
checksum = "673aac59facbab8a9007c7f6108d11f63b603f7cabff99fabf650fea5c32b861"

[[package]]
name = "unsigned-varint"
version = "0.7.2"
source = "registry+https://github.com/rust-lang/crates.io-index"
checksum = "6889a77d49f1f013504cec6bf97a2c730394adedaeb1deb5ea08949a50541105"

[[package]]
name = "untrusted"
version = "0.7.1"
source = "registry+https://github.com/rust-lang/crates.io-index"
checksum = "a156c684c91ea7d62626509bce3cb4e1d9ed5c4d978f7b4352658f96a4c26b4a"

[[package]]
name = "untrusted"
version = "0.9.0"
source = "registry+https://github.com/rust-lang/crates.io-index"
checksum = "8ecb6da28b8a351d773b68d5825ac39017e680750f980f3a1a85cd8dd28a47c1"

[[package]]
name = "unzip-n"
version = "0.1.2"
source = "registry+https://github.com/rust-lang/crates.io-index"
checksum = "c2e7e85a0596447f0f2ac090e16bc4c516c6fe91771fb0c0ccf7fa3dae896b9c"
dependencies = [
 "proc-macro2 1.0.79",
 "quote 1.0.35",
 "syn 1.0.109",
]

[[package]]
name = "ureq"
version = "2.9.6"
source = "registry+https://github.com/rust-lang/crates.io-index"
checksum = "11f214ce18d8b2cbe84ed3aa6486ed3f5b285cf8d8fbdbce9f3f767a724adc35"
dependencies = [
 "base64 0.21.7",
 "log",
 "native-tls",
 "once_cell",
 "url",
]

[[package]]
name = "url"
version = "2.5.0"
source = "registry+https://github.com/rust-lang/crates.io-index"
checksum = "31e6302e3bb753d46e83516cae55ae196fc0c309407cf11ab35cc51a4c2a4633"
dependencies = [
 "form_urlencoded",
 "idna 0.5.0",
 "percent-encoding",
 "serde",
]

[[package]]
name = "urlencoding"
version = "2.1.3"
source = "registry+https://github.com/rust-lang/crates.io-index"
checksum = "daf8dba3b7eb870caf1ddeed7bc9d2a049f3cfdfae7cb521b087cc33ae4c49da"

[[package]]
name = "utf-8"
version = "0.7.6"
source = "registry+https://github.com/rust-lang/crates.io-index"
checksum = "09cc8ee72d2a9becf2f2febe0205bbed8fc6615b7cb429ad062dc7b7ddd036a9"

[[package]]
name = "utf8parse"
version = "0.2.1"
source = "registry+https://github.com/rust-lang/crates.io-index"
checksum = "711b9620af191e0cdc7468a8d14e709c3dcdb115b36f838e601583af800a370a"

[[package]]
name = "uuid"
version = "0.8.2"
source = "registry+https://github.com/rust-lang/crates.io-index"
checksum = "bc5cf98d8186244414c848017f0e2676b3fcb46807f6668a97dfe67359a3c4b7"
dependencies = [
 "getrandom 0.2.12",
 "serde",
]

[[package]]
name = "uuid"
version = "1.8.0"
source = "registry+https://github.com/rust-lang/crates.io-index"
checksum = "a183cf7feeba97b4dd1c0d46788634f6221d87fa961b305bed08c851829efcc0"
dependencies = [
 "getrandom 0.2.12",
 "rand 0.8.5",
 "serde",
]

[[package]]
name = "valuable"
version = "0.1.0"
source = "registry+https://github.com/rust-lang/crates.io-index"
checksum = "830b7e5d4d90034032940e4ace0d9a9a057e7a45cd94e6c007832e39edb82f6d"

[[package]]
name = "variant_count"
version = "1.1.0"
source = "registry+https://github.com/rust-lang/crates.io-index"
checksum = "aae2faf80ac463422992abf4de234731279c058aaf33171ca70277c98406b124"
dependencies = [
 "quote 1.0.35",
 "syn 1.0.109",
]

[[package]]
name = "vcpkg"
version = "0.2.15"
source = "registry+https://github.com/rust-lang/crates.io-index"
checksum = "accd4ea62f7bb7a82fe23066fb0957d48ef677f6eeb8215f372f52e48bb32426"

[[package]]
name = "vec_map"
version = "0.8.2"
source = "registry+https://github.com/rust-lang/crates.io-index"
checksum = "f1bddf1187be692e79c5ffeab891132dfb0f236ed36a43c7ed39f1165ee20191"

[[package]]
name = "version_check"
version = "0.9.4"
source = "registry+https://github.com/rust-lang/crates.io-index"
checksum = "49874b5167b65d7193b8aba1567f5c7d93d001cafc34600cee003eda787e483f"

[[package]]
name = "versions"
version = "4.1.0"
source = "registry+https://github.com/rust-lang/crates.io-index"
checksum = "ee97e1d97bd593fb513912a07691b742361b3dd64ad56f2c694ea2dbfe0665d3"
dependencies = [
 "itertools 0.10.5",
 "nom 7.1.3",
]

[[package]]
name = "void"
version = "1.0.2"
source = "registry+https://github.com/rust-lang/crates.io-index"
checksum = "6a02e4885ed3bc0f2de90ea6dd45ebcbb66dacffe03547fadbb0eeae2770887d"

[[package]]
name = "vsimd"
version = "0.8.0"
source = "registry+https://github.com/rust-lang/crates.io-index"
checksum = "5c3082ca00d5a5ef149bb8b555a72ae84c9c59f7250f013ac822ac2e49b19c64"

[[package]]
name = "vte"
version = "0.10.1"
source = "registry+https://github.com/rust-lang/crates.io-index"
checksum = "6cbce692ab4ca2f1f3047fcf732430249c0e971bfdd2b234cf2c47ad93af5983"
dependencies = [
 "arrayvec 0.5.2",
 "utf8parse",
 "vte_generate_state_changes",
]

[[package]]
name = "vte_generate_state_changes"
version = "0.1.1"
source = "registry+https://github.com/rust-lang/crates.io-index"
checksum = "d257817081c7dffcdbab24b9e62d2def62e2ff7d00b1c20062551e6cccc145ff"
dependencies = [
 "proc-macro2 1.0.79",
 "quote 1.0.35",
]

[[package]]
name = "wait-timeout"
version = "0.2.0"
source = "registry+https://github.com/rust-lang/crates.io-index"
checksum = "9f200f5b12eb75f8c1ed65abd4b2db8a6e1b138a20de009dacee265a2498f3f6"
dependencies = [
 "libc",
]

[[package]]
name = "waker-fn"
version = "1.1.1"
source = "registry+https://github.com/rust-lang/crates.io-index"
checksum = "f3c4517f54858c779bbcbf228f4fca63d121bf85fbecb2dc578cdf4a39395690"

[[package]]
name = "walkdir"
version = "2.5.0"
source = "registry+https://github.com/rust-lang/crates.io-index"
checksum = "29790946404f91d9c5d06f9874efddea1dc06c5efe94541a7d6863108e3a5e4b"
dependencies = [
 "same-file",
 "winapi-util",
]

[[package]]
name = "want"
version = "0.3.1"
source = "registry+https://github.com/rust-lang/crates.io-index"
checksum = "bfa7760aed19e106de2c7c0b581b509f2f25d3dacaf737cb82ac61bc6d760b0e"
dependencies = [
 "try-lock",
]

[[package]]
name = "wasi"
version = "0.9.0+wasi-snapshot-preview1"
source = "registry+https://github.com/rust-lang/crates.io-index"
checksum = "cccddf32554fecc6acb585f82a32a72e28b48f8c4c1883ddfeeeaa96f7d8e519"

[[package]]
name = "wasi"
version = "0.10.2+wasi-snapshot-preview1"
source = "registry+https://github.com/rust-lang/crates.io-index"
checksum = "fd6fbd9a79829dd1ad0cc20627bf1ed606756a7f77edff7b66b7064f9cb327c6"

[[package]]
name = "wasi"
version = "0.11.0+wasi-snapshot-preview1"
source = "registry+https://github.com/rust-lang/crates.io-index"
checksum = "9c8d87e72b64a3b4db28d11ce29237c246188f4f51057d65a7eab63b7987e423"

[[package]]
name = "wasite"
version = "0.1.0"
source = "registry+https://github.com/rust-lang/crates.io-index"
checksum = "b8dad83b4f25e74f184f64c43b150b91efe7647395b42289f38e50566d82855b"

[[package]]
name = "wasm-bindgen"
version = "0.2.92"
source = "registry+https://github.com/rust-lang/crates.io-index"
checksum = "4be2531df63900aeb2bca0daaaddec08491ee64ceecbee5076636a3b026795a8"
dependencies = [
 "cfg-if 1.0.0",
 "wasm-bindgen-macro",
]

[[package]]
name = "wasm-bindgen-backend"
version = "0.2.92"
source = "registry+https://github.com/rust-lang/crates.io-index"
checksum = "614d787b966d3989fa7bb98a654e369c762374fd3213d212cfc0251257e747da"
dependencies = [
 "bumpalo",
 "log",
 "once_cell",
 "proc-macro2 1.0.79",
 "quote 1.0.35",
 "syn 2.0.57",
 "wasm-bindgen-shared",
]

[[package]]
name = "wasm-bindgen-futures"
version = "0.4.42"
source = "registry+https://github.com/rust-lang/crates.io-index"
checksum = "76bc14366121efc8dbb487ab05bcc9d346b3b5ec0eaa76e46594cabbe51762c0"
dependencies = [
 "cfg-if 1.0.0",
 "js-sys",
 "wasm-bindgen",
 "web-sys",
]

[[package]]
name = "wasm-bindgen-macro"
version = "0.2.92"
source = "registry+https://github.com/rust-lang/crates.io-index"
checksum = "a1f8823de937b71b9460c0c34e25f3da88250760bec0ebac694b49997550d726"
dependencies = [
 "quote 1.0.35",
 "wasm-bindgen-macro-support",
]

[[package]]
name = "wasm-bindgen-macro-support"
version = "0.2.92"
source = "registry+https://github.com/rust-lang/crates.io-index"
checksum = "e94f17b526d0a461a191c78ea52bbce64071ed5c04c9ffe424dcb38f74171bb7"
dependencies = [
 "proc-macro2 1.0.79",
 "quote 1.0.35",
 "syn 2.0.57",
 "wasm-bindgen-backend",
 "wasm-bindgen-shared",
]

[[package]]
name = "wasm-bindgen-shared"
version = "0.2.92"
source = "registry+https://github.com/rust-lang/crates.io-index"
checksum = "af190c94f2773fdb3729c55b007a722abb5384da03bc0986df4c289bf5567e96"

[[package]]
name = "web-sys"
version = "0.3.69"
source = "registry+https://github.com/rust-lang/crates.io-index"
checksum = "77afa9a11836342370f4817622a2f0f418b134426d91a82dfb48f532d2ec13ef"
dependencies = [
 "js-sys",
 "wasm-bindgen",
]

[[package]]
name = "webpki"
version = "0.22.4"
source = "registry+https://github.com/rust-lang/crates.io-index"
checksum = "ed63aea5ce73d0ff405984102c42de94fc55a6b75765d621c65262469b3c9b53"
dependencies = [
 "ring 0.17.8",
 "untrusted 0.9.0",
]

[[package]]
name = "webpki-roots"
version = "0.22.6"
source = "registry+https://github.com/rust-lang/crates.io-index"
checksum = "b6c71e40d7d2c34a5106301fb632274ca37242cd0c9d3e64dbece371a40a2d87"
dependencies = [
 "webpki",
]

[[package]]
name = "webpki-roots"
version = "0.25.4"
source = "registry+https://github.com/rust-lang/crates.io-index"
checksum = "5f20c57d8d7db6d3b86154206ae5d8fba62dd39573114de97c2cb0578251f8e1"

[[package]]
name = "which"
version = "4.4.2"
source = "registry+https://github.com/rust-lang/crates.io-index"
checksum = "87ba24419a2078cd2b0f2ede2691b6c66d8e47836da3b6db8265ebad47afbfc7"
dependencies = [
 "either",
 "home",
 "once_cell",
 "rustix 0.38.32",
]

[[package]]
name = "whoami"
version = "1.5.1"
source = "registry+https://github.com/rust-lang/crates.io-index"
checksum = "a44ab49fad634e88f55bf8f9bb3abd2f27d7204172a112c7c9987e01c1c94ea9"
dependencies = [
 "redox_syscall 0.4.1",
 "wasite",
 "web-sys",
]

[[package]]
name = "widestring"
version = "0.5.1"
source = "registry+https://github.com/rust-lang/crates.io-index"
checksum = "17882f045410753661207383517a6f62ec3dbeb6a4ed2acce01f0728238d1983"

[[package]]
name = "winapi"
version = "0.3.9"
source = "registry+https://github.com/rust-lang/crates.io-index"
checksum = "5c839a674fcd7a98952e593242ea400abe93992746761e38641405d28b00f419"
dependencies = [
 "winapi-i686-pc-windows-gnu",
 "winapi-x86_64-pc-windows-gnu",
]

[[package]]
name = "winapi-i686-pc-windows-gnu"
version = "0.4.0"
source = "registry+https://github.com/rust-lang/crates.io-index"
checksum = "ac3b87c63620426dd9b991e5ce0329eff545bccbbb34f3be09ff6fb6ab51b7b6"

[[package]]
name = "winapi-util"
version = "0.1.6"
source = "registry+https://github.com/rust-lang/crates.io-index"
checksum = "f29e6f9198ba0d26b4c9f07dbe6f9ed633e1f3d5b8b414090084349e46a52596"
dependencies = [
 "winapi",
]

[[package]]
name = "winapi-x86_64-pc-windows-gnu"
version = "0.4.0"
source = "registry+https://github.com/rust-lang/crates.io-index"
checksum = "712e227841d057c1ee1cd2fb22fa7e5a5461ae8e48fa2ca79ec42cfc1931183f"

[[package]]
name = "windows"
version = "0.51.1"
source = "registry+https://github.com/rust-lang/crates.io-index"
checksum = "ca229916c5ee38c2f2bc1e9d8f04df975b4bd93f9955dc69fabb5d91270045c9"
dependencies = [
 "windows-core 0.51.1",
 "windows-targets 0.48.5",
]

[[package]]
name = "windows-core"
version = "0.51.1"
source = "registry+https://github.com/rust-lang/crates.io-index"
checksum = "f1f8cf84f35d2db49a46868f947758c7a1138116f7fac3bc844f43ade1292e64"
dependencies = [
 "windows-targets 0.48.5",
]

[[package]]
name = "windows-core"
version = "0.52.0"
source = "registry+https://github.com/rust-lang/crates.io-index"
checksum = "33ab640c8d7e35bf8ba19b884ba838ceb4fba93a4e8c65a9059d08afcfc683d9"
dependencies = [
 "windows-targets 0.52.4",
]

[[package]]
name = "windows-sys"
version = "0.42.0"
source = "registry+https://github.com/rust-lang/crates.io-index"
checksum = "5a3e1820f08b8513f676f7ab6c1f99ff312fb97b553d30ff4dd86f9f15728aa7"
dependencies = [
 "windows_aarch64_gnullvm 0.42.2",
 "windows_aarch64_msvc 0.42.2",
 "windows_i686_gnu 0.42.2",
 "windows_i686_msvc 0.42.2",
 "windows_x86_64_gnu 0.42.2",
 "windows_x86_64_gnullvm 0.42.2",
 "windows_x86_64_msvc 0.42.2",
]

[[package]]
name = "windows-sys"
version = "0.45.0"
source = "registry+https://github.com/rust-lang/crates.io-index"
checksum = "75283be5efb2831d37ea142365f009c02ec203cd29a3ebecbc093d52315b66d0"
dependencies = [
 "windows-targets 0.42.2",
]

[[package]]
name = "windows-sys"
version = "0.48.0"
source = "registry+https://github.com/rust-lang/crates.io-index"
checksum = "677d2418bec65e3338edb076e806bc1ec15693c5d0104683f2efe857f61056a9"
dependencies = [
 "windows-targets 0.48.5",
]

[[package]]
name = "windows-sys"
version = "0.52.0"
source = "registry+https://github.com/rust-lang/crates.io-index"
checksum = "282be5f36a8ce781fad8c8ae18fa3f9beff57ec1b52cb3de0789201425d9a33d"
dependencies = [
 "windows-targets 0.52.4",
]

[[package]]
name = "windows-targets"
version = "0.42.2"
source = "registry+https://github.com/rust-lang/crates.io-index"
checksum = "8e5180c00cd44c9b1c88adb3693291f1cd93605ded80c250a75d472756b4d071"
dependencies = [
 "windows_aarch64_gnullvm 0.42.2",
 "windows_aarch64_msvc 0.42.2",
 "windows_i686_gnu 0.42.2",
 "windows_i686_msvc 0.42.2",
 "windows_x86_64_gnu 0.42.2",
 "windows_x86_64_gnullvm 0.42.2",
 "windows_x86_64_msvc 0.42.2",
]

[[package]]
name = "windows-targets"
version = "0.48.5"
source = "registry+https://github.com/rust-lang/crates.io-index"
checksum = "9a2fa6e2155d7247be68c096456083145c183cbbbc2764150dda45a87197940c"
dependencies = [
 "windows_aarch64_gnullvm 0.48.5",
 "windows_aarch64_msvc 0.48.5",
 "windows_i686_gnu 0.48.5",
 "windows_i686_msvc 0.48.5",
 "windows_x86_64_gnu 0.48.5",
 "windows_x86_64_gnullvm 0.48.5",
 "windows_x86_64_msvc 0.48.5",
]

[[package]]
name = "windows-targets"
version = "0.52.4"
source = "registry+https://github.com/rust-lang/crates.io-index"
checksum = "7dd37b7e5ab9018759f893a1952c9420d060016fc19a472b4bb20d1bdd694d1b"
dependencies = [
 "windows_aarch64_gnullvm 0.52.4",
 "windows_aarch64_msvc 0.52.4",
 "windows_i686_gnu 0.52.4",
 "windows_i686_msvc 0.52.4",
 "windows_x86_64_gnu 0.52.4",
 "windows_x86_64_gnullvm 0.52.4",
 "windows_x86_64_msvc 0.52.4",
]

[[package]]
name = "windows_aarch64_gnullvm"
version = "0.42.2"
source = "registry+https://github.com/rust-lang/crates.io-index"
checksum = "597a5118570b68bc08d8d59125332c54f1ba9d9adeedeef5b99b02ba2b0698f8"

[[package]]
name = "windows_aarch64_gnullvm"
version = "0.48.5"
source = "registry+https://github.com/rust-lang/crates.io-index"
checksum = "2b38e32f0abccf9987a4e3079dfb67dcd799fb61361e53e2882c3cbaf0d905d8"

[[package]]
name = "windows_aarch64_gnullvm"
version = "0.52.4"
source = "registry+https://github.com/rust-lang/crates.io-index"
checksum = "bcf46cf4c365c6f2d1cc93ce535f2c8b244591df96ceee75d8e83deb70a9cac9"

[[package]]
name = "windows_aarch64_msvc"
version = "0.42.2"
source = "registry+https://github.com/rust-lang/crates.io-index"
checksum = "e08e8864a60f06ef0d0ff4ba04124db8b0fb3be5776a5cd47641e942e58c4d43"

[[package]]
name = "windows_aarch64_msvc"
version = "0.48.5"
source = "registry+https://github.com/rust-lang/crates.io-index"
checksum = "dc35310971f3b2dbbf3f0690a219f40e2d9afcf64f9ab7cc1be722937c26b4bc"

[[package]]
name = "windows_aarch64_msvc"
version = "0.52.4"
source = "registry+https://github.com/rust-lang/crates.io-index"
checksum = "da9f259dd3bcf6990b55bffd094c4f7235817ba4ceebde8e6d11cd0c5633b675"

[[package]]
name = "windows_i686_gnu"
version = "0.42.2"
source = "registry+https://github.com/rust-lang/crates.io-index"
checksum = "c61d927d8da41da96a81f029489353e68739737d3beca43145c8afec9a31a84f"

[[package]]
name = "windows_i686_gnu"
version = "0.48.5"
source = "registry+https://github.com/rust-lang/crates.io-index"
checksum = "a75915e7def60c94dcef72200b9a8e58e5091744960da64ec734a6c6e9b3743e"

[[package]]
name = "windows_i686_gnu"
version = "0.52.4"
source = "registry+https://github.com/rust-lang/crates.io-index"
checksum = "b474d8268f99e0995f25b9f095bc7434632601028cf86590aea5c8a5cb7801d3"

[[package]]
name = "windows_i686_msvc"
version = "0.42.2"
source = "registry+https://github.com/rust-lang/crates.io-index"
checksum = "44d840b6ec649f480a41c8d80f9c65108b92d89345dd94027bfe06ac444d1060"

[[package]]
name = "windows_i686_msvc"
version = "0.48.5"
source = "registry+https://github.com/rust-lang/crates.io-index"
checksum = "8f55c233f70c4b27f66c523580f78f1004e8b5a8b659e05a4eb49d4166cca406"

[[package]]
name = "windows_i686_msvc"
version = "0.52.4"
source = "registry+https://github.com/rust-lang/crates.io-index"
checksum = "1515e9a29e5bed743cb4415a9ecf5dfca648ce85ee42e15873c3cd8610ff8e02"

[[package]]
name = "windows_x86_64_gnu"
version = "0.42.2"
source = "registry+https://github.com/rust-lang/crates.io-index"
checksum = "8de912b8b8feb55c064867cf047dda097f92d51efad5b491dfb98f6bbb70cb36"

[[package]]
name = "windows_x86_64_gnu"
version = "0.48.5"
source = "registry+https://github.com/rust-lang/crates.io-index"
checksum = "53d40abd2583d23e4718fddf1ebec84dbff8381c07cae67ff7768bbf19c6718e"

[[package]]
name = "windows_x86_64_gnu"
version = "0.52.4"
source = "registry+https://github.com/rust-lang/crates.io-index"
checksum = "5eee091590e89cc02ad514ffe3ead9eb6b660aedca2183455434b93546371a03"

[[package]]
name = "windows_x86_64_gnullvm"
version = "0.42.2"
source = "registry+https://github.com/rust-lang/crates.io-index"
checksum = "26d41b46a36d453748aedef1486d5c7a85db22e56aff34643984ea85514e94a3"

[[package]]
name = "windows_x86_64_gnullvm"
version = "0.48.5"
source = "registry+https://github.com/rust-lang/crates.io-index"
checksum = "0b7b52767868a23d5bab768e390dc5f5c55825b6d30b86c844ff2dc7414044cc"

[[package]]
name = "windows_x86_64_gnullvm"
version = "0.52.4"
source = "registry+https://github.com/rust-lang/crates.io-index"
checksum = "77ca79f2451b49fa9e2af39f0747fe999fcda4f5e241b2898624dca97a1f2177"

[[package]]
name = "windows_x86_64_msvc"
version = "0.42.2"
source = "registry+https://github.com/rust-lang/crates.io-index"
checksum = "9aec5da331524158c6d1a4ac0ab1541149c0b9505fde06423b02f5ef0106b9f0"

[[package]]
name = "windows_x86_64_msvc"
version = "0.48.5"
source = "registry+https://github.com/rust-lang/crates.io-index"
checksum = "ed94fce61571a4006852b7389a063ab983c02eb1bb37b47f8272ce92d06d9538"

[[package]]
name = "windows_x86_64_msvc"
version = "0.52.4"
source = "registry+https://github.com/rust-lang/crates.io-index"
checksum = "32b752e52a2da0ddfbdbcc6fceadfeede4c939ed16d13e648833a61dfb611ed8"

[[package]]
name = "winnow"
version = "0.4.11"
source = "registry+https://github.com/rust-lang/crates.io-index"
checksum = "656953b22bcbfb1ec8179d60734981d1904494ecc91f8a3f0ee5c7389bb8eb4b"
dependencies = [
 "memchr",
]

[[package]]
name = "winnow"
version = "0.5.40"
source = "registry+https://github.com/rust-lang/crates.io-index"
checksum = "f593a95398737aeed53e489c785df13f3618e41dbcd6718c6addbf1395aa6876"
dependencies = [
 "memchr",
]

[[package]]
name = "winnow"
version = "0.6.5"
source = "registry+https://github.com/rust-lang/crates.io-index"
checksum = "dffa400e67ed5a4dd237983829e66475f0a4a26938c4b04c21baede6262215b8"
dependencies = [
 "memchr",
]

[[package]]
name = "winreg"
version = "0.10.1"
source = "registry+https://github.com/rust-lang/crates.io-index"
checksum = "80d0f4e272c85def139476380b12f9ac60926689dd2e01d4923222f40580869d"
dependencies = [
 "winapi",
]

[[package]]
name = "winreg"
version = "0.50.0"
source = "registry+https://github.com/rust-lang/crates.io-index"
checksum = "524e57b2c537c0f9b1e69f1965311ec12182b4122e45035b1508cd24d2adadb1"
dependencies = [
 "cfg-if 1.0.0",
 "windows-sys 0.48.0",
]

[[package]]
name = "workspace-hack"
version = "0.1.0"
source = "git+https://github.com/fuzzland/ityfuzz-sui-fork.git#a88e9595de3c04a540c059d5f4b6d148419c508c"
dependencies = [
 "Inflector",
 "addr2line 0.19.0",
 "adler",
 "aead",
 "aes",
 "aes-gcm",
 "ahash 0.7.8",
 "ahash 0.8.11",
 "aho-corasick 0.7.20",
 "aho-corasick 1.1.3",
 "aliasable",
 "alloc-no-stdlib",
 "alloc-stdlib",
 "anemo",
 "anemo-build",
 "anemo-cli",
 "anemo-tower",
 "anes",
 "ansi_term",
 "anstream 0.3.2",
 "anstyle",
 "anstyle-parse",
 "anstyle-query",
 "anstyle-wincon 1.0.2",
 "anyhow",
 "arbitrary",
 "arc-swap",
 "ark-bls12-381",
 "ark-bn254",
 "ark-crypto-primitives",
 "ark-ec",
 "ark-ff 0.4.2",
 "ark-ff-asm 0.4.2",
 "ark-ff-macros 0.4.2",
 "ark-groth16",
 "ark-poly",
 "ark-r1cs-std",
 "ark-relations",
 "ark-secp256r1",
 "ark-serialize 0.4.2",
 "ark-serialize-derive",
 "ark-snark",
 "ark-std 0.4.0",
 "arrayref",
 "arrayvec 0.5.2",
 "arrayvec 0.7.4",
 "asn1-rs",
 "asn1-rs-derive",
 "asn1-rs-impl",
 "assert_cmd",
 "async-compression",
 "async-lock 2.8.0",
 "async-recursion",
 "async-stream",
 "async-stream-impl",
 "async-trait",
 "async_once",
 "atomicwrites",
 "atty",
 "auto_ops",
 "autocfg",
 "autotools",
 "aws-config",
 "aws-credential-types",
 "aws-endpoint",
 "aws-http",
 "aws-sdk-dynamodb",
 "aws-sdk-s3",
 "aws-sdk-sso",
 "aws-sdk-sts",
 "aws-sig-auth",
 "aws-sigv4",
 "aws-smithy-async",
 "aws-smithy-checksums",
 "aws-smithy-client",
 "aws-smithy-eventstream",
 "aws-smithy-http",
 "aws-smithy-http-tower",
 "aws-smithy-json",
 "aws-smithy-query",
 "aws-smithy-types",
 "aws-smithy-xml",
 "aws-types",
 "axum",
 "axum-core",
 "axum-extra",
 "axum-server",
 "backoff",
 "backtrace",
 "base-x",
 "base16ct 0.1.1",
 "base16ct 0.2.0",
 "base64 0.13.1",
 "base64 0.21.7",
 "base64-simd",
 "base64-url",
 "base64ct",
 "bcs",
 "beef",
 "better_any",
 "better_typeid_derive",
 "bimap",
 "bincode",
 "bindgen 0.65.1",
 "bip32",
 "bit-set",
 "bit-vec",
 "bitcoin-private",
 "bitcoin_hashes",
 "bitflags 1.3.2",
 "bitflags 2.5.0",
 "bitmaps",
 "bitvec 0.20.4",
 "blake2",
 "blake3",
 "block-buffer 0.10.4",
 "block-buffer 0.9.0",
 "block-padding 0.2.1",
 "block-padding 0.3.3",
 "blst",
 "brotli",
 "brotli-decompressor",
 "bs58 0.4.0",
 "bstr",
 "bulletproofs",
 "bumpalo",
 "byte-slice-cast",
 "bytecode-interpreter-crypto",
 "bytecount",
 "bytemuck",
 "byteorder",
 "bytes",
 "bytes-utils",
 "cached",
 "cached_proc_macro",
 "cached_proc_macro_types",
 "camino",
 "cargo-platform",
 "cargo_metadata 0.15.4",
 "cassowary",
 "cast",
 "cbc",
 "cc",
 "cexpr",
 "cfg-expr 0.13.0",
 "cfg-if 1.0.0",
 "chrono",
 "chrono-tz 0.6.3",
 "chrono-tz-build 0.0.3",
 "ciborium",
 "ciborium-io",
 "ciborium-ll",
 "cipher",
 "clang-sys",
 "clap 2.34.0",
 "clap 3.2.25",
 "clap 4.5.4",
 "clap_builder",
 "clap_derive 3.2.25",
 "clap_derive 4.5.4",
 "clap_lex 0.2.4",
 "clap_lex 0.5.1",
 "clear_on_drop",
 "clipboard-win",
 "codespan",
 "codespan-reporting",
 "collectable",
 "colorchoice",
 "colored",
 "colored-diff",
 "combine",
 "comfy-table 6.2.0",
 "console",
 "console-api 0.4.0",
 "console-subscriber",
 "const-oid",
 "const-str",
 "constant_time_eq 0.2.6",
 "convert_case 0.4.0",
 "convert_case 0.6.0",
 "core-foundation",
 "core-foundation-sys",
 "core2",
 "cpp_demangle",
 "cpufeatures",
 "crc32c",
 "crc32fast",
 "criterion",
 "criterion-plot",
 "crossbeam-channel",
 "crossbeam-deque",
 "crossbeam-epoch",
 "crossbeam-utils",
 "crossterm 0.21.0",
 "crossterm 0.22.1",
 "crossterm 0.25.0",
 "crossterm_winapi 0.8.0",
 "crossterm_winapi 0.9.1",
 "crunchy",
 "crypto-bigint 0.4.9",
 "crypto-bigint 0.5.5",
 "crypto-common",
 "crypto-mac",
 "csv",
 "csv-core",
 "ctor 0.1.26",
 "ctr",
 "curve25519-dalek-fiat",
 "curve25519-dalek-ng",
 "darling 0.14.4",
 "darling_core 0.14.4",
 "darling_macro 0.14.4",
 "dashmap",
 "data-encoding",
 "data-encoding-macro",
 "data-encoding-macro-internal",
 "datatest-stable",
 "debug-ignore",
 "debugid",
 "der 0.6.1",
 "der 0.7.8",
 "der-parser",
 "derivative",
 "derive-syn-parse",
 "derive_arbitrary",
 "derive_builder 0.12.0",
 "derive_builder_core 0.12.0",
 "derive_builder_macro 0.12.0",
 "derive_more",
 "determinator",
 "deunicode 0.4.5",
 "diesel",
 "diesel-derive-enum",
 "diesel_derives",
 "diesel_migrations",
 "diesel_table_macro_syntax",
 "diff",
 "difference",
 "difflib",
 "diffus",
 "diffy",
 "digest 0.10.7",
 "digest 0.9.0",
 "dirs 4.0.0",
 "dirs-next",
 "dirs-sys 0.3.7",
 "dirs-sys-next",
 "displaydoc",
 "dissimilar",
 "doc-comment",
 "downcast",
 "duration-str",
 "dyn-clone",
 "ecdsa 0.14.8",
 "ecdsa 0.16.9",
 "ed25519",
 "ed25519-consensus",
 "ed25519-dalek-fiat",
 "either",
 "elliptic-curve 0.12.3",
 "elliptic-curve 0.13.8",
 "encode_unicode 0.3.6",
 "encode_unicode 1.0.0",
 "encoding_rs",
 "endian-type",
 "enum-compat-util",
 "enum_dispatch",
 "errno 0.2.8",
 "errno 0.3.8",
 "error-code",
 "ethnum",
 "event-listener 2.5.3",
 "expect-test",
 "eyre",
 "fail",
 "fastcrypto",
 "fastcrypto-derive",
 "fastcrypto-zkp",
 "fastrand 1.9.0",
 "fd-lock",
 "fdlimit",
 "ff 0.12.1",
 "ff 0.13.0",
 "fiat-crypto",
 "findshlibs",
 "fixed-hash 0.7.0",
 "fixedbitset 0.2.0",
 "fixedbitset 0.4.2",
 "flate2",
 "float-cmp",
 "fnv",
 "form_urlencoded",
 "fragile",
 "fs_extra",
 "funty 1.1.0",
 "futures",
 "futures-channel",
 "futures-core",
 "futures-executor",
 "futures-io",
 "futures-lite 1.13.0",
 "futures-macro",
 "futures-sink",
 "futures-task",
 "futures-timer",
 "futures-util",
 "generic-array 0.14.7",
 "getrandom 0.1.16",
 "getrandom 0.2.12",
 "ghash",
 "gimli 0.27.3",
 "git-version",
 "git-version-macro",
 "glob",
 "globset",
 "globwalk",
 "gloo-net",
 "gloo-timers",
 "gloo-utils",
 "governor",
 "group 0.12.1",
 "group 0.13.0",
 "guppy",
 "guppy-summaries",
 "guppy-workspace-hack",
 "h2",
 "hakari",
 "half 1.8.3",
 "hashbrown 0.12.3",
 "hashbrown 0.13.2",
 "hdrhistogram",
 "headers",
 "headers-core",
 "heck 0.3.3",
 "heck 0.4.1",
 "hex",
 "hex-literal 0.3.4",
 "hkdf",
 "hmac 0.11.0",
 "hmac 0.12.1",
 "hmac-sha512",
 "home",
 "http",
 "http-body",
 "http-range-header",
 "httparse",
 "httpdate",
 "humansize 1.1.1",
 "humantime",
 "hyper",
 "hyper-rustls 0.23.2",
 "hyper-rustls 0.24.2",
 "hyper-timeout",
 "iana-time-zone",
 "ident_case",
 "idna 0.3.0",
 "if_chain",
 "ignore",
 "im",
 "impl-codec 0.5.1",
 "impl-serde 0.3.2",
 "impl-trait-for-tuples",
 "include_dir",
 "include_dir_macros",
 "indenter",
 "indexmap 1.9.3",
 "indicatif",
 "inferno",
 "inout",
 "inquire",
 "insta",
 "instant",
 "integer-encoding",
 "internment",
 "io-lifetimes",
 "ipnet",
 "iri-string",
 "is-terminal",
 "itertools 0.10.5",
 "itoa",
 "jemalloc-ctl",
 "jemalloc-sys",
 "jobserver",
 "js-sys",
 "json_to_table",
 "jsonpath_lib",
 "jsonrpsee",
 "jsonrpsee-client-transport",
 "jsonrpsee-core",
 "jsonrpsee-http-client",
 "jsonrpsee-proc-macros",
 "jsonrpsee-server",
 "jsonrpsee-types",
 "jsonrpsee-wasm-client",
 "jsonrpsee-ws-client",
 "k256 0.11.6",
 "keccak",
 "lazy_static",
 "lazycell",
 "leb128",
 "libc",
 "libloading 0.7.4",
 "libm",
 "librocksdb-sys",
 "libtest-mimic",
 "libz-sys",
 "linked-hash-map",
 "linux-raw-sys 0.1.4",
 "linux-raw-sys 0.3.8",
 "lock_api",
 "log",
 "lru 0.10.1",
 "lz4-sys",
 "mach",
 "match_opt",
 "matchers",
 "matchit 0.5.0",
 "matchit 0.7.3",
 "md-5 0.10.6",
 "md-5 0.9.1",
 "memchr",
 "memmap2 0.5.10",
 "memoffset 0.6.5",
 "memoffset 0.7.1",
 "merlin",
 "migrations_internals",
 "migrations_macros",
 "mime",
 "mime_guess",
 "minimal-lexical",
 "miniz_oxide 0.6.2",
 "mio 0.7.14",
 "mio 0.8.11",
 "mockall",
 "mockall_derive",
 "more-asserts",
 "move-abigen",
 "move-abstract-stack",
 "move-binary-format",
 "move-borrow-graph",
 "move-bytecode-source-map",
 "move-bytecode-utils",
 "move-bytecode-verifier",
 "move-bytecode-verifier-v0",
 "move-bytecode-viewer",
 "move-cli",
 "move-command-line-common",
 "move-compiler",
 "move-core-types",
 "move-coverage",
 "move-disassembler",
 "move-docgen",
 "move-errmapgen",
 "move-ir-compiler",
 "move-ir-to-bytecode",
 "move-ir-to-bytecode-syntax",
 "move-ir-types",
 "move-model",
 "move-package",
 "move-proc-macros",
 "move-prover",
 "move-prover-boogie-backend",
 "move-read-write-set-types",
 "move-resource-viewer",
 "move-stackless-bytecode",
 "move-stackless-bytecode-interpreter",
 "move-stdlib",
 "move-stdlib-v0",
 "move-symbol-pool",
 "move-transactional-test-runner",
 "move-unit-test",
 "move-vm-config",
 "move-vm-profiler",
 "move-vm-runtime",
 "move-vm-runtime-v0",
 "move-vm-test-utils",
 "move-vm-types",
 "multiaddr",
 "multibase",
 "multihash",
 "multihash-derive",
 "multimap",
 "named-lock",
 "nested",
 "newline-converter",
 "nexlint",
 "nexlint-lints",
 "nibble_vec",
 "nix 0.23.2",
 "nix 0.24.3",
 "no-std-compat",
 "nom 7.1.3",
 "nonzero_ext",
 "normalize-line-endings",
 "ntapi 0.4.1",
 "ntest",
 "ntest_test_cases",
 "ntest_timeout",
 "nu-ansi-term",
 "num 0.4.1",
 "num-bigint 0.4.4",
 "num-bigint-dig",
 "num-complex 0.4.5",
 "num-format",
 "num-integer",
 "num-iter",
 "num-rational 0.4.1",
 "num-traits",
 "num_cpus",
 "num_enum 0.6.1",
 "num_enum_derive 0.6.1",
 "number_prefix",
 "object 0.30.4",
 "oid-registry",
 "once_cell",
 "oorandom",
 "opaque-debug",
 "openssl-probe",
 "os_str_bytes",
 "ouroboros 0.15.6",
 "ouroboros 0.9.5",
 "ouroboros_macro 0.15.6",
 "ouroboros_macro 0.9.5",
 "output_vt100",
 "outref",
 "overload",
 "owo-colors",
 "p256",
 "papergrid",
 "parity-scale-codec 2.3.1",
 "parity-scale-codec-derive 2.3.1",
 "parking",
 "parking_lot 0.11.2",
 "parking_lot 0.12.1",
 "parking_lot_core 0.8.6",
 "parking_lot_core 0.9.9",
 "parse-zoneinfo",
 "paste",
 "pathdiff",
 "pbkdf2 0.11.0",
 "peeking_take_while",
 "pem",
 "pem-rfc7468 0.6.0",
 "pem-rfc7468 0.7.0",
 "percent-encoding",
 "pest",
 "pest_derive",
 "pest_generator",
 "pest_meta",
 "petgraph 0.5.1",
 "petgraph 0.6.4",
 "phf",
 "phf_codegen",
 "phf_generator",
 "phf_macros",
 "phf_shared 0.11.2",
 "pin-project",
 "pin-project-internal",
 "pin-project-lite",
 "pin-utils",
 "pkcs1",
 "pkcs8 0.10.2",
 "pkcs8 0.9.0",
 "pkg-config",
 "plotters",
 "plotters-backend",
 "plotters-svg",
 "polyval",
 "portable-atomic 0.3.20",
 "poseidon-ark",
 "pprof",
 "ppv-lite86",
 "pq-sys",
 "predicates 2.1.5",
 "predicates-core",
 "predicates-tree",
 "pretty",
 "pretty_assertions",
 "prettyplease 0.1.25",
 "prettyplease 0.2.17",
 "prettytable-rs",
 "primeorder",
 "primitive-types 0.10.1",
 "proc-macro-crate 1.1.3",
 "proc-macro-error",
 "proc-macro-error-attr",
 "proc-macro-hack",
 "proc-macro2 0.4.30",
 "proc-macro2 1.0.79",
 "prometheus",
 "prometheus-http-query",
 "proptest",
 "proptest-derive 0.3.0",
 "prost",
 "prost-build",
 "prost-derive",
 "prost-types",
 "protobuf 2.28.0",
 "protobuf-src",
 "quanta",
 "quick-error 1.2.3",
 "quick-error 2.0.1",
 "quick-xml",
 "quinn",
 "quinn-proto",
 "quinn-udp",
 "quote 0.6.13",
 "quote 1.0.35",
 "r2d2",
 "radium 0.6.2",
 "radix_trie",
 "rand 0.7.3",
 "rand 0.8.5",
 "rand_chacha 0.2.2",
 "rand_chacha 0.3.1",
 "rand_core 0.5.1",
 "rand_core 0.6.4",
 "rand_xorshift",
 "rand_xoshiro",
 "raw-cpuid",
 "rayon",
 "rayon-core",
 "rcgen",
 "read-write-set",
 "read-write-set-dynamic",
 "readonly",
 "ref-cast",
 "ref-cast-impl",
 "regex",
 "regex-automata 0.1.10",
 "regex-syntax 0.6.29",
 "regex-syntax 0.7.5",
 "reqwest",
 "retain_mut",
 "rfc6979 0.3.1",
 "rfc6979 0.4.0",
 "rgb",
 "ring 0.16.20",
 "ripemd",
 "roaring",
 "rocksdb",
 "ron",
 "rsa",
 "rstest",
 "rstest_macros",
 "rusoto_core",
 "rusoto_credential",
 "rusoto_kms",
 "rusoto_signature",
 "rust_decimal",
 "rustc-demangle",
 "rustc-hash",
 "rustc-hex",
 "rustc_version 0.4.0",
 "rusticata-macros",
 "rustix 0.36.17",
 "rustix 0.37.27",
 "rustls 0.20.9",
 "rustls 0.21.10",
 "rustls-native-certs",
 "rustls-pemfile",
 "rustls-webpki 0.100.3",
 "rustversion",
 "rusty-fork",
 "rustyline",
 "rustyline-derive",
 "ryu",
 "same-file",
 "schannel",
 "scheduled-thread-pool",
 "schemars",
 "schemars_derive",
 "scopeguard",
 "sct",
 "sec1 0.3.0",
 "sec1 0.7.3",
 "secp256k1 0.27.0",
 "secp256k1-sys 0.8.1",
 "security-framework",
 "security-framework-sys",
 "semver 0.11.0",
 "semver 1.0.22",
 "semver-parser 0.10.2",
 "send_wrapper 0.4.0",
 "serde",
 "serde-name",
 "serde-reflection",
 "serde_bytes",
 "serde_derive",
 "serde_derive_internals",
 "serde_json",
 "serde_path_to_error",
 "serde_repr",
 "serde_spanned",
 "serde_test",
 "serde_urlencoded",
 "serde_with",
 "serde_with_macros",
 "serde_yaml 0.8.26",
 "serde_yaml 0.9.34+deprecated",
 "sha-1 0.10.1",
 "sha-1 0.9.8",
 "sha1",
 "sha2 0.10.8",
 "sha2 0.9.9",
 "sha3 0.10.8",
 "sha3 0.9.1",
 "sharded-slab",
 "shell-words",
 "shellexpand",
 "shlex",
 "signal-hook",
 "signal-hook-mio",
 "signal-hook-registry",
 "signature 1.6.4",
 "signature 2.2.0",
 "similar",
 "simplelog",
 "siphasher",
 "sized-chunks",
 "slab",
 "slip10_ed25519",
 "slug",
 "smallvec",
 "snap",
 "socket2 0.4.10",
 "socket2 0.5.6",
 "soketto",
 "spin 0.5.2",
 "spki 0.6.0",
 "spki 0.7.3",
 "stable_deref_trait",
 "static_assertions",
 "str-buf",
 "str_stack",
 "strip-ansi-escapes",
 "strsim 0.10.0",
 "strsim 0.8.0",
 "structopt",
 "structopt-derive",
 "strum 0.24.1",
 "strum_macros 0.24.3",
 "subprocess",
 "subtle",
 "subtle-ng",
 "symbolic-common",
 "symbolic-demangle",
 "syn 0.15.44",
 "syn 1.0.109",
 "syn 2.0.57",
 "sync_wrapper",
 "synstructure",
 "sysinfo",
 "tabled",
 "tabled_derive",
 "tabular",
 "tap",
 "target-lexicon",
 "target-spec",
 "tempfile",
 "tera",
 "term",
 "termcolor",
 "termtree",
 "test-fuzz",
 "test-fuzz-internal",
 "test-fuzz-macro",
 "test-fuzz-runtime",
 "textwrap 0.11.0",
 "textwrap 0.16.1",
 "thiserror",
 "thiserror-impl",
 "thread_local",
 "threadpool",
 "time 0.1.43",
 "time 0.3.34",
 "time-core",
 "time-macros",
 "tiny-bip39",
 "tinytemplate",
 "tinyvec",
 "tinyvec_macros",
 "tokio",
 "tokio-io-timeout",
 "tokio-macros 2.4.0",
 "tokio-retry",
 "tokio-rustls 0.23.4",
 "tokio-rustls 0.24.1",
 "tokio-stream",
 "tokio-util 0.7.10",
 "toml 0.5.11",
 "toml 0.7.8",
 "toml_datetime 0.5.1",
 "toml_datetime 0.6.5",
 "toml_edit 0.14.4",
 "toml_edit 0.15.0",
 "toml_edit 0.19.15",
 "tonic 0.8.3",
 "tonic-build",
 "tonic-health",
 "toolchain_find 0.2.0",
 "tower",
 "tower-http",
 "tower-layer",
 "tower-service",
 "tracing",
 "tracing-appender",
 "tracing-attributes",
 "tracing-core",
 "tracing-futures",
 "tracing-serde",
 "tracing-subscriber 0.2.25",
 "tracing-subscriber 0.3.18",
 "treeline",
 "try-lock",
 "ttl_cache",
 "tui",
 "twox-hash",
 "typed-arena",
 "typenum",
 "ucd-trie",
 "uint",
 "unarray",
 "uncased",
 "unescape",
 "unic-char-property",
 "unic-char-range",
 "unic-common",
 "unic-segment",
 "unic-ucd-segment",
 "unic-ucd-version",
 "unicase",
 "unicode-bidi",
 "unicode-ident",
 "unicode-normalization",
 "unicode-segmentation",
 "unicode-width",
 "unicode-xid 0.1.0",
 "unicode-xid 0.2.4",
 "universal-hash",
 "unsafe-libyaml",
 "unsigned-varint",
 "untrusted 0.7.1",
 "unzip-n",
 "url",
 "urlencoding",
 "utf8parse",
 "uuid 1.8.0",
 "variant_count",
 "vcpkg",
 "vec_map",
 "version_check",
 "versions",
 "vsimd",
 "vte",
 "vte_generate_state_changes",
 "wait-timeout",
 "waker-fn",
 "walkdir",
 "want",
 "wasm-bindgen",
 "wasm-bindgen-backend",
 "wasm-bindgen-futures",
 "wasm-bindgen-macro",
 "wasm-bindgen-macro-support",
 "wasm-bindgen-shared",
 "web-sys",
 "webpki",
 "webpki-roots 0.22.6",
 "which",
 "whoami",
 "widestring",
 "winapi",
 "winapi-util",
 "windows-sys 0.42.0",
 "windows-sys 0.48.0",
 "windows-targets 0.48.5",
 "windows_x86_64_msvc 0.42.2",
 "windows_x86_64_msvc 0.48.5",
 "winnow 0.4.11",
 "winreg 0.10.1",
 "wyz 0.2.0",
 "x509-parser",
 "xml-rs",
 "xmlparser",
 "yaml-rust",
 "yansi 0.5.1",
 "yasna",
 "zeroize",
 "zeroize_derive",
 "zstd 0.12.4",
 "zstd-safe 6.0.6",
 "zstd-sys",
]

[[package]]
name = "ws_stream_wasm"
version = "0.7.4"
source = "registry+https://github.com/rust-lang/crates.io-index"
checksum = "7999f5f4217fe3818726b66257a4475f71e74ffd190776ad053fa159e50737f5"
dependencies = [
 "async_io_stream",
 "futures",
 "js-sys",
 "log",
 "pharos",
 "rustc_version 0.4.0",
 "send_wrapper 0.6.0",
 "thiserror",
 "wasm-bindgen",
 "wasm-bindgen-futures",
 "web-sys",
]

[[package]]
name = "wyz"
version = "0.2.0"
source = "registry+https://github.com/rust-lang/crates.io-index"
checksum = "85e60b0d1b5f99db2556934e21937020776a5d31520bf169e851ac44e6420214"

[[package]]
name = "wyz"
version = "0.5.1"
source = "registry+https://github.com/rust-lang/crates.io-index"
checksum = "05f360fc0b24296329c78fda852a1e9ae82de9cf7b27dae4b7f62f118f77b9ed"
dependencies = [
 "tap",
]

[[package]]
name = "x509-parser"
version = "0.14.0"
source = "registry+https://github.com/rust-lang/crates.io-index"
checksum = "e0ecbeb7b67ce215e40e3cc7f2ff902f94a223acf44995934763467e7b1febc8"
dependencies = [
 "asn1-rs",
 "base64 0.13.1",
 "data-encoding",
 "der-parser",
 "lazy_static",
 "nom 7.1.3",
 "oid-registry",
 "rusticata-macros",
 "thiserror",
 "time 0.3.34",
]

[[package]]
name = "xml-rs"
version = "0.8.19"
source = "registry+https://github.com/rust-lang/crates.io-index"
checksum = "0fcb9cbac069e033553e8bb871be2fbdffcab578eb25bd0f7c508cedc6dcd75a"

[[package]]
name = "xmlparser"
version = "0.13.6"
source = "registry+https://github.com/rust-lang/crates.io-index"
checksum = "66fee0b777b0f5ac1c69bb06d361268faafa61cd4682ae064a171c16c433e9e4"

[[package]]
name = "xxhash-rust"
version = "0.8.10"
source = "registry+https://github.com/rust-lang/crates.io-index"
checksum = "927da81e25be1e1a2901d59b81b37dd2efd1fc9c9345a55007f09bf5a2d3ee03"

[[package]]
name = "yaml-rust"
version = "0.4.5"
source = "registry+https://github.com/rust-lang/crates.io-index"
checksum = "56c1936c4cc7a1c9ab21a1ebb602eb942ba868cbd44a99cb7cdc5892335e1c85"
dependencies = [
 "linked-hash-map",
]

[[package]]
name = "yansi"
version = "0.5.1"
source = "registry+https://github.com/rust-lang/crates.io-index"
checksum = "09041cd90cf85f7f8b2df60c646f853b7f535ce68f85244eb6731cf89fa498ec"

[[package]]
name = "yansi"
version = "1.0.1"
source = "registry+https://github.com/rust-lang/crates.io-index"
checksum = "cfe53a6657fd280eaa890a3bc59152892ffa3e30101319d168b781ed6529b049"

[[package]]
name = "yasna"
version = "0.5.2"
source = "registry+https://github.com/rust-lang/crates.io-index"
checksum = "e17bb3549cc1321ae1296b9cdc2698e2b6cb1992adfa19a8c72e5b7a738f44cd"
dependencies = [
 "time 0.3.34",
]

[[package]]
name = "z3"
version = "0.12.1"
source = "registry+https://github.com/rust-lang/crates.io-index"
checksum = "4a7ff5718c079e7b813378d67a5bed32ccc2086f151d6185074a7e24f4a565e8"
dependencies = [
 "log",
 "z3-sys",
]

[[package]]
name = "z3-sys"
version = "0.8.1"
source = "registry+https://github.com/rust-lang/crates.io-index"
checksum = "d7cf70fdbc0de3f42b404f49b0d4686a82562254ea29ff0a155eef2f5430f4b0"
dependencies = [
 "bindgen 0.66.1",
 "cmake",
]

[[package]]
name = "zerocopy"
version = "0.7.32"
source = "registry+https://github.com/rust-lang/crates.io-index"
checksum = "74d4d3961e53fa4c9a25a8637fc2bfaf2595b3d3ae34875568a5cf64787716be"
dependencies = [
 "zerocopy-derive",
]

[[package]]
name = "zerocopy-derive"
version = "0.7.32"
source = "registry+https://github.com/rust-lang/crates.io-index"
checksum = "9ce1b18ccd8e73a9321186f97e46f9f04b778851177567b1975109d26a08d2a6"
dependencies = [
 "proc-macro2 1.0.79",
 "quote 1.0.35",
 "syn 2.0.57",
]

[[package]]
name = "zeroize"
version = "1.7.0"
source = "registry+https://github.com/rust-lang/crates.io-index"
checksum = "525b4ec142c6b68a2d10f01f7bbf6755599ca3f81ea53b8431b7dd348f5fdb2d"
dependencies = [
 "zeroize_derive",
]

[[package]]
name = "zeroize_derive"
version = "1.4.2"
source = "registry+https://github.com/rust-lang/crates.io-index"
checksum = "ce36e65b0d2999d2aafac989fb249189a141aee1f53c612c1f37d72631959f69"
dependencies = [
 "proc-macro2 1.0.79",
 "quote 1.0.35",
 "syn 2.0.57",
]

[[package]]
name = "zip"
version = "0.6.6"
source = "registry+https://github.com/rust-lang/crates.io-index"
checksum = "760394e246e4c28189f19d488c058bf16f564016aefac5d32bb1f3b51d5e9261"
dependencies = [
 "aes",
 "byteorder",
 "bzip2",
 "constant_time_eq 0.1.5",
 "crc32fast",
 "crossbeam-utils",
 "flate2",
 "hmac 0.12.1",
 "pbkdf2 0.11.0",
 "sha1",
 "time 0.3.34",
 "zstd 0.11.2+zstd.1.5.2",
]

[[package]]
name = "zstd"
version = "0.11.2+zstd.1.5.2"
source = "registry+https://github.com/rust-lang/crates.io-index"
checksum = "20cc960326ece64f010d2d2107537f26dc589a6573a316bd5b1dba685fa5fde4"
dependencies = [
 "zstd-safe 5.0.2+zstd.1.5.2",
]

[[package]]
name = "zstd"
version = "0.12.4"
source = "registry+https://github.com/rust-lang/crates.io-index"
checksum = "1a27595e173641171fc74a1232b7b1c7a7cb6e18222c11e9dfb9888fa424c53c"
dependencies = [
 "zstd-safe 6.0.6",
]

[[package]]
name = "zstd-safe"
version = "5.0.2+zstd.1.5.2"
source = "registry+https://github.com/rust-lang/crates.io-index"
checksum = "1d2a5585e04f9eea4b2a3d1eca508c4dee9592a89ef6f450c11719da0726f4db"
dependencies = [
 "libc",
 "zstd-sys",
]

[[package]]
name = "zstd-safe"
version = "6.0.6"
source = "registry+https://github.com/rust-lang/crates.io-index"
checksum = "ee98ffd0b48ee95e6c5168188e44a54550b1564d9d530ee21d5f0eaed1069581"
dependencies = [
 "libc",
 "zstd-sys",
]

[[package]]
name = "zstd-sys"
version = "2.0.10+zstd.1.5.6"
source = "registry+https://github.com/rust-lang/crates.io-index"
checksum = "c253a4914af5bafc8fa8c86ee400827e83cf6ec01195ec1f1ed8441bf00d65aa"
dependencies = [
 "cc",
 "pkg-config",
]
