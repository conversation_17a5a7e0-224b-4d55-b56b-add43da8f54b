# Etherscan v2 API Update and New Chain Support

## Summary

This update addresses the Etherscan API compatibility issues and adds support for new blockchain networks.

## Changes Made

### 1. Fixed Etherscan API URLs

**Problem**: The code was incorrectly using `https://api.etherscan.io/v2/api` for many chains, which is not the correct Etherscan v2 API format.

**Solution**: Updated all chain-specific Etherscan API endpoints to use the correct URLs:

- **ETH**: `https://api.etherscan.io/v2/api` (correct v2 API)
- **SEPOLIA**: `https://api-sepolia.etherscan.io/api` (chain-specific API)
- **BSC**: `https://api.bscscan.com/api` (BscScan API)
- **POLYGON**: `https://api.polygonscan.com/api` (PolygonScan API)
- **ARBITRUM**: `https://api.arbiscan.io/api` (Arbiscan API)
- **OPTIMISM**: `https://api-optimistic.etherscan.io/api` (Optimistic Etherscan API)
- **BASE**: `https://api.basescan.org/api` (BaseScan API)
- **BLAST**: `https://api.blastscan.io/api` (BlastScan API)
- **LINEA**: `https://api.lineascan.build/api` (LineaScan API)
- **SCROLL**: `https://api.scrollscan.com/api` (ScrollScan API)
- And many more...

### 2. Added New Chain Support

Added support for two new blockchain networks:

#### Berachain (BERA)
- **Chain ID**: 80094
- **RPC URL**: `https://rpc.berachain.com`
- **Explorer API**: `https://api.etherscan.io/v2/api` (Etherscan v2 unified endpoint)
- **Block Explorer**: `https://berascan.com/`
- **Native Token**: WBERA
- **Supported Tokens**: WBERA, USDC, USDT, WETH, WBTC

#### Bepolia (BEPOLIA) - Berachain Testnet
- **Chain ID**: 80069
- **RPC URL**: `https://bepolia.rpc.berachain.com`
- **Explorer API**: `https://api.etherscan.io/v2/api` (Etherscan v2 unified endpoint)
- **Native Token**: WBERA
- **Supported Tokens**: WBERA, USDC, USDT, WETH, WBTC

### 3. Enhanced API Handling

**Etherscan v2 API Support**: The code now properly handles both v1 and v2 API formats:
- v2 API requests include the `chainid` parameter as required
- v1 API requests use the traditional format without chainid
- Automatic detection based on URL pattern (`/v2/api`)
- New Berachain networks use the unified Etherscan v2 API endpoint: `https://api.etherscan.io/v2/api`

### 4. Updated CLI Help

Updated the command-line help text to include all supported chains:
```
-c, --chain-type <CHAIN_TYPE>
    Onchain - Chain type (eth,goerli,sepolia,bsc,chapel,polygon,mumbai,fantom,avalanche,optimism,
    arbitrum,gnosis,base,celo,zkevm,zkevm_testnet,blast,linea,iotex,scroll,vana,story,bera,bepolia,local)
```

## Files Modified

1. **`src/evm/onchain/endpoints.rs`**:
   - Added `BERA` and `BEPOLIA` to Chain enum
   - Updated `get_chain_etherscan_base()` with correct API URLs
   - Added chain ID mappings (80084 for BERA, 80085 for BEPOLIA)
   - Added RPC URL mappings
   - Added token configurations for new chains
   - Updated Blockscout API detection

2. **`src/evm/mod.rs`**:
   - Updated CLI help text to include new chains

3. **`Cargo.toml`**:
   - Removed `static-link-z3` feature to use system Z3 library

4. **`build.sh`**:
   - Updated with build notes and new chain information

## Testing

- ✅ Project builds successfully
- ✅ New chains (`bera`, `bepolia`) are recognized by CLI
- ✅ Help text displays all supported chains
- ✅ API URL generation works for all chains
- ✅ Chain ID mapping works correctly

## Usage Examples

### Using Berachain
```bash
./cli evm -c bera -t ****************************************** --onchain-etherscan-api-key YOUR_API_KEY
```

### Using Bepolia (Berachain Testnet)
```bash
./cli evm -c bepolia -t ****************************************** --onchain-etherscan-api-key YOUR_API_KEY
```

## Build Requirements

- CMake (installed via Homebrew)
- Z3 (installed via Homebrew)
- Rust toolchain

## Build Command

```bash
Z3_SYS_Z3_HEADER=/opt/homebrew/include/z3.h PKG_CONFIG_PATH=/opt/homebrew/lib/pkgconfig LIBRARY_PATH=/opt/homebrew/lib cargo build --release
```

Or use the provided build script:
```bash
./build.sh
```
