[{"type": "constructor", "inputs": [], "stateMutability": "nonpayable"}, {"type": "function", "name": "_totalCollateralLiquidity", "inputs": [{"name": "isBase", "type": "bool", "internalType": "bool"}], "outputs": [{"name": "", "type": "uint256", "internalType": "uint256"}], "stateMutability": "view"}, {"type": "function", "name": "addPricingLiquidity", "inputs": [{"name": "recipient", "type": "address", "internalType": "address"}], "outputs": [], "stateMutability": "nonpayable"}, {"type": "function", "name": "adminContract", "inputs": [], "outputs": [{"name": "", "type": "address", "internalType": "contract IExponentsAdmin"}], "stateMutability": "view"}, {"type": "function", "name": "baseAsset", "inputs": [], "outputs": [{"name": "", "type": "address", "internalType": "address"}], "stateMutability": "view"}, {"type": "function", "name": "buggy", "inputs": [], "outputs": [], "stateMutability": "nonpayable"}, {"type": "function", "name": "buyExpo", "inputs": [{"name": "powerId", "type": "uint256", "internalType": "uint256"}, {"name": "recipient", "type": "address", "internalType": "address"}], "outputs": [], "stateMutability": "nonpayable"}, {"type": "function", "name": "calcExitTaxBps", "inputs": [{"name": "sideIsLong", "type": "bool", "internalType": "bool"}, {"name": "ibcExpoOut", "type": "uint256", "internalType": "uint256"}], "outputs": [{"name": "calcResult", "type": "tuple", "internalType": "struct ExitTaxCalcResult", "components": [{"name": "exitTax", "type": "uint256", "internalType": "uint256"}, {"name": "collateralRatioTax", "type": "int256", "internalType": "int256"}, {"name": "longShortImbalanceTax", "type": "int256", "internalType": "int256"}, {"name": "pidPTax", "type": "int256", "internalType": "int256"}, {"name": "pidITax", "type": "int256", "internalType": "int256"}, {"name": "pidDTax", "type": "int256", "internalType": "int256"}]}], "stateMutability": "view"}, {"type": "function", "name": "claimProtocolFee", "inputs": [{"name": "recipient", "type": "address", "internalType": "address"}], "outputs": [], "stateMutability": "nonpayable"}, {"type": "function", "name": "currentPrice", "inputs": [], "outputs": [{"name": "", "type": "uint256", "internalType": "uint256"}], "stateMutability": "view"}, {"type": "function", "name": "feePercent", "inputs": [], "outputs": [{"name": "", "type": "uint256", "internalType": "uint256"}], "stateMutability": "view"}, {"type": "function", "name": "fundingRate", "inputs": [], "outputs": [{"name": "baseFundingRate", "type": "uint256", "internalType": "uint256"}, {"name": "quoteFundingRate", "type": "uint256", "internalType": "uint256"}, {"name": "updateTimestamp", "type": "uint256", "internalType": "uint256"}], "stateMutability": "view"}, {"type": "function", "name": "getExitTaxParams", "inputs": [], "outputs": [{"name": "taxParams", "type": "tuple[2]", "internalType": "struct ExitTaxParams[2]", "components": [{"name": "collateralRatioTax", "type": "tuple", "internalType": "struct TaxBracket", "components": [{"name": "rateBps", "type": "int256", "internalType": "int256"}, {"name": "minRateBps", "type": "int256", "internalType": "int256"}, {"name": "maxRateBps", "type": "int256", "internalType": "int256"}]}, {"name": "longShortRatioTax", "type": "tuple", "internalType": "struct TaxBracket", "components": [{"name": "rateBps", "type": "int256", "internalType": "int256"}, {"name": "minRateBps", "type": "int256", "internalType": "int256"}, {"name": "maxRateBps", "type": "int256", "internalType": "int256"}]}, {"name": "pidProportionalTax", "type": "tuple", "internalType": "struct TaxBracket", "components": [{"name": "rateBps", "type": "int256", "internalType": "int256"}, {"name": "minRateBps", "type": "int256", "internalType": "int256"}, {"name": "maxRateBps", "type": "int256", "internalType": "int256"}]}, {"name": "pidIntegralTax", "type": "tuple", "internalType": "struct TaxBracket", "components": [{"name": "rateBps", "type": "int256", "internalType": "int256"}, {"name": "minRateBps", "type": "int256", "internalType": "int256"}, {"name": "maxRateBps", "type": "int256", "internalType": "int256"}]}, {"name": "pidDerivativeTax", "type": "tuple", "internalType": "struct TaxBracket", "components": [{"name": "rateBps", "type": "int256", "internalType": "int256"}, {"name": "minRateBps", "type": "int256", "internalType": "int256"}, {"name": "maxRateBps", "type": "int256", "internalType": "int256"}]}, {"name": "lifetime", "type": "tuple", "internalType": "struct MeasurementWindow", "components": [{"name": "blockNumber", "type": "uint256", "internalType": "uint256"}, {"name": "cumulativeExitTaxes", "type": "uint256", "internalType": "uint256"}, {"name": "cumulativeFundingRateFees", "type": "uint256", "internalType": "uint256"}, {"name": "cumulativeRedemptionsLoss", "type": "uint256", "internalType": "uint256"}, {"name": "cumulativeRedemptionsGain", "type": "uint256", "internalType": "uint256"}]}, {"name": "current", "type": "tuple", "internalType": "struct MeasurementWindow", "components": [{"name": "blockNumber", "type": "uint256", "internalType": "uint256"}, {"name": "cumulativeExitTaxes", "type": "uint256", "internalType": "uint256"}, {"name": "cumulativeFundingRateFees", "type": "uint256", "internalType": "uint256"}, {"name": "cumulativeRedemptionsLoss", "type": "uint256", "internalType": "uint256"}, {"name": "cumulativeRedemptionsGain", "type": "uint256", "internalType": "uint256"}]}, {"name": "previous", "type": "tuple", "internalType": "struct MeasurementWindow", "components": [{"name": "blockNumber", "type": "uint256", "internalType": "uint256"}, {"name": "cumulativeExitTaxes", "type": "uint256", "internalType": "uint256"}, {"name": "cumulativeFundingRateFees", "type": "uint256", "internalType": "uint256"}, {"name": "cumulativeRedemptionsLoss", "type": "uint256", "internalType": "uint256"}, {"name": "cumulativeRedemptionsGain", "type": "uint256", "internalType": "uint256"}]}, {"name": "integral", "type": "tuple", "internalType": "struct MeasurementWindow", "components": [{"name": "blockNumber", "type": "uint256", "internalType": "uint256"}, {"name": "cumulativeExitTaxes", "type": "uint256", "internalType": "uint256"}, {"name": "cumulativeFundingRateFees", "type": "uint256", "internalType": "uint256"}, {"name": "cumulativeRedemptionsLoss", "type": "uint256", "internalType": "uint256"}, {"name": "cumulativeRedemptionsGain", "type": "uint256", "internalType": "uint256"}]}, {"name": "peakCLPProfit", "type": "tuple", "internalType": "struct MeasurementWindow", "components": [{"name": "blockNumber", "type": "uint256", "internalType": "uint256"}, {"name": "cumulativeExitTaxes", "type": "uint256", "internalType": "uint256"}, {"name": "cumulativeFundingRateFees", "type": "uint256", "internalType": "uint256"}, {"name": "cumulativeRedemptionsLoss", "type": "uint256", "internalType": "uint256"}, {"name": "cumulativeRedemptionsGain", "type": "uint256", "internalType": "uint256"}]}]}], "stateMutability": "view"}, {"type": "function", "name": "getReserves", "inputs": [], "outputs": [{"name": "baseReserve", "type": "uint256", "internalType": "uint256"}, {"name": "quote<PERSON><PERSON><PERSON>", "type": "uint256", "internalType": "uint256"}, {"name": "baseCollateral", "type": "uint256", "internalType": "uint256"}, {"name": "quoteCollateral", "type": "uint256", "internalType": "uint256"}], "stateMutability": "view"}, {"type": "function", "name": "initialize", "inputs": [{"name": "_adminContract", "type": "address", "internalType": "address"}, {"name": "symbol", "type": "string", "internalType": "string"}, {"name": "baseAssetToken", "type": "address", "internalType": "address"}, {"name": "quoteAssetToken", "type": "address", "internalType": "address"}, {"name": "expoToken", "type": "address", "internalType": "address"}, {"name": "baseAmount", "type": "uint256", "internalType": "uint256"}, {"name": "quoteAmount", "type": "uint256", "internalType": "uint256"}, {"name": "baseCollateralAmount", "type": "uint256", "internalType": "uint256"}, {"name": "quoteCollateralAmount", "type": "uint256", "internalType": "uint256"}, {"name": "_feePercent", "type": "uint256", "internalType": "uint256"}, {"name": "allowedPowerIds", "type": "uint256[]", "internalType": "uint256[]"}, {"name": "recipient", "type": "address", "internalType": "address"}], "outputs": [], "stateMutability": "nonpayable"}, {"type": "function", "name": "lockedCollateral", "inputs": [{"name": "clpToken", "type": "address", "internalType": "address"}, {"name": "timestamp", "type": "uint256", "internalType": "uint256"}], "outputs": [{"name": "collateral", "type": "uint256", "internalType": "uint256"}], "stateMutability": "view"}, {"type": "function", "name": "lockedCollateral", "inputs": [{"name": "baseCollateral", "type": "bool", "internalType": "bool"}, {"name": "timestamp", "type": "uint256", "internalType": "uint256"}], "outputs": [{"name": "collateral", "type": "uint256", "internalType": "uint256"}], "stateMutability": "view"}, {"type": "function", "name": "notifyCollateralChanged", "inputs": [{"name": "addCollateral", "type": "bool", "internalType": "bool"}], "outputs": [], "stateMutability": "nonpayable"}, {"type": "function", "name": "onERC1155BatchReceived", "inputs": [{"name": "", "type": "address", "internalType": "address"}, {"name": "", "type": "address", "internalType": "address"}, {"name": "", "type": "uint256[]", "internalType": "uint256[]"}, {"name": "", "type": "uint256[]", "internalType": "uint256[]"}, {"name": "", "type": "bytes", "internalType": "bytes"}], "outputs": [{"name": "", "type": "bytes4", "internalType": "bytes4"}], "stateMutability": "nonpayable"}, {"type": "function", "name": "onERC1155Received", "inputs": [{"name": "", "type": "address", "internalType": "address"}, {"name": "", "type": "address", "internalType": "address"}, {"name": "", "type": "uint256", "internalType": "uint256"}, {"name": "", "type": "uint256", "internalType": "uint256"}, {"name": "", "type": "bytes", "internalType": "bytes"}], "outputs": [{"name": "", "type": "bytes4", "internalType": "bytes4"}], "stateMutability": "nonpayable"}, {"type": "function", "name": "protocolFeeBase", "inputs": [], "outputs": [{"name": "", "type": "uint256", "internalType": "uint256"}], "stateMutability": "view"}, {"type": "function", "name": "protocolFeeQuote", "inputs": [], "outputs": [{"name": "", "type": "uint256", "internalType": "uint256"}], "stateMutability": "view"}, {"type": "function", "name": "quoteAsset", "inputs": [], "outputs": [{"name": "", "type": "address", "internalType": "address"}], "stateMutability": "view"}, {"type": "function", "name": "referencePriceContract", "inputs": [], "outputs": [{"name": "", "type": "address", "internalType": "address"}], "stateMutability": "view"}, {"type": "function", "name": "removePricingLiquidity", "inputs": [{"name": "recipient", "type": "address", "internalType": "address"}], "outputs": [], "stateMutability": "nonpayable"}, {"type": "function", "name": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "inputs": [], "outputs": [{"name": "tokens", "type": "tuple", "internalType": "struct PoolParameters", "components": [{"name": "baseAsset", "type": "address", "internalType": "address"}, {"name": "quoteAsset", "type": "address", "internalType": "address"}, {"name": "expoToken", "type": "address", "internalType": "address"}, {"name": "pricingLpToken", "type": "address", "internalType": "address"}, {"name": "baseCollateralVault", "type": "address", "internalType": "address"}, {"name": "quoteCollateralVault", "type": "address", "internalType": "address"}]}], "stateMutability": "view"}, {"type": "function", "name": "sellExpo", "inputs": [{"name": "powerId", "type": "uint256", "internalType": "uint256"}, {"name": "recipient", "type": "address", "internalType": "address"}], "outputs": [], "stateMutability": "nonpayable"}, {"type": "function", "name": "supportsInterface", "inputs": [{"name": "interfaceId", "type": "bytes4", "internalType": "bytes4"}], "outputs": [{"name": "", "type": "bool", "internalType": "bool"}], "stateMutability": "view"}, {"type": "function", "name": "swap", "inputs": [{"name": "baseAmountOut", "type": "uint256", "internalType": "uint256"}, {"name": "quoteAmountOut", "type": "uint256", "internalType": "uint256"}, {"name": "recipient", "type": "address", "internalType": "address"}, {"name": "data", "type": "bytes", "internalType": "bytes"}], "outputs": [], "stateMutability": "nonpayable"}, {"type": "event", "name": "ExpoBought", "inputs": [{"name": "from", "type": "address", "indexed": true, "internalType": "address"}, {"name": "recipient", "type": "address", "indexed": true, "internalType": "address"}, {"name": "powerId", "type": "uint256", "indexed": false, "internalType": "uint256"}, {"name": "amountIn", "type": "uint256", "indexed": false, "internalType": "uint256"}, {"name": "exposAmountOut", "type": "uint256", "indexed": false, "internalType": "uint256"}], "anonymous": false}, {"type": "event", "name": "ExpoSold", "inputs": [{"name": "from", "type": "address", "indexed": true, "internalType": "address"}, {"name": "recipient", "type": "address", "indexed": true, "internalType": "address"}, {"name": "powerId", "type": "uint256", "indexed": false, "internalType": "uint256"}, {"name": "exposAmountIn", "type": "uint256", "indexed": false, "internalType": "uint256"}, {"name": "amountOut", "type": "uint256", "indexed": false, "internalType": "uint256"}, {"name": "exitTaxFee", "type": "uint256", "indexed": false, "internalType": "uint256"}, {"name": "exitTaxBps", "type": "uint256", "indexed": false, "internalType": "uint256"}, {"name": "collateralRatioTaxBpsInflated", "type": "int256", "indexed": false, "internalType": "int256"}, {"name": "longShortImbalanceTaxBpsInflated", "type": "int256", "indexed": false, "internalType": "int256"}, {"name": "pidPTaxBpsInflated", "type": "int256", "indexed": false, "internalType": "int256"}, {"name": "pidITaxBpsInflated", "type": "int256", "indexed": false, "internalType": "int256"}, {"name": "pidDTaxBpsInflated", "type": "int256", "indexed": false, "internalType": "int256"}], "anonymous": false}, {"type": "event", "name": "FundingRateUpdated", "inputs": [{"name": "timestamp", "type": "uint256", "indexed": false, "internalType": "uint256"}, {"name": "baseFundingRate", "type": "uint256", "indexed": false, "internalType": "uint256"}, {"name": "quoteFundingRate", "type": "uint256", "indexed": false, "internalType": "uint256"}], "anonymous": false}, {"type": "event", "name": "Initialized", "inputs": [{"name": "version", "type": "uint64", "indexed": false, "internalType": "uint64"}], "anonymous": false}, {"type": "event", "name": "PoolInitialized", "inputs": [{"name": "from", "type": "address", "indexed": true, "internalType": "address"}, {"name": "baseAmount", "type": "uint256", "indexed": false, "internalType": "uint256"}, {"name": "quoteAmount", "type": "uint256", "indexed": false, "internalType": "uint256"}, {"name": "baseCollateralAmount", "type": "uint256", "indexed": false, "internalType": "uint256"}, {"name": "quoteCollateralAmount", "type": "uint256", "indexed": false, "internalType": "uint256"}, {"name": "feePercent", "type": "uint256", "indexed": false, "internalType": "uint256"}, {"name": "recipient", "type": "address", "indexed": false, "internalType": "address"}], "anonymous": false}, {"type": "event", "name": "PricingLiquidityAdded", "inputs": [{"name": "from", "type": "address", "indexed": true, "internalType": "address"}, {"name": "recipient", "type": "address", "indexed": true, "internalType": "address"}, {"name": "baseAmountIn", "type": "uint256", "indexed": false, "internalType": "uint256"}, {"name": "quoteAmountIn", "type": "uint256", "indexed": false, "internalType": "uint256"}, {"name": "lpAmountOut", "type": "uint256", "indexed": false, "internalType": "uint256"}], "anonymous": false}, {"type": "event", "name": "PricingLiquidityRemoved", "inputs": [{"name": "from", "type": "address", "indexed": true, "internalType": "address"}, {"name": "recipient", "type": "address", "indexed": true, "internalType": "address"}, {"name": "lpAmountIn", "type": "uint256", "indexed": false, "internalType": "uint256"}, {"name": "baseAmountOut", "type": "uint256", "indexed": false, "internalType": "uint256"}, {"name": "quoteAmountOut", "type": "uint256", "indexed": false, "internalType": "uint256"}], "anonymous": false}, {"type": "event", "name": "ProtocolFeeClaimed", "inputs": [{"name": "from", "type": "address", "indexed": true, "internalType": "address"}, {"name": "claimer", "type": "address", "indexed": true, "internalType": "address"}, {"name": "baseAmountClaimed", "type": "uint256", "indexed": false, "internalType": "uint256"}, {"name": "quoteAmountClaimed", "type": "uint256", "indexed": false, "internalType": "uint256"}], "anonymous": false}, {"type": "event", "name": "Rebase", "inputs": [{"name": "baseCollateralBefore", "type": "uint256", "indexed": false, "internalType": "uint256"}, {"name": "baseCollateralAfter", "type": "uint256", "indexed": false, "internalType": "uint256"}, {"name": "quoteCollateralBefore", "type": "uint256", "indexed": false, "internalType": "uint256"}, {"name": "quoteCollateralAfter", "type": "uint256", "indexed": false, "internalType": "uint256"}], "anonymous": false}, {"type": "event", "name": "Swapped", "inputs": [{"name": "from", "type": "address", "indexed": true, "internalType": "address"}, {"name": "recipient", "type": "address", "indexed": true, "internalType": "address"}, {"name": "baseAmountIn", "type": "uint256", "indexed": false, "internalType": "uint256"}, {"name": "quoteAmountIn", "type": "uint256", "indexed": false, "internalType": "uint256"}, {"name": "baseAmountOut", "type": "uint256", "indexed": false, "internalType": "uint256"}, {"name": "quoteAmountOut", "type": "uint256", "indexed": false, "internalType": "uint256"}], "anonymous": false}, {"type": "error", "name": "AddressEmptyCode", "inputs": [{"name": "target", "type": "address", "internalType": "address"}]}, {"type": "error", "name": "AddressInsufficientBalance", "inputs": [{"name": "account", "type": "address", "internalType": "address"}]}, {"type": "error", "name": "BalanceMismatch", "inputs": []}, {"type": "error", "name": "CollateralInsufficient", "inputs": [{"name": "baseAsset", "type": "bool", "internalType": "bool"}, {"name": "currentCollateral", "type": "uint256", "internalType": "uint256"}, {"name": "collateralNeeded", "type": "uint256", "internalType": "uint256"}]}, {"type": "error", "name": "EmptyAddress", "inputs": []}, {"type": "error", "name": "FailedInnerCall", "inputs": []}, {"type": "error", "name": "FeeConfigNotSupported", "inputs": [{"name": "fee", "type": "uint256", "internalType": "uint256"}]}, {"type": "error", "name": "InputAmountTooSmall", "inputs": []}, {"type": "error", "name": "InsufficientBalance", "inputs": []}, {"type": "error", "name": "InvalidExponent", "inputs": [{"name": "powerId", "type": "uint256", "internalType": "uint256"}]}, {"type": "error", "name": "InvalidInitialization", "inputs": []}, {"type": "error", "name": "InvalidInput", "inputs": []}, {"type": "error", "name": "InvalidSwapInput", "inputs": []}, {"type": "error", "name": "InvalidSwapOutput", "inputs": []}, {"type": "error", "name": "NotInitializing", "inputs": []}, {"type": "error", "name": "OutputAmountTooSmall", "inputs": []}, {"type": "error", "name": "PriceChangeOutOfLimit", "inputs": [{"name": "oldPrice", "type": "uint256", "internalType": "uint256"}, {"name": "newPrice", "type": "uint256", "internalType": "uint256"}]}, {"type": "error", "name": "ProtocolPaused", "inputs": []}, {"type": "error", "name": "ReentrancyGuardReentrantCall", "inputs": []}, {"type": "error", "name": "SafeERC20FailedOperation", "inputs": [{"name": "token", "type": "address", "internalType": "address"}]}, {"type": "error", "name": "Unauthorized", "inputs": []}]