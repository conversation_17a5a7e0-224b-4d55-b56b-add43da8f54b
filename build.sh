#!/bin/bash

# Build script for ityfuzz
# This script sets up the necessary environment variables and builds the project
#
# Updates made:
# - Fixed Etherscan API URLs to use proper chain-specific endpoints instead of generic v2 API
# - Added support for Berachain (BERA) and Bepolia (BEPOLIA) chains
# - Updated to use system Z3 library instead of static linking

set -e

echo "Building ityfuzz..."

# Set environment variables for Z3 system library
export Z3_SYS_Z3_HEADER=/opt/homebrew/include/z3.h
export PKG_CONFIG_PATH=/opt/homebrew/lib/pkgconfig
export LIBRARY_PATH=/opt/homebrew/lib

# Build the project
cargo build --release

# Copy the binary to the expected location
cp target/release/ityfuzz ./cli

echo "Build completed successfully!"
echo "Binary available at: ./cli"
echo ""
echo "New chains supported: bera, bepolia"
echo "Updated Etherscan API endpoints to use proper chain-specific URLs"
