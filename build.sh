#!/bin/bash

# Build script for ityfuzz
# This script sets up the necessary environment variables and builds the project

set -e

echo "Building ityfuzz..."

# Set environment variables for Z3 system library
export Z3_SYS_Z3_HEADER=/opt/homebrew/include/z3.h
export PKG_CONFIG_PATH=/opt/homebrew/lib/pkgconfig
export LIBRARY_PATH=/opt/homebrew/lib

# Build the project
cargo build --release

# Copy the binary to the expected location
cp target/release/ityfuzz ./cli

echo "Build completed successfully!"
echo "Binary available at: ./cli"
