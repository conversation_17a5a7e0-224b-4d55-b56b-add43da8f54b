Here we document vulnerabilities that can be found by ItyFuzz in 30 minute:

## BSC
You need set BSC Etherscan API keys to `BSC_ETHERSCAN_API_KEY` environmental variable before running. 

### SEAMAN
**Vulnerability: Fund Loss; Time Take: 0h-0m-3s**

Run
```
ityfuzz evm -t ******************************************,******************************************,******************************************,****************************************** -c bsc --onchain-block-number 23467515 -f -i -p --onchain-etherscan-api-key $BSC_ETHERSCAN_API_KEY
```


### RES02
**Vulnerability: Price Manipulation; Time Take: 0h-0m-2s**

Run
```
ityfuzz evm -t ******************************************,******************************************,******************************************,******************************************,******************************************,******************************************,******************************************,****************************************** -c bsc --onchain-block-number 21948016 -f -i -p --onchain-etherscan-api-key $BSC_ETHERSCAN_API_KEY
```


### LPC
**Vulnerability: Fund Loss; Time Take: 0h-0m-4s**

Run
```
ityfuzz evm -t ******************************************,******************************************,******************************************,****************************************** -c bsc --onchain-block-number 19852596 -f -i -p --onchain-etherscan-api-key $BSC_ETHERSCAN_API_KEY
```


### BIGFI
**Vulnerability: Price Manipulation; Time Take: 0h-8m-31s**

Run
```
ityfuzz evm -t ******************************************,******************************************,******************************************,****************************************** -c bsc --onchain-block-number 26685503 -f -i -p --onchain-etherscan-api-key $BSC_ETHERSCAN_API_KEY
```


### BEGO
**Vulnerability: Fund Loss; Time Take: 0h-0m-18s**

Run
```
ityfuzz evm -t ******************************************,******************************************,****************************************** -c bsc --onchain-block-number 22315679 -f -i -p --onchain-etherscan-api-key $BSC_ETHERSCAN_API_KEY
```


### Yyds
**Vulnerability: Fund Loss; Time Take: 0h-0m-4s**

Run
```
ityfuzz evm -t ******************************************,******************************************,******************************************,******************************************,******************************************,****************************************** -c bsc --onchain-block-number 21157025 -f -i -p --onchain-etherscan-api-key $BSC_ETHERSCAN_API_KEY
```


### EGD-Finance
**Vulnerability: Fund Loss; Time Take: 0h-0m-2s**

Run
```
ityfuzz evm -t ******************************************,******************************************,******************************************,******************************************,******************************************,******************************************,******************************************,****************************************** -c bsc --onchain-block-number 20245522 -f -i -p --onchain-etherscan-api-key $BSC_ETHERSCAN_API_KEY
```


### BBOX
**Vulnerability: Price Manipulation; Time Take: 0h-0m-4s**

Run
```
ityfuzz evm -t ******************************************,******************************************,****************************************** -c bsc --onchain-block-number 23106506 -f -i -p --onchain-etherscan-api-key $BSC_ETHERSCAN_API_KEY
```


### FAPEN
**Vulnerability: Fund Loss; Time Take: 0h-0m-2s**

Run
```
ityfuzz evm -t ******************************************,******************************************,****************************************** -c bsc --onchain-block-number 28637846 -f -i -p --onchain-etherscan-api-key $BSC_ETHERSCAN_API_KEY
```


### AUR
**Vulnerability: Fund Loss; Time Take: 0h-5m-36s**

Run
```
ityfuzz evm -t ******************************************,******************************************,****************************************** -c bsc --onchain-block-number 23282134 -f -i -p --onchain-etherscan-api-key $BSC_ETHERSCAN_API_KEY
```


### LocalTrader2
**Vulnerability: Fund Loss; Time Take: 0h-16m-53s**

Run
```
ityfuzz evm -t ******************************************,******************************************,******************************************,******************************************,****************************************** -c bsc --onchain-block-number 28460897 -f -i -p --onchain-etherscan-api-key $BSC_ETHERSCAN_API_KEY
```


### Annex
**Vulnerability: Fund Loss; Time Take: 0h-5m-59s**

Run
```
ityfuzz evm -t ******************************************,******************************************,****************************************** -c bsc --onchain-block-number 23165446 -f -i -p --onchain-etherscan-api-key $BSC_ETHERSCAN_API_KEY
```


### ARA
**Vulnerability: Arbitrary Call; Time Take: 0h-0m-5s**

Run
```
ityfuzz evm -t ******************************************,******************************************,******************************************,******************************************,******************************************,****************************************** -c bsc --onchain-block-number 29214010 -f -i -p --onchain-etherscan-api-key $BSC_ETHERSCAN_API_KEY
```


### PLTD
**Vulnerability: Price Manipulation; Time Take: 0h-10m-27s**

Run
```
ityfuzz evm -t ******************************************,******************************************,******************************************,****************************************** -c bsc --onchain-block-number 22252045 -f -i -p --onchain-etherscan-api-key $BSC_ETHERSCAN_API_KEY
```


### Sheep
**Vulnerability: Price Manipulation; Time Take: 0h-2m-5s**

Run
```
ityfuzz evm -t ******************************************,******************************************,******************************************,****************************************** -c bsc --onchain-block-number 25543755 -f -i -p --onchain-etherscan-api-key $BSC_ETHERSCAN_API_KEY
```


### SUT
**Vulnerability: Arbitrary Call; Time Take: 0h-0m-0s**

Run
```
ityfuzz evm -t ******************************************,******************************************,******************************************,******************************************,******************************************,****************************************** -c bsc --onchain-block-number 30165901 -f -i -p --onchain-etherscan-api-key $BSC_ETHERSCAN_API_KEY
```


### ApeDAO
**Vulnerability: Price Manipulation; Time Take: 0h-0m-2s**

Run
```
ityfuzz evm -t ******************************************,******************************************,******************************************,******************************************,******************************************,****************************************** -c bsc --onchain-block-number 30072293 -f -i -p --onchain-etherscan-api-key $BSC_ETHERSCAN_API_KEY
```


### Axioma
**Vulnerability: Fund Loss; Time Take: 0h-0m-4s**

Run
```
ityfuzz evm -t ******************************************,******************************************,****************************************** -c bsc --onchain-block-number 27620320 -f -i -p --onchain-etherscan-api-key $BSC_ETHERSCAN_API_KEY
```


### ValueDefi
**Vulnerability: Fund Loss; Time Take: 0h-13m-29s**

Run
```
ityfuzz evm -t ******************************************,******************************************,******************************************,******************************************,****************************************** -c bsc --onchain-block-number 7223029 -f -i -p --onchain-etherscan-api-key $BSC_ETHERSCAN_API_KEY
```


### Novo
**Vulnerability: Price Manipulation; Time Take: 0h-1m-21s**

Run
```
ityfuzz evm -t ******************************************,******************************************,******************************************,******************************************,******************************************,****************************************** -c bsc --onchain-block-number 18225002 -f -i -p --onchain-etherscan-api-key $BSC_ETHERSCAN_API_KEY
```


### RADT
**Vulnerability: Price Manipulation; Time Take: 0h-10m-27s**

Run
```
ityfuzz evm -t ******************************************,******************************************,******************************************,******************************************,****************************************** -c bsc --onchain-block-number 21572418 -f -i -p --onchain-etherscan-api-key $BSC_ETHERSCAN_API_KEY
```


### LaunchZone
**Vulnerability: Arbitrary Call; Time Take: 0h-0m-13s**

Run
```
ityfuzz evm -t ******************************************,******************************************,0xDb821BB482cfDae5D3B1A48EeaD8d2F74678D593,0x3a6d8cA21D1CF76F653A67577FA0D27453350dD8,0x0ccee62efec983f3ec4bad3247153009fb483551,0x3B78458981eB7260d1f781cb8be2CaAC7027DbE2 -c bsc --onchain-block-number 26024419 -f -i -p --onchain-etherscan-api-key $BSC_ETHERSCAN_API_KEY
```


### Thena
**Vulnerability: Price Manipulation; Time Take: 0h-0m-6s**

Run
```
ityfuzz evm -t ******************************************,0x2952beb1326acCbB5243725bd4Da2fC937BCa087,0x8AC76a51cc950d9822D68b83fE1Ad97B32Cd580d,0xF4C8E32EaDEC4BFe97E0F595AdD0f4450a863a11,0x39E29f4FB13AeC505EF32Ee6Ff7cc16e2225B11F,0x20a304a7d126758dfe6B243D0fc515F83bCA8431,0x618f9Eb0E1a698409621f4F487B563529f003643,0xA99c4051069B774102d6D215c6A9ba69BD616E6a -c bsc --onchain-block-number 26834149 -f -i -p --onchain-etherscan-api-key $BSC_ETHERSCAN_API_KEY
```


### PancakeHunny
**Vulnerability: Price Manipulation; Time Take: 0h-9m-59s**

Run
```
ityfuzz evm -t 0x12180BB36DdBce325b3be0c087d61Fce39b8f5A4,0x0E09FaBB73Bd3Ade0a17ECC321fD13a19e81cE82,******************************************,0xb9b0090aaa81f374d66d94a8138d80caa2002950,0x109Ea28dbDea5E6ec126FbC8c33845DFe812a300,0x515Fb5a7032CdD688B292086cf23280bEb9E31B6,0x565b72163f17849832A692A3c5928cc502f46D69 -c bsc --onchain-block-number 7962338 -f -i -p --onchain-etherscan-api-key $BSC_ETHERSCAN_API_KEY
```


### BGLD
**Vulnerability: Price Manipulation; Time Take: 0h-2m-52s**

Run
```
ityfuzz evm -t ******************************************,0xE445654F3797c5Ee36406dBe88FBAA0DfbdDB2Bb,0x429339fa7A2f2979657B25ed49D64d4b98a2050d,******************************************,******************************************,******************************************,******************************************,******************************************,******************************************,****************************************** -c bsc --onchain-block-number 23844529 -f -i -p --onchain-etherscan-api-key $BSC_ETHERSCAN_API_KEY
```


### HPAY
**Vulnerability: Fund Loss; Time Take: 0h-11m-38s**

Run
```
ityfuzz evm -t ******************************************,******************************************,****************************************** -c bsc --onchain-block-number 22280853 -f -i -p --onchain-etherscan-api-key $BSC_ETHERSCAN_API_KEY
```


### THB
**Vulnerability: Fund Loss; Time Take: 0h-1m-7s**

Run
```
ityfuzz evm -t ******************************************,****************************************** -c bsc --onchain-block-number 21785004 -f -i -p --onchain-etherscan-api-key $BSC_ETHERSCAN_API_KEY
```


### CS
**Vulnerability: Price Manipulation; Time Take: 0h-0m-26s**

Run
```
ityfuzz evm -t ******************************************,******************************************,******************************************,****************************************** -c bsc --onchain-block-number 28466976 -f -i -p --onchain-etherscan-api-key $BSC_ETHERSCAN_API_KEY
```


### Melo
**Vulnerability: Fund Loss; Time Take: 0h-0m-12s**

Run
```
ityfuzz evm -t ******************************************,******************************************,****************************************** -c bsc --onchain-block-number 27960445 -f -i -p --onchain-etherscan-api-key $BSC_ETHERSCAN_API_KEY
```


### GSS
**Vulnerability: Price Manipulation; Time Take: 0h-8m-23s**

Run
```
ityfuzz evm -t ******************************************,******************************************,******************************************,******************************************,****************************************** -c bsc --onchain-block-number 31108558 -f -i -p --onchain-etherscan-api-key $BSC_ETHERSCAN_API_KEY
```


### CFC
**Vulnerability: Fund Loss; Time Take: 0h-0m-24s**

Run
```
ityfuzz evm -t ******************************************,******************************************,******************************************,******************************************,******************************************,******************************************,****************************************** -c bsc --onchain-block-number 29116478 -f -i -p --onchain-etherscan-api-key $BSC_ETHERSCAN_API_KEY
```


### cftoken
**Vulnerability: Price Manipulation; Time Take: 0h-0m-54s**

Run
```
ityfuzz evm -t ******************************************,****************************************** -c bsc --onchain-block-number 16841980 -f -i -p --onchain-etherscan-api-key $BSC_ETHERSCAN_API_KEY
```


### AES
**Vulnerability: Price Manipulation; Time Take: 0h-0m-1s**

Run
```
ityfuzz evm -t ******************************************,******************************************,****************************************** -c bsc --onchain-block-number 23695904 -f -i -p --onchain-etherscan-api-key $BSC_ETHERSCAN_API_KEY
```


### Utopia
**Vulnerability: Price Manipulation; Time Take: 0h-11m-26s**

Run
```
ityfuzz evm -t ******************************************,******************************************,******************************************,******************************************,****************************************** -c bsc --onchain-block-number 30119396 -f -i -p --onchain-etherscan-api-key $BSC_ETHERSCAN_API_KEY
```


### MintoFinance
**Vulnerability: Fund Loss; Time Take: 0h-0m-1s**

Run
```
ityfuzz evm -t ******************************************,******************************************,******************************************,******************************************,******************************************,******************************************,****************************************** -c bsc --onchain-block-number 30214253 -f -i -p --onchain-etherscan-api-key $BSC_ETHERSCAN_API_KEY
```


### BabyDogeCoin02
**Vulnerability: Fund Loss; Time Take: 0h-20m-22s**

Run
```
ityfuzz evm -t ******************************************,******************************************,******************************************,******************************************,******************************************,******************************************,******************************************,******************************************,******************************************,******************************************,******************************************,******************************************,******************************************,******************************************,****************************************** -c bsc --onchain-block-number 29295010 -f -i -p --onchain-etherscan-api-key $BSC_ETHERSCAN_API_KEY
```


### MBC_ZZSH
**Vulnerability: Fund Loss; Time Take: 0h-0m-34s**

Run
```
ityfuzz evm -t ******************************************,******************************************,******************************************,******************************************,******************************************,****************************************** -c bsc --onchain-block-number 23474460 -f -i -p --onchain-etherscan-api-key $BSC_ETHERSCAN_API_KEY
```


### WGPT
**Vulnerability: Fund Loss; Time Take: 0h-0m-40s**

Run
```
ityfuzz evm -t ******************************************,******************************************,******************************************,******************************************,******************************************,******************************************,******************************************,******************************************,****************************************** -c bsc --onchain-block-number 29891709 -f -i -p --onchain-etherscan-api-key $BSC_ETHERSCAN_API_KEY
```


### ROI
**Vulnerability: Fund Loss; Time Take: 0h-0m-1s**

Run
```
ityfuzz evm -t ******************************************,******************************************,******************************************,******************************************,******************************************,******************************************,****************************************** -c bsc --onchain-block-number 21143795 -f -i -p --onchain-etherscan-api-key $BSC_ETHERSCAN_API_KEY
```


### HEALTH
**Vulnerability: Price Manipulation; Time Take: 0h-0m-3s**

Run
```
ityfuzz evm -t ******************************************,******************************************,******************************************,****************************************** -c bsc --onchain-block-number 22337425 -f -i -p --onchain-etherscan-api-key $BSC_ETHERSCAN_API_KEY
```


### Shadowfi
**Vulnerability: Price Manipulation; Time Take: 0h-29m-17s**

Run
```
ityfuzz evm -t ******************************************,******************************************,****************************************** -c bsc --onchain-block-number 20969095 -f -i -p --onchain-etherscan-api-key $BSC_ETHERSCAN_API_KEY
```


### MetaPoint
**Vulnerability: Fund Loss; Time Take: 0h-20m-18s**

Run
```
ityfuzz evm -t ******************************************,******************************************,******************************************,******************************************,******************************************,******************************************,******************************************,******************************************,******************************************,0x68531F3d3A20027ed3A428e90Ddf8e32a9F35DC8,0x9117df9aA33B23c0A9C2C913aD0739273c3930b3,0x52AeD741B5007B4fb66860b5B31dD4c542D65785,0xE5cBd18Db5C1930c0A07696eC908f20626a55E3C,0x3B5E381130673F794a5CF67FBbA48688386BEa86,0xC254741776A13f0C3eFF755a740A4B2aAe14a136 -c bsc --onchain-block-number 27264383 -f -i -p --onchain-etherscan-api-key $BSC_ETHERSCAN_API_KEY
```


### Carrot
**Vulnerability: Arbitrary Call; Time Take: 0h-0m-1s**

Run
```
ityfuzz evm -t ******************************************,0x6863b549bf730863157318df4496eD111aDFA64f,0xcFF086EaD392CcB39C49eCda8C974ad5238452aC,0x5575406ef6b15eec1986c412b9fbe144522c45ae,0x6863b549bf730863157318df4496ed111adfa64f -c bsc --onchain-block-number 22055611 -f -i -p --onchain-etherscan-api-key $BSC_ETHERSCAN_API_KEY
```


### RES
**Vulnerability: Price Manipulation; Time Take: 0h-0m-3s**

Run
```
ityfuzz evm -t ******************************************,******************************************,******************************************,0xff333de02129af88aae101ab777d3f5d709fec6f,******************************************,******************************************,******************************************,****************************************** -c bsc --onchain-block-number 21948016 -f -i -p --onchain-etherscan-api-key $BSC_ETHERSCAN_API_KEY
```


### RFB
**Vulnerability: Fund Loss; Time Take: 0h-0m-16s**

Run
```
ityfuzz evm -t 0x26f1457f067bF26881F311833391b52cA871a4b5,0x03184AAA6Ad4F7BE876423D9967d1467220a544e,******************************************,****************************************** -c bsc --onchain-block-number 23649423 -f -i -p --onchain-etherscan-api-key $BSC_ETHERSCAN_API_KEY
```

