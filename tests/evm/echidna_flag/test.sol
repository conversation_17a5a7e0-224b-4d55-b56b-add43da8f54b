contract Test {
    event Flag(bool);

    bool private flag0 = true;
    bool private flag1 = true;

    function set0(int256 val) public returns (bool) {
        if (val % 100 == 0) flag0 = false;
    }

    function set1(int256 val) public returns (bool) {
        if (val % 10 == 0 && !flag0) flag1 = false;
    }

    function echidna_alwaystrue() public returns (bool) {
        return (true);
    }

    function echidna_revert_always() public returns (bool) {
        revert();
    }

    function echidna_sometimesfalse() public returns (bool) {
        emit Flag(flag0);
        emit Flag(flag1);
        return (flag1);
    }
}
