[profile.default]
src = "src"
out = "out"
libs = ["lib"]

rpc_endpoints = { mainnet = "https://rpc.ankr.com/eth", eth = "https://rpc.ankr.com/eth", optimism = "https://rpc.ankr.com/optimism", fantom = "https://rpc.ankr.com/fantom", arbitrum = "https://rpc.ankr.com/arbitrum", bsc = "https://rpc.ankr.com/bsc", moonriver = "https://moonriver.public.blastapi.io", gnosis = "https://rpc.ankr.com/gnosis", avax = "https://rpc.ankr.com/avalanche", polygon = "https://rpc.ankr.com/polygon", goerli = "https://rpc.ankr.com/eth_goerli", era = "https://mainnet.era.zksync.io", zksync = "https://mainnet.era.zksync.io" }

# See more config options https://github.com/foundry-rs/foundry/blob/master/crates/config/README.md#all-options
