// SPDX-License-Identifier: UNLICENSED
pragma solidity ^0.8.13;

import {Test} from "forge-std/Test.sol";

interface IStaxLP {
    function balanceOf(address) external returns (uint256);
    function transfer(address, uint256) external returns (bool);
    function approve(address, uint256) external returns (bool);
}

contract StaxExploitTest is Test {
    uint256 private initialAmount;
    IStaxLP private StaxLP = IStaxLP(0xBcB8b7FC9197fEDa75C101fA69d3211b5a30dCD9);
    address private StaxLPStaking = 0xd2869042E12a3506100af1D192b5b04D65137941;
    address private tokenHolder = address(0xeCb456EA5365865EbAb8a2661B0c503410e9B347);

    function setUp() public {
        // vm.createSelectFork("http://bsc.internal.fuzz.land", "latest");
        vm.createSelectFork("mainnet", 15725066);

        targetContract(address(StaxLP));
        targetContract(address(StaxLPStaking));

        initialAmount = StaxLP.balanceOf(tokenHolder);
        vm.prank(tokenHolder);
        StaxLP.transfer(address(this), initialAmount);
        StaxLP.approve(address(StaxLPStaking), type(uint256).max);
    }

    function invariant_1() public {
        assertEq(StaxLP.balanceOf(address(this)), initialAmount + 1);
    }
}
