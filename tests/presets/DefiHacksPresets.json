[{"calls": ["0xa6417ed6", "0xb6b55f25", "0xa6417ed6", "0x2e1a7d4d", "0xa6417ed6", "0xb6b55f25", "0xa6417ed6", "0x2e1a7d4d", "0xa6417ed6", "0xb6b55f25", "0xa6417ed6", "0x2e1a7d4d", "0xa6417ed6", "0xb6b55f25", "0xa6417ed6", "0x2e1a7d4d", "0xa6417ed6", "0xb6b55f25", "0xa6417ed6", "0x2e1a7d4d", "0xa6417ed6", "0xb6b55f25", "0xa6417ed6", "0x2e1a7d4d"], "exploit_name": "HarvestFinance_exp", "function_sigs": ["0x095ea7b3", "0x022c0d9f", "0xb6b55f25", "0x2e1a7d4d", "0xa9059cbb", "0xa6417ed6"]}, {"calls": ["0x2e1a7d4d", "0x5f72f450", "0x5f72f450", "0x5f72f450", "0x5f72f450", "0x5f72f450", "0x5f72f450", "0x5f72f450", "0x5f72f450", "0x5f72f450", "0x5f72f450", "0x5f72f450", "0x5f72f450", "0x5f72f450", "0x5f72f450", "0x5f72f450", "0x5f72f450", "0x5f72f450", "0x5f72f450", "0x5f72f450", "0x5f72f450", "0x5f72f450", "0x5f72f450", "0x5f72f450", "0x5f72f450", "0x5f72f450", "0x5f72f450", "0x5f72f450", "0x5f72f450", "0x5f72f450", "0x5f72f450", "0x5f72f450", "0x5f72f450", "0x5f72f450", "0x5f72f450", "0x5f72f450", "0x5f72f450", "0x5f72f450", "0x5f72f450", "0x5f72f450", "0x5f72f450", "0x5f72f450", "0x5f72f450", "0x5f72f450", "0x5f72f450", "0x5f72f450", "0x5f72f450", "0x5f72f450", "0x5f72f450", "0x5f72f450", "0x5f72f450", "0xd0e30db0"], "exploit_name": "RFB_exp", "function_sigs": ["0x095ea7b3", "0x5f72f450", "0x791ac947", "0xb6f9de95", "0xd0e30db0", "0x2e1a7d4d", "0xa9059cbb"]}, {"calls": ["0xc88a5e6d", "0xc657c718", "0xc657c718", "0xc657c718", "0xc657c718", "0xc657c718", "0xf2fde38b", "0x061c82d0", "0xfb0ecfa4", "0x6ac9a870", "0x8ee88c53", "0x52390c02", "0x52390c02", "0x52390c02", "0x52390c02", "0x52390c02", "0x52390c02", "0x52390c02", "0x52390c02", "0x52390c02", "0x52390c02", "0x52390c02", "0x52390c02", "0x52390c02", "0x52390c02", "0x061c82d0", "0x061c82d0", "0x3685d419", "0xfff6cae9"], "exploit_name": "ROI_exp", "function_sigs": ["0xfb0ecfa4", "0x095ea7b3", "0x061c82d0", "0xc657c718", "0x52390c02", "0x3685d419", "0x8ee88c53", "0x791ac947", "0xc88a5e6d", "0xf2fde38b", "0x6ac9a870", "0x022c0d9f", "0xfff6cae9", "0xfb3bdb41", "0xa9059cbb"]}, {"calls": ["0xc657c718", "0xc657c718", "0xc657c718", "0xc657c718", "0xc657c718", "0xc657c718", "0xa515df66", "0xa515df66", "0xa515df66", "0xa515df66", "0xa515df66", "0xa515df66", "0xa515df66", "0xa515df66", "0xa515df66", "0xa515df66", "0xa515df66", "0xa515df66", "0xa515df66", "0xa515df66", "0xa515df66", "0xa515df66", "0xa515df66", "0xa515df66", "0xa515df66", "0xa515df66", "0xa515df66", "0xa515df66", "0xa515df66", "0xa515df66", "0xa515df66", "0xa515df66", "0xa515df66", "0xa515df66", "0xa515df66", "0xa515df66", "0xa515df66", "0xa515df66", "0xa515df66", "0xa515df66", "0xa515df66", "0xa515df66", "0xa515df66", "0xa515df66", "0xa515df66", "0xa515df66", "0xa515df66", "0xa515df66", "0xa515df66", "0xa515df66", "0xa515df66", "0xa515df66", "0xa515df66", "0xa515df66", "0xa515df66", "0xa515df66"], "exploit_name": "NewFreeDAO_exp", "function_sigs": ["0x095ea7b3", "0xa515df66", "0xc657c718", "0xa9059cbb", "0x5c11d795"]}, {"calls": ["0xc657c718", "0xc657c718", "0xc657c718", "0xc657c718", "0xc657c718", "0xc657c718", "0xc657c718", "0xc657c718", "0xc657c718", "0xc657c718", "0xc657c718", "0x92b888ae", "0xab9c4b5d", "0x2e1a7d4d", "0x0272f3a3", "0x8340f549", "0x59eba454", "0x28b7b036", "0xb95cac28", "0x8bdb3913", "0x5224372c", "0x5224372c", "0x5224372c", "0x5224372c", "0x59eba454", "0x28b7b036", "0x59eba454", "0x59eba454", "0x59eba454", "0x28b7b036", "0x28b7b036", "0x28b7b036", "0x28b7b036", "0x28b7b036", "0x28b7b036"], "exploit_name": "Sentiment_exp", "function_sigs": ["0x92b888ae", "0xab9c4b5d", "0x0272f3a3", "0xc657c718", "0x095ea7b3", "0x8340f549", "0x28b7b036", "0x59eba454", "0xb95cac28", "0x5224372c", "0x8bdb3913", "0x2e1a7d4d"]}, {"calls": ["0xc657c718", "0xc657c718", "0xba087652"], "exploit_name": "ReaperFarm", "function_sigs": ["0xba087652", "0xc657c718"]}, {"calls": ["0xe46dcfeb", "0xcbf0b0c0"], "exploit_name": "Parity_kill", "function_sigs": ["0xcbf0b0c0", "0xe46dcfeb"]}, {"calls": ["0xe5d6bf02", "0x1e83409a", "0x1e83409a", "0x1e83409a", "0x1e83409a", "0x1e83409a", "0x1e83409a", "0x1e83409a", "0x1e83409a", "0x1e83409a", "0x1e83409a", "0x1e83409a", "0x1e83409a", "0x1e83409a", "0x1e83409a", "0x1e83409a", "0x1e83409a", "0x1e83409a", "0x1e83409a", "0x1e83409a", "0x1e83409a", "0x1e83409a", "0x1e83409a", "0x1e83409a", "0x1e83409a", "0x1e83409a", "0x1e83409a", "0x1e83409a", "0x1e83409a", "0x1e83409a", "0x1e83409a", "0x1e83409a", "0x1e83409a", "0x1e83409a", "0x1e83409a", "0x1e83409a", "0x1e83409a", "0x1e83409a", "0x1e83409a", "0x1e83409a", "0x1e83409a", "0x1e83409a", "0x1e83409a", "0x1e83409a", "0x1e83409a", "0x1e83409a", "0x1e83409a", "0x1e83409a", "0x1e83409a", "0x1e83409a", "0x1e83409a", "0x1e83409a", "0x1e83409a", "0x1e83409a", "0x1e83409a", "0x1e83409a", "0x1e83409a", "0x1e83409a", "0x1e83409a", "0x1e83409a", "0x1e83409a", "0x1e83409a", "0x1e83409a", "0x1e83409a", "0x1e83409a", "0x1e83409a", "0x1e83409a", "0x1e83409a", "0x1e83409a", "0x1e83409a", "0x1e83409a", "0x1e83409a", "0x1e83409a", "0x1e83409a", "0x1e83409a", "0x1e83409a", "0x1e83409a", "0x1e83409a", "0x1e83409a", "0x1e83409a", "0x1e83409a", "0x1e83409a", "0x1e83409a", "0x1e83409a", "0x1e83409a", "0x1e83409a", "0x1e83409a", "0x1e83409a", "0x1e83409a", "0x1e83409a", "0x1e83409a", "0x1e83409a", "0x1e83409a", "0x1e83409a", "0x1e83409a", "0x1e83409a", "0x1e83409a", "0x1e83409a", "0x1e83409a", "0x1e83409a", "0x1e83409a", "0x1e83409a", "0x1e83409a", "0x1e83409a", "0x1e83409a", "0x1e83409a", "0x1e83409a", "0x1e83409a", "0x1e83409a", "0x1e83409a", "0x1e83409a", "0x1e83409a", "0x1e83409a", "0x1e83409a", "0x1e83409a", "0x1e83409a", "0x1e83409a", "0x1e83409a", "0x1e83409a", "0x1e83409a", "0x1e83409a", "0x1e83409a", "0x1e83409a", "0x1e83409a", "0x1e83409a", "0x1e83409a", "0x1e83409a", "0x1e83409a", "0x1e83409a", "0x1e83409a", "0x1e83409a", "0x1e83409a", "0x1e83409a", "0x1e83409a", "0x1e83409a", "0x1e83409a", "0x1e83409a", "0x1e83409a", "0x1e83409a", "0x1e83409a", "0x1e83409a", "0x1e83409a", "0x1e83409a", "0x1e83409a", "0x1e83409a", "0x1e83409a", "0x1e83409a", "0x1e83409a", "0x1e83409a", "0x1e83409a", "0x1e83409a", "0x1e83409a", "0x1e83409a", "0x1e83409a", "0x1e83409a", "0x1e83409a", "0x1e83409a", "0x1e83409a", "0x1e83409a", "0x1e83409a", "0x1e83409a", "0x1e83409a", "0x1e83409a", "0x1e83409a", "0x1e83409a", "0x1e83409a", "0x1e83409a", "0x1e83409a", "0x1e83409a", "0x1e83409a", "0x1e83409a", "0x1e83409a", "0x1e83409a", "0x1e83409a", "0x1e83409a", "0x1e83409a", "0x1e83409a", "0x1e83409a", "0x1e83409a", "0x1e83409a", "0x1e83409a", "0x1e83409a", "0x1e83409a", "0x1e83409a", "0x1e83409a", "0x1e83409a", "0x1e83409a", "0x1e83409a", "0x1e83409a", "0x1e83409a", "0x1e83409a", "0x1e83409a", "0x1e83409a", "0x1e83409a", "0x1e83409a", "0x1e83409a", "0x1e83409a", "0x1e83409a", "0x1e83409a", "0x1e83409a", "0x1e83409a", "0x1e83409a", "0x1e83409a", "0x1e83409a", "0x1e83409a", "0x1e83409a", "0x1e83409a", "0x1e83409a", "0x1e83409a", "0x1e83409a", "0x1e83409a", "0x1e83409a", "0x1e83409a", "0x1e83409a", "0x1e83409a", "0x1e83409a", "0x1e83409a", "0x1e83409a", "0x1e83409a", "0x1e83409a", "0x1e83409a", "0x1e83409a", "0x1e83409a", "0x1e83409a", "0x1e83409a", "0x1e83409a", "0x1e83409a", "0x1e83409a", "0x1e83409a", "0x1e83409a", "0x1e83409a", "0x1e83409a", "0x1e83409a", "0x1e83409a", "0x1e83409a", "0x1e83409a", "0x1e83409a", "0x1e83409a", "0x1e83409a", "0x1e83409a", "0x1e83409a", "0x1e83409a", "0x1e83409a", "0x1e83409a", "0x1e83409a", "0x1e83409a", "0x1e83409a", "0x1e83409a", "0x1e83409a", "0x1e83409a", "0x1e83409a", "0x1e83409a", "0x1e83409a", "0x1e83409a", "0x1e83409a", "0x1e83409a", "0x1e83409a", "0x1e83409a", "0x1e83409a", "0x1e83409a", "0x1e83409a", "0x1e83409a", "0x1e83409a", "0x1e83409a", "0x1e83409a", "0x1e83409a", "0x1e83409a", "0x1e83409a", "0x1e83409a", "0x1e83409a", "0x1e83409a", "0x1e83409a", "0x1e83409a", "0x1e83409a", "0x1e83409a", "0x1e83409a", "0x1e83409a", "0x1e83409a", "0x1e83409a", "0x1e83409a", "0x1e83409a", "0x1e83409a", "0x1e83409a", "0x1e83409a", "0x1e83409a", "0x1e83409a", "0x1e83409a", "0x1e83409a", "0x1e83409a", "0x1e83409a", "0x1e83409a", "0x1e83409a", "0x1e83409a", "0x1e83409a", "0x1e83409a", "0x1e83409a", "0x1e83409a", "0x1e83409a", "0x1e83409a", "0x1e83409a", "0x1e83409a", "0x1e83409a", "0x1e83409a", "0x1e83409a", "0x1e83409a", "0x1e83409a", "0x1e83409a", "0x1e83409a", "0x1e83409a", "0x1e83409a", "0x1e83409a", "0x1e83409a", "0x1e83409a", "0x1e83409a", "0x1e83409a", "0x1e83409a", "0x1e83409a", "0x1e83409a", "0x1e83409a", "0x1e83409a", "0x1e83409a", "0x1e83409a", "0x1e83409a", "0x1e83409a", "0x1e83409a", "0x1e83409a", "0x1e83409a", "0x1e83409a", "0x1e83409a", "0x1e83409a", "0x1e83409a", "0x1e83409a", "0x1e83409a", "0x1e83409a", "0x1e83409a", "0x1e83409a", "0x1e83409a", "0x1e83409a", "0x1e83409a", "0x1e83409a", "0x1e83409a", "0x1e83409a", "0x1e83409a", "0x1e83409a", "0x1e83409a", "0x1e83409a", "0x1e83409a", "0x1e83409a", "0x1e83409a", "0x1e83409a", "0x1e83409a", "0x1e83409a", "0x1e83409a", "0x1e83409a", "0x1e83409a", "0x1e83409a", "0x1e83409a", "0x1e83409a", "0x1e83409a", "0x1e83409a", "0x1e83409a", "0x1e83409a", "0x1e83409a", "0x1e83409a", "0x1e83409a", "0x1e83409a", "0x1e83409a", "0x1e83409a", "0x1e83409a", "0x1e83409a", "0x1e83409a", "0x1e83409a", "0x1e83409a", "0x1e83409a", "0x1e83409a", "0x1e83409a", "0x1e83409a", "0x1e83409a", "0x1e83409a", "0x1e83409a", "0x1e83409a", "0x1e83409a", "0x1e83409a", "0x1e83409a", "0x1e83409a", "0x1e83409a", "0x1e83409a", "0x1e83409a", "0x1e83409a", "0x1e83409a", "0x1e83409a", "0x1e83409a", "0x1e83409a", "0x1e83409a", "0x1e83409a", "0x1e83409a", "0x1e83409a", "0x1e83409a", "0x1e83409a", "0x1e83409a", "0x1e83409a"], "exploit_name": "VTF_exp", "function_sigs": ["0xe5d6bf02", "0x095ea7b3", "0x1e83409a", "0xa9059cbb", "0x5c11d795"]}, {"calls": ["0x1e83409a", "0xb45b25a9", "0xaff8d492", "0xbc9dac1d"], "exploit_name": "Yyds_exp", "function_sigs": ["0xaff8d492", "0x022c0d9f", "0xbc9dac1d", "0xa9059cbb", "0x1e83409a", "0xb45b25a9"]}, {"calls": ["0xc2998238", "0xa0712d68", "0xc5ebeaec", "0xde5f6268", "0x853828b6", "0x0e752702", "0x852a12e3"], "exploit_name": "MooCAKECTX_exp", "function_sigs": ["0xc5ebeaec", "0x095ea7b3", "0xa0712d68", "0xc2998238", "0xde5f6268", "0x853828b6", "0xa9059cbb", "0x5c11d795", "0x852a12e3", "0x0e752702"]}, {"calls": ["0x70a08231", "0x266cf109", "0x65bc9481", "0x70ca10bb", "0x70ca10bb", "0x70ca10bb", "0x70ca10bb", "0x70ca10bb", "0x70ca10bb", "0x70ca10bb", "0x45b56078", "0x7bde82f2", "0x90c5013b"], "exploit_name": "EFVault_exp", "function_sigs": ["0x266cf109", "0x7bde82f2", "0x65bc9481", "0x90c5013b", "0x70a08231", "0x45b56078", "0x70ca10bb"]}, {"calls": ["0xc657c718", "0xc657c718", "0xc657c718", "0xc657c718", "0xc657c718", "0xc657c718", "0xc657c718", "0xc657c718", "0xc657c718", "0xd084c0a6", "0xd084c0a6", "0xd084c0a6", "0xd084c0a6", "0x76704de0", "0x76704de0", "0x76704de0", "0x76704de0", "0xc88a5e6d", "0xb86f3ea6", "0xb86f3ea6", "0x3df02124", "0xb86f3ea6", "0x3df02124", "0xb86f3ea6"], "exploit_name": "TeamFinance", "function_sigs": ["0xd084c0a6", "0x095ea7b3", "0xc657c718", "0x3df02124", "0xc88a5e6d", "0xb86f3ea6", "0x76704de0"]}, {"calls": ["0xc657c718", "0xc657c718", "0x2646478b", "0xfa461e33"], "exploit_name": "Sushi_Router_exp", "function_sigs": ["0xfa461e33", "0x2646478b", "0xc657c718"]}, {"calls": ["0xc657c718", "0xc657c718", "0xc657c718", "0xc657c718", "0xc657c718", "0xc657c718", "0xc657c718", "0xc657c718", "0xc657c718", "0xc657c718", "0xc657c718", "0xc657c718", "0xc657c718", "0xc657c718", "0xc657c718", "0xc657c718", "0xc657c718", "0xc657c718", "0xc657c718", "0x5c38449e", "0xab9c4b5d", "0xab9c4b5d", "0xc2998238", "0x0b4c7e4d", "0xa0712d68", "0x0b4c7e4d", "0x269b5581", "0xc5ebeaec", "0xc5ebeaec", "0xc5ebeaec", "0xc5ebeaec", "0x2f865568", "0x8f15b6b5", "0x3df02124", "0x0b4c7e4d", "0x8f15b6b5"], "exploit_name": "Midas_exp", "function_sigs": ["0xc5ebeaec", "0x414bf389", "0x095ea7b3", "0xab9c4b5d", "0xc657c718", "0xa0712d68", "0x0b4c7e4d", "0xc2998238", "0x3df02124", "0x8f15b6b5", "0x2f865568", "0xceb757d5", "0x5c38449e", "0x269b5581", "0xa9059cbb"]}, {"calls": ["0xd0e30db0", "0x06447d56", "0x3d18b912", "0x90c5013b"], "exploit_name": "PancakeHunny_exp", "function_sigs": ["0x3d18b912", "0x7ff36ab5", "0x90c5013b", "0xd0e30db0", "0x06447d56", "0xa9059cbb", "0x18cbafe5"]}, {"calls": ["0xca669fa7", "0xe5d6bf02", "0xca669fa7", "0x33500e26", "0x8e1764f4", "0xca669fa7", "0x33500e26", "0xe5d6bf02", "0xca669fa7", "0xa8feda51"], "exploit_name": "AkutarNFT_exp", "function_sigs": ["0xe5d6bf02", "0x33500e26", "0x8e1764f4", "0xca669fa7", "0xa8feda51"]}, {"calls": ["0x06447d56"], "exploit_name": "Meter_exp", "function_sigs": ["0x38ed1739", "0x06447d56"]}, {"calls": ["0xc657c718", "0xc657c718", "0xc657c718", "0xc657c718", "0x32854cc2", "0x32854cc2", "0x32854cc2", "0x32854cc2", "0x32854cc2", "0x32854cc2", "0x32854cc2", "0x32854cc2", "0x32854cc2", "0x32854cc2", "0x32854cc2", "0x32854cc2", "0x32854cc2", "0x32854cc2", "0x32854cc2", "0x32854cc2", "0x32854cc2", "0x32854cc2", "0x32854cc2", "0x32854cc2", "0x32854cc2", "0x32854cc2", "0x32854cc2", "0x32854cc2", "0x32854cc2", "0x32854cc2", "0x32854cc2"], "exploit_name": "<PERSON><PERSON><PERSON><PERSON><PERSON>_SwapRouter", "function_sigs": ["0x32854cc2", "0xc657c718"]}, {"calls": ["0xffa18649", "0xa67a6a45", "0x23b872dd"], "exploit_name": "MEVbadc0de_exp", "function_sigs": ["0xa67a6a45", "0xffa18649", "0x23b872dd"]}, {"calls": ["0x31daf7fe"], "exploit_name": "BrahTOPG_exp", "function_sigs": ["0x31daf7fe", "0xa9059cbb", "0x5c11d795", "0x095ea7b3"]}, {"calls": ["0xc657c718", "0xc657c718", "0xc657c718", "0xc657c718", "0xc657c718", "0xc657c718", "0xc657c718", "0x9908fc8b", "0xb6b55f25", "0x9908fc8b", "0xb6b55f25", "0x2da478aa", "0x2e1a7d4d", "0x2da478aa", "0x2e1a7d4d", "0x9908fc8b"], "exploit_name": "Allbridge_exp2", "function_sigs": ["0x095ea7b3", "0xc657c718", "0x2da478aa", "0x022c0d9f", "0xb6b55f25", "0x2e1a7d4d", "0xa9059cbb", "0x9908fc8b"]}, {"calls": ["0xabb3bd5e", "0x011b51d5", "0x011b51d5", "0x011b51d5", "0x011b51d5", "0x011b51d5"], "exploit_name": "THB_exp", "function_sigs": ["0x011b51d5", "0xabb3bd5e"]}, {"calls": ["0xa0712d68", "0x144fa6d7", "0xa694fc3a", "0x144fa6d7", "0x1f7b4f30", "0x2e1a7d4d"], "exploit_name": "HPAY_exp", "function_sigs": ["0x095ea7b3", "0xa0712d68", "0x144fa6d7", "0xa694fc3a", "0x1f7b4f30", "0x2e1a7d4d", "0x5c11d795"]}, {"calls": ["0x83f12fec"], "exploit_name": "BEC_exp", "function_sigs": ["0x83f12fec"]}, {"calls": ["0x6c648fca"], "exploit_name": "Chainswap_exp2", "function_sigs": ["0x6c648fca"]}, {"calls": ["0x84800812", "0x84800812", "0x84800812", "0x84800812"], "exploit_name": "BNB48MEVBot_exp", "function_sigs": ["0x84800812"]}, {"calls": ["0x02e236bc", "0x02e236bc", "0x5dcb7ab2", "0xf2465f09"], "exploit_name": "Revest_exp", "function_sigs": ["0x095ea7b3", "0x02e236bc", "0x5dcb7ab2", "0x022c0d9f", "0xf2465f09", "0xa9059cbb"]}, {"calls": ["0xf483b3da", "0xc0a47c93", "0x02b9446c", "0x860ffea1", "0x4b8a3529", "0x02ce728f", "0x912860c5", "0xf7888aec", "0x97da6d30", "0xf7888aec", "0x97da6d30"], "exploit_name": "Kashi_exp", "function_sigs": ["0xf7888aec", "0x095ea7b3", "0x8803dbee", "0x860ffea1", "0x02b9446c", "0x4b8a3529", "0xf483b3da", "0xc0a47c93", "0x912860c5", "0x97da6d30", "0x02ce728f", "0xa9059cbb"]}, {"calls": ["0xc657c718", "0xc657c718", "0xc657c718", "0xc657c718", "0xc657c718", "0xc657c718", "0xc657c718", "0x06447d56", "0xe2bbb158", "0x90c5013b"], "exploit_name": "BXH_exp", "function_sigs": ["0x095ea7b3", "0xc657c718", "0x90c5013b", "0xe2bbb158", "0x06447d56", "0x022c0d9f", "0xa9059cbb", "0x5c11d795"]}, {"calls": ["0xded998b9", "0xded998b9", "0xed856cdc", "0xbe7305a1", "0xded998b9", "0xed856cdc"], "exploit_name": "ElasticSwap_exp", "function_sigs": ["0x095ea7b3", "0xbe7305a1", "0xded998b9", "0x022c0d9f", "0xed856cdc", "0xa9059cbb"]}, {"calls": ["0xd0e30db0"], "exploit_name": "Uranium_exp", "function_sigs": ["0xd0e30db0", "0xa9059cbb", "0x022c0d9f"]}, {"calls": ["0x7271479a"], "exploit_name": "Discover_exp", "function_sigs": ["0x022c0d9f", "0x7271479a"]}, {"calls": ["0x4a248d2a", "0xd4b97046", "0x4a248d2a", "0xd4b97046", "0x4a248d2a", "0xd4b97046", "0x4a248d2a", "0xd4b97046", "0x4a248d2a", "0xd4b97046", "0x4a248d2a", "0xd4b97046", "0x4a248d2a", "0xd4b97046", "0x4a248d2a", "0xd4b97046", "0x8ba14562", "0xc2998238", "0x1249c58b", "0xa0712d68", "0xa0712d68", "0xa0712d68", "0x5ec88c79", "0xc5ebeaec", "0xc2ca269a", "0xbc25cf77", "0x0e752702", "0x852a12e3", "0x852a12e3", "0x852a12e3", "0x852a12e3", "0xe5d6bf02", "0x379607f5"], "exploit_name": "INUKO_exp", "function_sigs": ["0x8ba14562", "0xc5ebeaec", "0x095ea7b3", "0xbc25cf77", "0xe5d6bf02", "0xa0712d68", "0xc2998238", "0x379607f5", "0x5ec88c79", "0xe8e33700", "0xa9059cbb", "0xc2ca269a", "0x4a248d2a", "0xd4b97046", "0x5c11d795", "0x0e752702", "0x852a12e3", "0x1249c58b"]}, {"calls": ["0xab9c4b5d", "0x4515cef3", "0x6e553f65", "0xa0712d68", "0xc2998238", "0x1a4c1ca3", "0xc5ebeaec", "0x1a4c1ca3", "0x1a4c1ca3", "0x1a4d01d2", "0x1a4c1ca3"], "exploit_name": "InverseFinance_exp", "function_sigs": ["0xc5ebeaec", "0x6e553f65", "0xab9c4b5d", "0x095ea7b3", "0xa0712d68", "0x4515cef3", "0xc2998238", "0x1a4d01d2", "0x1a4c1ca3"]}, {"calls": ["0x5122120b", "0x70a08231", "0x70a08231", "0x19b4c989", "0xe5d6bf02", "0xbe4ebaed", "0xbe4ebaed", "0xbe4ebaed", "0xbe4ebaed", "0xbe4ebaed", "0xbe4ebaed", "0xbe4ebaed", "0xbe4ebaed", "0xbe4ebaed", "0xbd586299", "0x70a08231"], "exploit_name": "DPC_exp", "function_sigs": ["0xbe4ebaed", "0x5122120b", "0x095ea7b3", "0xe5d6bf02", "0x70a08231", "0x38ed1739", "0xbd586299", "0xe8e33700", "0x5c11d795", "0x19b4c989"]}, {"calls": ["0xc657c718", "0xc657c718", "0xc657c718", "0xc657c718", "0xc657c718", "0x70a08231", "0x266cf109", "0x65bc9481", "0x70ca10bb", "0x4f1f05bc", "0x4f1f05bc", "0x4f1f05bc", "0x4f1f05bc", "0x4f1f05bc", "0x4f1f05bc", "0x4f1f05bc", "0x4f1f05bc", "0x4f1f05bc", "0x4f1f05bc", "0x4f1f05bc", "0x4f1f05bc", "0x4f1f05bc", "0x4f1f05bc"], "exploit_name": "SwapX_exp", "function_sigs": ["0x266cf109", "0x095ea7b3", "0x65bc9481", "0xc657c718", "0x70a08231", "0x38ed1739", "0x70ca10bb", "0x4f1f05bc"]}, {"calls": ["0xd96a094a", "0x1e69fcc4", "0xfff6cae9", "0xe4849b32"], "exploit_name": "ZoomproFinance_exp", "function_sigs": ["0xd96a094a", "0x095ea7b3", "0x1e69fcc4", "0xe4849b32", "0x022c0d9f", "0xfff6cae9", "0xa9059cbb"]}, {"calls": ["0x29965a1d", "0x2e1a7d4d", "0x1249c58b", "0xc5ebeaec", "0xa6afed95", "0xc5ebeaec", "0xc5ebeaec", "0xd0e30db0"], "exploit_name": "Cream_exp", "function_sigs": ["0xc5ebeaec", "0x095ea7b3", "0x38ed1739", "0x29965a1d", "0xa6afed95", "0xd0e30db0", "0x022c0d9f", "0x2e1a7d4d", "0xa9059cbb", "0x1249c58b"]}, {"calls": ["0x0881d06f", "0xfff6cae9"], "exploit_name": "DDC_exp", "function_sigs": ["0x0881d06f", "0x38ed1739", "0x095ea7b3", "0xfff6cae9"]}, {"calls": [], "exploit_name": "Anyswap_poc", "function_sigs": ["0xa9059cbb", "0x8d7d3eea"]}, {"calls": ["0x47e7ef24", "0x4b8a3529"], "exploit_name": "TIFI_exp", "function_sigs": ["0x095ea7b3", "0x4b8a3529", "0x022c0d9f", "0xa9059cbb", "0x5c11d795", "0x47e7ef24"]}, {"calls": ["0x61b69abd", "0x61b69abd", "0x61b69abd", "0x61b69abd", "0x61b69abd", "0x61b69abd", "0x61b69abd", "0x61b69abd", "0x61b69abd", "0x61b69abd", "0x61b69abd", "0x61b69abd", "0x61b69abd", "0x61b69abd", "0x61b69abd", "0x61b69abd", "0x61b69abd", "0x61b69abd", "0x61b69abd", "0x61b69abd", "0x61b69abd", "0x61b69abd", "0x61b69abd", "0x61b69abd", "0x61b69abd", "0x61b69abd", "0x61b69abd", "0x61b69abd", "0x61b69abd", "0x61b69abd", "0x61b69abd", "0x61b69abd", "0x61b69abd", "0x61b69abd", "0x61b69abd", "0x61b69abd", "0x61b69abd", "0x61b69abd", "0x61b69abd", "0x61b69abd", "0x61b69abd", "0x61b69abd", "0x61b69abd", "0x61b69abd", "0x61b69abd", "0x61b69abd", "0x61b69abd", "0x61b69abd", "0x61b69abd", "0x61b69abd", "0x61b69abd", "0x61b69abd", "0x61b69abd", "0x61b69abd", "0x61b69abd", "0x61b69abd", "0x61b69abd", "0x61b69abd", "0x61b69abd", "0x61b69abd", "0x61b69abd", "0x61b69abd", "0x61b69abd", "0x61b69abd", "0x61b69abd", "0x61b69abd", "0x61b69abd", "0x61b69abd", "0x61b69abd", "0x61b69abd", "0x61b69abd", "0x61b69abd", "0x61b69abd", "0x61b69abd", "0x61b69abd", "0x61b69abd", "0x61b69abd", "0x61b69abd", "0x61b69abd", "0x61b69abd", "0x61b69abd", "0x61b69abd", "0x61b69abd", "0x61b69abd", "0x61b69abd", "0x61b69abd", "0x61b69abd", "0x61b69abd", "0x61b69abd", "0x61b69abd", "0x61b69abd", "0x61b69abd", "0x61b69abd", "0x61b69abd", "0x61b69abd", "0x61b69abd", "0x61b69abd", "0x61b69abd", "0x61b69abd", "0x61b69abd", "0x61b69abd", "0x61b69abd", "0x61b69abd", "0x61b69abd", "0x61b69abd", "0x61b69abd", "0x61b69abd", "0x61b69abd", "0x61b69abd", "0x61b69abd", "0x61b69abd", "0x61b69abd", "0x61b69abd", "0x61b69abd", "0x61b69abd", "0x61b69abd", "0x61b69abd", "0x61b69abd", "0x61b69abd", "0x61b69abd", "0x61b69abd", "0x61b69abd", "0x61b69abd", "0x61b69abd", "0x61b69abd", "0x61b69abd", "0x61b69abd", "0x61b69abd", "0x61b69abd", "0x61b69abd", "0x61b69abd", "0x61b69abd", "0x61b69abd", "0x61b69abd", "0x61b69abd", "0x61b69abd"], "exploit_name": "Optimism_exp", "function_sigs": ["0x61b69abd"]}, {"calls": ["0x06447d56", "0x23b872dd", "0x5cffe9de", "0xc4a0db96", "0x48c54b9d", "0xa22cb465", "0x3d5d190c"], "exploit_name": "Bayc_apecoin_exp", "function_sigs": ["0x095ea7b3", "0xc4a0db96", "0x48c54b9d", "0x3d5d190c", "0x06447d56", "0x5cffe9de", "0xa22cb465", "0x23b872dd"]}, {"calls": ["0x5c38449e", "0xc8820f6c", "0xc8820f6c", "0xc8820f6c", "0xc8820f6c", "0xc8820f6c", "0xc8820f6c", "0xc8820f6c", "0xc8820f6c", "0x853828b6", "0x89afcb44"], "exploit_name": "Grim_exp", "function_sigs": ["0xc8820f6c", "0x095ea7b3", "0x853828b6", "0xe8e33700", "0x5c38449e", "0x89afcb44", "0xa9059cbb"]}, {"calls": ["0xc88a5e6d", "0x1f7b4f30", "0x06447d56", "0x23b872dd", "0x90c5013b", "0x45b56078", "0xc88a5e6d", "0x23b872dd", "0xe6ee8580", "0xc88a5e6d", "0x23b872dd", "0xe6ee8580", "0xc88a5e6d", "0x23b872dd", "0xe6ee8580", "0xc88a5e6d", "0x23b872dd", "0xe6ee8580", "0xc88a5e6d", "0x23b872dd", "0xe6ee8580", "0xc88a5e6d", "0x23b872dd", "0xe6ee8580", "0xc88a5e6d", "0x23b872dd", "0xe6ee8580", "0xc88a5e6d", "0x23b872dd", "0xe6ee8580", "0xc88a5e6d", "0x23b872dd", "0xe6ee8580", "0xc88a5e6d", "0x23b872dd", "0xe6ee8580", "0xc88a5e6d", "0x23b872dd", "0xe6ee8580", "0xc88a5e6d", "0x23b872dd", "0xe6ee8580", "0xc88a5e6d", "0x23b872dd", "0xe6ee8580", "0xc88a5e6d", "0x23b872dd", "0xe6ee8580", "0xc88a5e6d", "0x23b872dd", "0xe6ee8580", "0xc88a5e6d", "0x23b872dd", "0xe6ee8580", "0xc88a5e6d", "0x23b872dd", "0xe6ee8580", "0xc88a5e6d", "0x23b872dd", "0xe6ee8580", "0xc88a5e6d", "0x23b872dd", "0xe6ee8580", "0xc88a5e6d", "0x23b872dd", "0xe6ee8580", "0xc88a5e6d", "0x23b872dd", "0xe6ee8580", "0xc88a5e6d", "0x23b872dd", "0xe6ee8580", "0xc88a5e6d", "0x23b872dd", "0xe6ee8580", "0xc88a5e6d", "0x23b872dd", "0xe6ee8580", "0xc88a5e6d", "0x23b872dd", "0xe6ee8580", "0xc88a5e6d", "0x23b872dd", "0xe6ee8580", "0xc88a5e6d", "0x23b872dd", "0xe6ee8580", "0xc88a5e6d", "0x23b872dd", "0xe6ee8580", "0xc88a5e6d", "0x23b872dd", "0xe6ee8580", "0xc88a5e6d", "0x23b872dd", "0xe6ee8580", "0xc88a5e6d", "0x23b872dd", "0xe6ee8580", "0xc88a5e6d", "0x23b872dd", "0xe6ee8580", "0xc88a5e6d", "0x23b872dd", "0xe6ee8580", "0x8735442a", "0x8735442a", "0x8735442a", "0x8735442a", "0x8735442a", "0x8735442a", "0x8735442a", "0x8735442a", "0x8735442a", "0x8735442a", "0x8735442a", "0x8735442a", "0x8735442a", "0x8735442a", "0x8735442a", "0x8735442a", "0x8735442a", "0x8735442a", "0x8735442a", "0x8735442a", "0x8735442a", "0x8735442a", "0x8735442a", "0x8735442a", "0x8735442a", "0x8735442a", "0x8735442a", "0x8735442a", "0x8735442a", "0x8735442a", "0x8735442a", "0x8735442a", "0x8735442a"], "exploit_name": "XCarnival", "function_sigs": ["0xe6ee8580", "0x8735442a", "0x90c5013b", "0x45b56078", "0xc88a5e6d", "0x06447d56", "0x1f7b4f30", "0x23b872dd"]}, {"calls": [], "exploit_name": "HEALTH_exp", "function_sigs": ["0xa9059cbb", "0x5c11d795", "0x095ea7b3"]}, {"calls": ["0xc0406226"], "exploit_name": "Allbridge_exp", "function_sigs": ["0xc0406226"]}, {"calls": ["0x06447d56", "0x8a809095", "0x90c5013b"], "exploit_name": "ATK_exp", "function_sigs": ["0x095ea7b3", "0x90c5013b", "0x06447d56", "0x022c0d9f", "0x8a809095", "0xa9059cbb", "0x5c11d795"]}, {"calls": ["0xc657c718", "0xc657c718", "0xc657c718", "0x70a08231", "0x266cf109", "0x65bc9481", "0x70ca10bb", "0x70ca10bb", "0x70ca10bb", "0x70ca10bb", "0x70ca10bb", "0x70ca10bb", "0x70ca10bb", "0xc3d718a4"], "exploit_name": "Dexible_exp", "function_sigs": ["0x266cf109", "0x095ea7b3", "0x65bc9481", "0xc657c718", "0x70a08231", "0xc3d718a4", "0x70ca10bb"]}, {"calls": [], "exploit_name": "SEAMAN_exp", "function_sigs": ["0xa9059cbb", "0x5c11d795", "0x095ea7b3"]}, {"calls": ["0xca669fa7", "0x93ba3f15", "0xca669fa7", "0xca669fa7", "0xc9d27afe", "0xda35c664", "0x3e4f49e6", "0xe5d6bf02", "0x3e4f49e6", "0x44c028fe", "0x23b872dd"], "exploit_name": "BuildF_exp", "function_sigs": ["0xe5d6bf02", "0x095ea7b3", "0x93ba3f15", "0x3e4f49e6", "0xc9d27afe", "0xa9059cbb", "0xca669fa7", "0x44c028fe", "0xda35c664", "0x23b872dd"]}, {"calls": ["0x5c38449e", "0xa22cb465", "0x5cffe9de", "0xc4a0db96", "0x12424e3f", "0x23b872dd", "0x23b872dd", "0x23b872dd", "0x23b872dd", "0x23b872dd", "0x23b872dd", "0x23b872dd", "0x23b872dd", "0x23b872dd", "0x23b872dd", "0x23b872dd", "0x23b872dd", "0x23b872dd", "0x23b872dd", "0x23b872dd", "0x23b872dd", "0x23b872dd", "0x23b872dd", "0x23b872dd", "0x23b872dd", "0x4dd2f3a2", "0x1ba91d46", "0x42842e0e", "0x42842e0e", "0x42842e0e", "0x9e5faafc", "0x853828b6", "0x3d5d190c", "0x2e1a7d4d"], "exploit_name": "Omni_exp", "function_sigs": ["0x095ea7b3", "0x8803dbee", "0xc4a0db96", "0x42842e0e", "0x2e1a7d4d", "0x853828b6", "0x1ba91d46", "0x3d5d190c", "0x9e5faafc", "0x5c38449e", "0x12424e3f", "0x4dd2f3a2", "0x5cffe9de", "0xa9059cbb", "0xa22cb465", "0x23b872dd"]}, {"calls": ["0xa6aa57ce", "0xa6aa57ce", "0xa6aa57ce", "0xdb006a75"], "exploit_name": "Bacon_exp", "function_sigs": ["0x095ea7b3", "0xa6aa57ce", "0xdb006a75", "0x022c0d9f", "0xa9059cbb"]}, {"calls": ["0xdf791e50", "0xdf791e50"], "exploit_name": "APC_exp", "function_sigs": ["0xa9059cbb", "0x5c11d795", "0x095ea7b3", "0xdf791e50"]}, {"calls": ["0xc88a5e6d", "0xab9c4b5d", "0x5c38449e", "0x0b4c7e4d", "0xc2998238", "0xb6b55f25", "0xa0712d68", "0x269b5581", "0xc5ebeaec", "0x2f865568", "0x853828b6", "0x269b5581", "0xc5ebeaec", "0xd0e30db0"], "exploit_name": "Market_exp", "function_sigs": ["0xc5ebeaec", "0xab9c4b5d", "0x095ea7b3", "0xa0712d68", "0x0b4c7e4d", "0xc2998238", "0x853828b6", "0xc88a5e6d", "0x2f865568", "0xd0e30db0", "0x5c38449e", "0x269b5581", "0xb6b55f25", "0xbc651188", "0xa9059cbb", "0x5c11d795"]}, {"calls": ["0xc657c718", "0xc657c718", "0xc657c718", "0xc657c718", "0xc657c718", "0x70a08231", "0x266cf109", "0x65bc9481", "0x70ca10bb", "0xa0712d68", "0xbea9e2d9", "0x2a86b16e"], "exploit_name": "Phoenix_exp", "function_sigs": ["0x266cf109", "0x095ea7b3", "0x65bc9481", "0xc657c718", "0xa0712d68", "0xbea9e2d9", "0x70a08231", "0x38ed1739", "0xe8e33700", "0x2a86b16e", "0xa9059cbb", "0x70ca10bb"]}, {"calls": ["0x32a09b10"], "exploit_name": "BEGO_exp", "function_sigs": ["0x32a09b10", "0x5c11d795", "0x095ea7b3"]}, {"calls": ["0x42966c68", "0x42966c68", "0x42966c68", "0x42966c68", "0x42966c68", "0x42966c68", "0x42966c68", "0x42966c68", "0x42966c68", "0x42966c68", "0x42966c68", "0x42966c68", "0x42966c68", "0x42966c68", "0x42966c68", "0x42966c68", "0x42966c68", "0x42966c68", "0x42966c68", "0x42966c68", "0x42966c68", "0x42966c68", "0x42966c68", "0x42966c68", "0x42966c68", "0x42966c68", "0x42966c68", "0x42966c68", "0x42966c68", "0x42966c68", "0x42966c68", "0x42966c68", "0x42966c68", "0x42966c68", "0x42966c68", "0x42966c68", "0x42966c68", "0x42966c68", "0x42966c68", "0x42966c68", "0x42966c68", "0x42966c68", "0x42966c68", "0x42966c68", "0x42966c68", "0x42966c68", "0x42966c68", "0x42966c68", "0x42966c68", "0x42966c68", "0x42966c68", "0x42966c68", "0x42966c68", "0x42966c68", "0x42966c68", "0x42966c68", "0x42966c68", "0x42966c68", "0x42966c68", "0x42966c68", "0x42966c68", "0x42966c68", "0x42966c68", "0x42966c68", "0x42966c68", "0x42966c68", "0x42966c68", "0x42966c68", "0x42966c68", "0x42966c68", "0x42966c68", "0x42966c68", "0x42966c68", "0x42966c68", "0x42966c68", "0x42966c68", "0x42966c68", "0x42966c68", "0x42966c68", "0x42966c68", "0x42966c68", "0x42966c68", "0x42966c68", "0x42966c68", "0x42966c68", "0x42966c68", "0x42966c68", "0x42966c68", "0x42966c68", "0x42966c68", "0x42966c68", "0x42966c68", "0x42966c68", "0xfff6cae9"], "exploit_name": "Sheep_exp", "function_sigs": ["0x42966c68", "0x095ea7b3", "0xfff6cae9", "0xa9059cbb", "0x5c11d795"]}, {"calls": ["0xc657c718", "0xc657c718"], "exploit_name": "LPC", "function_sigs": ["0xa9059cbb", "0x022c0d9f", "0xc657c718"]}, {"calls": [], "exploit_name": "interface", "function_sigs": []}, {"calls": ["0xd0e30db0", "0x23b872dd", "0xfff6cae9"], "exploit_name": "Novo_exp", "function_sigs": ["0x095ea7b3", "0xd0e30db0", "0x022c0d9f", "0xfff6cae9", "0xa9059cbb", "0x5c11d795", "0x23b872dd"]}, {"calls": ["0xff3f1869"], "exploit_name": "Pickle_exp", "function_sigs": ["0xff3f1869"]}, {"calls": ["0xc657c718", "0xc657c718", "0xc657c718", "0xc657c718", "0xc657c718", "0xc657c718", "0xc657c718", "0xc657c718", "0xc657c718", "0xc657c718", "0xc657c718", "0xc657c718", "0xc657c718", "0x42b0b77c", "0x4cc82215", "0x617ba037", "0x4cc82215", "0x617ba037", "0x4cc82215", "0x617ba037", "0x4cc82215", "0x617ba037", "0x4cc82215", "0x617ba037", "0x4cc82215", "0x617ba037", "0x4cc82215", "0x617ba037", "0x4cc82215", "0x2e1a7d4d", "0xbd5023a9", "0x1d5d7237", "0x1d5d7237", "0x1d5d7237"], "exploit_name": "paraspace_exp", "function_sigs": ["0x414bf389", "0xdb3e2198", "0x095ea7b3", "0xc657c718", "0x2e1a7d4d", "0xbd5023a9", "0x617ba037", "0x4cc82215", "0x42b0b77c", "0xa9059cbb", "0x1d5d7237"]}, {"calls": ["0xbc25cf77", "0xbc25cf77", "0xbc25cf77", "0xbc25cf77", "0xbc25cf77", "0xbc25cf77", "0xbc25cf77", "0xbc25cf77", "0xbc25cf77", "0xbc25cf77", "0xbc25cf77", "0xbc25cf77", "0xbc25cf77", "0xbc25cf77", "0xbc25cf77", "0xbc25cf77"], "exploit_name": "XST02_exp", "function_sigs": ["0xa9059cbb", "0x022c0d9f", "0xbc25cf77"]}, {"calls": ["0xca669fa7", "0x912d97fc"], "exploit_name": "Quixotic_exp", "function_sigs": ["0x912d97fc", "0xca669fa7"]}, {"calls": ["0xd9caed12", "0xfff6cae9"], "exploit_name": "RADT_exp", "function_sigs": ["0x095ea7b3", "0xd9caed12", "0xfff6cae9", "0xa9059cbb", "0x5c11d795"]}, {"calls": ["0xc657c718", "0xc657c718", "0xc657c718", "0xc657c718", "0xc657c718", "0xc657c718", "0x4641257d"], "exploit_name": "DFS_exp", "function_sigs": ["0x4641257d", "0xc657c718"]}, {"calls": ["0xd0e30db0", "0xd87aa643", "0x0c0fd549", "0x448a1047"], "exploit_name": "OmniEstate_exp", "function_sigs": ["0x095ea7b3", "0x448a1047", "0x0c0fd549", "0xd0e30db0", "0xd87aa643", "0x5c11d795"]}, {"calls": ["0x1c4009f9", "0xb6b55f25", "0xe5d6bf02", "0x1f7b4f30", "0xca669fa7", "0x4641257d", "0x63ad2c41", "0x1c4009f9", "0x3d18b912", "0xf77c4791"], "exploit_name": "PancakeBunny_exp", "function_sigs": ["0x3d18b912", "0x4641257d", "0xe5d6bf02", "0x63ad2c41", "0x095ea7b3", "0x1c4009f9", "0xf77c4791", "0x1f7b4f30", "0x022c0d9f", "0xb6b55f25", "0xa9059cbb", "0xca669fa7"]}, {"calls": ["0xc657c718", "0xc657c718", "0xc657c718", "0xc657c718", "0x06447d56", "0xc04a8a10", "0x90c5013b", "0x5c38449e", "0xe8eda9df", "0xa415bcad", "0xe8eda9df", "0xa415bcad", "0xe8eda9df", "0xa415bcad", "0xe8eda9df", "0xa415bcad", "0xe8eda9df", "0xa415bcad", "0xe8eda9df", "0xa415bcad", "0xe8eda9df", "0xa415bcad", "0xe8eda9df", "0xa415bcad", "0xe8eda9df", "0xa415bcad", "0xe8eda9df", "0xa415bcad", "0xe8eda9df", "0xa415bcad", "0xe8eda9df", "0xa415bcad", "0xe8eda9df", "0xa415bcad", "0xe8eda9df", "0xa415bcad", "0xe8eda9df", "0xa415bcad", "0xe8eda9df", "0xa415bcad", "0xe8eda9df", "0xa415bcad", "0xe8eda9df", "0xa415bcad", "0xe8eda9df", "0xa415bcad", "0xe8eda9df", "0xa415bcad", "0xe8eda9df", "0xa415bcad", "0xe8eda9df", "0xa415bcad", "0xe8eda9df", "0xa415bcad", "0xe8eda9df", "0xa415bcad", "0xe8eda9df", "0xa415bcad", "0xe8eda9df", "0xa415bcad", "0xe8eda9df", "0xa415bcad", "0xe8eda9df", "0xa415bcad", "0xe8eda9df", "0xa415bcad", "0xe8eda9df", "0xa415bcad", "0xe8eda9df", "0xa415bcad", "0xe8eda9df", "0xa415bcad", "0xe8eda9df", "0xa415bcad", "0xe8eda9df", "0xa415bcad", "0xe8eda9df", "0xa415bcad", "0xe8eda9df", "0xa415bcad", "0xe8eda9df", "0xa415bcad", "0xe8eda9df", "0xa415bcad", "0xe8eda9df", "0xa415bcad", "0xe8eda9df", "0xa415bcad", "0xe8eda9df", "0xa415bcad", "0xe8eda9df", "0xa415bcad", "0xe8eda9df", "0xa415bcad", "0xe8eda9df", "0xa415bcad", "0xe8eda9df", "0xa415bcad", "0xe8eda9df", "0xa415bcad", "0xe8eda9df", "0xa415bcad", "0xe8eda9df", "0xa415bcad", "0xe8eda9df", "0xa415bcad", "0xe8eda9df", "0xa415bcad", "0x89afcb44", "0xfff6cae9", "0xa415bcad"], "exploit_name": "RoeFinance_exp", "function_sigs": ["0xe8eda9df", "0x095ea7b3", "0xc657c718", "0x90c5013b", "0xc04a8a10", "0xa415bcad", "0x06447d56", "0x5c38449e", "0xfff6cae9", "0x89afcb44", "0xa9059cbb", "0x5c11d795"]}, {"calls": ["0x454b0608", "0xd0e30db0"], "exploit_name": "Gym_1_exp", "function_sigs": ["0x454b0608", "0xd0e30db0", "0xaf2979eb", "0xe8e33700", "0x022c0d9f", "0xa9059cbb", "0x5c11d795"]}, {"calls": ["0x81bac14f", "0x81bac14f", "0x81bac14f", "0x81bac14f", "0x81bac14f", "0x81bac14f", "0x81bac14f", "0x81bac14f", "0x81bac14f", "0x81bac14f", "0x81bac14f", "0x81bac14f", "0x81bac14f", "0x81bac14f", "0x81bac14f", "0x81bac14f", "0x81bac14f", "0x81bac14f", "0x81bac14f", "0x81bac14f", "0x81bac14f", "0x81bac14f", "0xf7e9e813", "0xf7e9e813", "0x51cff8d9", "0xf7e9e813", "0x51cff8d9", "0xf7e9e813", "0x51cff8d9", "0xf7e9e813", "0x51cff8d9", "0xf7e9e813", "0x51cff8d9", "0xf7e9e813", "0x51cff8d9", "0xf7e9e813", "0x51cff8d9", "0xf7e9e813", "0x51cff8d9", "0xf7e9e813", "0x51cff8d9", "0xf7e9e813", "0x51cff8d9", "0xf7e9e813", "0x51cff8d9", "0xf7e9e813", "0x51cff8d9", "0xf7e9e813", "0x51cff8d9", "0xf7e9e813", "0x51cff8d9", "0xf7e9e813", "0x51cff8d9", "0xf7e9e813", "0x51cff8d9", "0x51cff8d9", "0x51cff8d9", "0x51cff8d9", "0x51cff8d9", "0x51cff8d9"], "exploit_name": "UEarnPool_exp", "function_sigs": ["0x51cff8d9", "0x81bac14f", "0xf7e9e813", "0x022c0d9f", "0xa9059cbb"]}, {"calls": ["0x2e1a7d4d", "0x1b45d17b", "0xa4b910fb", "0x2e1a7d4d", "0x1b45d17b", "0xa4b910fb", "0x2e1a7d4d", "0x1b45d17b", "0xa4b910fb", "0x2e1a7d4d", "0x1b45d17b", "0xa4b910fb"], "exploit_name": "poolz_exp", "function_sigs": ["0x095ea7b3", "0x7ff36ab5", "0xa4b910fb", "0x1b45d17b", "0x2e1a7d4d", "0xa9059cbb", "0x5c11d795"]}, {"calls": ["0x6115880a", "0xca669fa7", "0xca669fa7", "0x23b872dd", "0x23b872dd"], "exploit_name": "RES_exp", "function_sigs": ["0xca669fa7", "0x095ea7b3", "0x022c0d9f", "0xa9059cbb", "0x6115880a", "0x23b872dd"]}, {"calls": ["0x45b56078", "0xed88c68e", "0x2118c75c"], "exploit_name": "RariCapital_exp", "function_sigs": ["0xed88c68e", "0x2118c75c", "0x45b56078"]}, {"calls": ["0x2e2d2984"], "exploit_name": "Visor_exp", "function_sigs": ["0x2e2d2984"]}, {"calls": ["0xc11c2e92", "0x2e1a7d4d"], "exploit_name": "BDEX_exp", "function_sigs": ["0xc11c2e92", "0xa9059cbb", "0x2e1a7d4d", "0x022c0d9f"]}, {"calls": ["0x9dc29fac", "0xfff6cae9"], "exploit_name": "Shadowfi_exp", "function_sigs": ["0x9dc29fac", "0x5c11d795", "0x095ea7b3", "0xfff6cae9"]}, {"calls": ["0x1f7b4f30", "0xab9c4b5d", "0xab9c4b5d", "0xa0712d68", "0xc2998238", "0xc5ebeaec", "0x4d49e87d", "0xf2fad2b6", "0x84cdd9bc", "0xf2fad2b6", "0x84cdd9bc", "0xf2fad2b6", "0x84cdd9bc", "0xf2fad2b6", "0x84cdd9bc", "0xf2fad2b6", "0x84cdd9bc", "0xf2fad2b6", "0x84cdd9bc", "0xf2fad2b6", "0x84cdd9bc", "0xf2fad2b6", "0x84cdd9bc", "0xf2fad2b6", "0x84cdd9bc", "0xf2fad2b6", "0x84cdd9bc", "0x91695586", "0x91695586", "0x91695586", "0xcce7ec13", "0x91695586", "0x91695586", "0x91695586", "0x0e752702", "0x852a12e3", "0x9908fc8b", "0x1f7b4f30", "0x1e9a6950", "0x1f7b4f30", "0xab9c4b5d", "0xab9c4b5d", "0xa0712d68", "0xc2998238", "0xc5ebeaec", "0x4d49e87d", "0xf2fad2b6", "0x84cdd9bc", "0xf2fad2b6", "0x84cdd9bc", "0xf2fad2b6", "0x84cdd9bc", "0xf2fad2b6", "0x84cdd9bc", "0xf2fad2b6", "0x84cdd9bc", "0xf2fad2b6", "0x84cdd9bc", "0xf2fad2b6", "0x84cdd9bc", "0xf2fad2b6", "0x84cdd9bc", "0xf2fad2b6", "0x84cdd9bc", "0xf2fad2b6", "0x84cdd9bc", "0x91695586", "0x91695586", "0x91695586", "0xcce7ec13", "0x91695586", "0x91695586", "0x91695586", "0x0e752702", "0x852a12e3", "0x9908fc8b", "0x1f7b4f30", "0x1e9a6950", "0x1f7b4f30", "0xab9c4b5d", "0xab9c4b5d", "0xa0712d68", "0xc2998238", "0xc5ebeaec", "0x4d49e87d", "0xf2fad2b6", "0x84cdd9bc", "0xf2fad2b6", "0x84cdd9bc", "0xf2fad2b6", "0x84cdd9bc", "0xf2fad2b6", "0x84cdd9bc", "0xf2fad2b6", "0x84cdd9bc", "0xf2fad2b6", "0x84cdd9bc", "0xf2fad2b6", "0x84cdd9bc", "0xf2fad2b6", "0x84cdd9bc", "0xf2fad2b6", "0x84cdd9bc", "0xf2fad2b6", "0x84cdd9bc", "0x91695586", "0x91695586", "0x91695586", "0xcce7ec13", "0x91695586", "0x91695586", "0x91695586", "0x0e752702", "0x852a12e3", "0x9908fc8b", "0x1f7b4f30", "0x1e9a6950", "0x1f7b4f30", "0xab9c4b5d", "0xab9c4b5d", "0xa0712d68", "0xc2998238", "0xc5ebeaec", "0x4d49e87d", "0xf2fad2b6", "0x84cdd9bc", "0xf2fad2b6", "0x84cdd9bc", "0xf2fad2b6", "0x84cdd9bc", "0xf2fad2b6", "0x84cdd9bc", "0xf2fad2b6", "0x84cdd9bc", "0xf2fad2b6", "0x84cdd9bc", "0xf2fad2b6", "0x84cdd9bc", "0xf2fad2b6", "0x84cdd9bc", "0xf2fad2b6", "0x84cdd9bc", "0xf2fad2b6", "0x84cdd9bc", "0x91695586", "0x91695586", "0x91695586", "0xcce7ec13", "0x91695586", "0x91695586", "0x91695586", "0x0e752702", "0x852a12e3", "0x9908fc8b", "0x1f7b4f30", "0x1e9a6950", "0x1f7b4f30", "0xab9c4b5d", "0xab9c4b5d", "0xa0712d68", "0xc2998238", "0xc5ebeaec", "0x4d49e87d", "0xf2fad2b6", "0x84cdd9bc", "0xf2fad2b6", "0x84cdd9bc", "0xf2fad2b6", "0x84cdd9bc", "0xf2fad2b6", "0x84cdd9bc", "0xf2fad2b6", "0x84cdd9bc", "0xf2fad2b6", "0x84cdd9bc", "0xf2fad2b6", "0x84cdd9bc", "0xf2fad2b6", "0x84cdd9bc", "0xf2fad2b6", "0x84cdd9bc", "0xf2fad2b6", "0x84cdd9bc", "0x91695586", "0x91695586", "0x91695586", "0xcce7ec13", "0x91695586", "0x91695586", "0x91695586", "0x0e752702", "0x852a12e3", "0x9908fc8b", "0x1f7b4f30", "0x1e9a6950", "0x1f7b4f30", "0xab9c4b5d", "0xab9c4b5d", "0xa0712d68", "0xc2998238", "0xc5ebeaec", "0x4d49e87d", "0xf2fad2b6", "0x84cdd9bc", "0xf2fad2b6", "0x84cdd9bc", "0xf2fad2b6", "0x84cdd9bc", "0xf2fad2b6", "0x84cdd9bc", "0xf2fad2b6", "0x84cdd9bc", "0xf2fad2b6", "0x84cdd9bc", "0xf2fad2b6", "0x84cdd9bc", "0xf2fad2b6", "0x84cdd9bc", "0xf2fad2b6", "0x84cdd9bc", "0xf2fad2b6", "0x84cdd9bc", "0x91695586", "0x91695586", "0x91695586", "0xcce7ec13", "0x91695586", "0x91695586", "0x91695586", "0x0e752702", "0x852a12e3", "0x9908fc8b", "0x1f7b4f30", "0x1e9a6950"], "exploit_name": "Overnight_exp", "function_sigs": ["0xc5ebeaec", "0x095ea7b3", "0xab9c4b5d", "0x1e9a6950", "0xa0712d68", "0xf2fad2b6", "0x84cdd9bc", "0x91695586", "0xc2998238", "0x38ed1739", "0x8a657e67", "0x9908fc8b", "0x1f7b4f30", "0x852a12e3", "0xcce7ec13", "0x0e752702", "0x4d49e87d"]}, {"calls": ["0x5039972a"], "exploit_name": "dodo_flashloan_exp", "function_sigs": ["0x5039972a", "0xa9059cbb"]}, {"calls": ["0x5c38449e", "0xa6afed95", "0xa0712d68", "0xc2998238", "0xc5ebeaec", "0xede4edd0", "0x852a12e3"], "exploit_name": "Rari_exp", "function_sigs": ["0xc5ebeaec", "0x095ea7b3", "0xa0712d68", "0xc2998238", "0xede4edd0", "0xa6afed95", "0x5c38449e", "0xa9059cbb", "0x852a12e3"]}, {"calls": ["0x1c86323b"], "exploit_name": "Auctus_exp", "function_sigs": ["0x1c86323b"]}, {"calls": ["0xc657c718", "0xc657c718", "0xc657c718", "0xc657c718", "0xc657c718", "0x4641257d"], "exploit_name": "ThoreumFinance_exp", "function_sigs": ["0x4641257d", "0xc657c718"]}, {"calls": ["0x2e1a7d4d", "0xa0712d68", "0xdb006a75"], "exploit_name": "Elephant_Money_poc", "function_sigs": ["0xa0712d68", "0xb6f9de95", "0xdb006a75", "0x022c0d9f", "0x2e1a7d4d", "0xa9059cbb", "0x5c11d795"]}, {"calls": ["0xc657c718", "0xc657c718", "0xc657c718", "0x70a08231", "0x266cf109", "0x65bc9481", "0x70ca10bb", "0xbc25cf77", "0xbc25cf77", "0xbc25cf77", "0xbc25cf77", "0xbc25cf77", "0xbc25cf77", "0xbc25cf77", "0xbc25cf77"], "exploit_name": "Upswing_exp", "function_sigs": ["0x266cf109", "0x095ea7b3", "0x65bc9481", "0xc657c718", "0x70a08231", "0x38ed1739", "0xa9059cbb", "0x70ca10bb", "0xbc25cf77"]}, {"calls": ["0x7ae06e58", "0xbb1e66ff", "0xa7ab8335"], "exploit_name": "BurgerSwap_exp", "function_sigs": ["0xbb1e66ff", "0xa7ab8335", "0x095ea7b3", "0x8803dbee", "0x38ed1739", "0x7ae06e58", "0x022c0d9f", "0xa9059cbb"]}, {"calls": ["0xd0e30db0", "0x5dd44a6d", "0x00c24087", "0x00c24087", "0x00c24087", "0x1b879378", "0x5dd44a6d", "0x5dd44a6d", "0x5dd44a6d", "0x5dd44a6d", "0x5dd44a6d", "0x5dd44a6d", "0x5dd44a6d", "0x5dd44a6d", "0x5dd44a6d", "0x5dd44a6d", "0x5dd44a6d", "0x5dd44a6d", "0x5dd44a6d", "0x5dd44a6d", "0x5dd44a6d", "0x5dd44a6d", "0x5dd44a6d", "0x5dd44a6d", "0x5dd44a6d", "0x5dd44a6d", "0x5dd44a6d", "0x5dd44a6d", "0x5dd44a6d", "0x5dd44a6d", "0x5dd44a6d", "0x5dd44a6d", "0x5dd44a6d", "0x5dd44a6d", "0x5dd44a6d", "0x5dd44a6d", "0x5dd44a6d", "0x5dd44a6d", "0x5dd44a6d", "0x5dd44a6d", "0x5dd44a6d", "0x5dd44a6d", "0x5dd44a6d", "0x5dd44a6d", "0x5dd44a6d", "0x5dd44a6d", "0x5dd44a6d", "0x5dd44a6d", "0x5dd44a6d", "0x5dd44a6d", "0x5dd44a6d", "0x5dd44a6d", "0x5dd44a6d", "0x5dd44a6d", "0x5dd44a6d", "0x5dd44a6d", "0x5dd44a6d", "0x5dd44a6d", "0x5dd44a6d", "0x5dd44a6d", "0x5dd44a6d", "0x138d03cd"], "exploit_name": "Mono_exp", "function_sigs": ["0x1b879378", "0x095ea7b3", "0x00c24087", "0x5dd44a6d", "0x138d03cd", "0xd0e30db0"]}, {"calls": ["0xd2423b51"], "exploit_name": "Sushimiso_exp", "function_sigs": ["0xd2423b51"]}, {"calls": [], "exploit_name": "<PERSON><PERSON>-<PERSON><PERSON>_Digg", "function_sigs": []}, {"calls": ["0x0b6b2d42", "0x0b6b2d42", "0x0b6b2d42", "0x0b6b2d42", "0x0b6b2d42", "0x0b6b2d42", "0x0b6b2d42", "0x0b6b2d42", "0x060203eb", "0x060203eb", "0x060203eb", "0x060203eb", "0x060203eb", "0x060203eb", "0x060203eb", "0x060203eb", "0x060203eb", "0x060203eb", "0x060203eb", "0x060203eb", "0x060203eb", "0x060203eb", "0x060203eb", "0x060203eb", "0x060203eb", "0x060203eb"], "exploit_name": "Rubic_exp", "function_sigs": ["0x0b6b2d42", "0x060203eb"]}, {"calls": ["0xe5d6bf02", "0x43aa2a9a", "0x43aa2a9a", "0x43aa2a9a", "0x43aa2a9a", "0x43aa2a9a", "0x43aa2a9a", "0x43aa2a9a", "0x43aa2a9a", "0x43aa2a9a", "0x43aa2a9a", "0x43aa2a9a", "0x43aa2a9a", "0x43aa2a9a", "0x43aa2a9a", "0x43aa2a9a", "0x43aa2a9a", "0x43aa2a9a", "0x43aa2a9a", "0x43aa2a9a", "0x43aa2a9a", "0x43aa2a9a", "0x43aa2a9a", "0x43aa2a9a", "0x43aa2a9a", "0x43aa2a9a", "0x43aa2a9a", "0x43aa2a9a", "0x43aa2a9a", "0x43aa2a9a", "0x43aa2a9a", "0x43aa2a9a", "0x43aa2a9a", "0x43aa2a9a", "0x43aa2a9a", "0x43aa2a9a", "0x43aa2a9a", "0x43aa2a9a", "0x43aa2a9a", "0x43aa2a9a", "0x43aa2a9a", "0x43aa2a9a", "0x43aa2a9a", "0x43aa2a9a", "0x43aa2a9a", "0x43aa2a9a", "0x43aa2a9a", "0x43aa2a9a", "0x43aa2a9a", "0x43aa2a9a", "0x43aa2a9a", "0x43aa2a9a", "0x43aa2a9a", "0x43aa2a9a", "0x43aa2a9a", "0x43aa2a9a", "0x43aa2a9a", "0x43aa2a9a", "0x43aa2a9a", "0x43aa2a9a", "0x43aa2a9a", "0x43aa2a9a", "0x43aa2a9a", "0x43aa2a9a", "0x43aa2a9a", "0x43aa2a9a", "0x43aa2a9a", "0x43aa2a9a", "0x43aa2a9a", "0x43aa2a9a", "0x43aa2a9a", "0x43aa2a9a", "0x43aa2a9a", "0x43aa2a9a", "0x43aa2a9a", "0x43aa2a9a", "0x43aa2a9a", "0x43aa2a9a", "0x43aa2a9a", "0x43aa2a9a", "0x43aa2a9a", "0x43aa2a9a", "0x43aa2a9a", "0x43aa2a9a", "0x43aa2a9a", "0x43aa2a9a", "0x43aa2a9a", "0x43aa2a9a", "0x43aa2a9a", "0x43aa2a9a", "0x43aa2a9a", "0x43aa2a9a", "0x43aa2a9a", "0x43aa2a9a", "0x43aa2a9a", "0x43aa2a9a", "0x43aa2a9a", "0x43aa2a9a", "0x43aa2a9a", "0x43aa2a9a", "0x43aa2a9a"], "exploit_name": "RL_exp", "function_sigs": ["0xe5d6bf02", "0x095ea7b3", "0xbaa2abde", "0xe8e33700", "0x43aa2a9a", "0xa9059cbb", "0x5c11d795"]}, {"calls": ["0xc657c718", "0xc657c718", "0x90d52832", "0x90d52832"], "exploit_name": "RevertFinance_exp", "function_sigs": ["0x90d52832", "0xc657c718"]}, {"calls": ["0xc657c718", "0xc657c718", "0xc657c718", "0xc657c718", "0xc657c718", "0x70a08231", "0x266cf109", "0x65bc9481", "0x70ca10bb"], "exploit_name": "DKP_exp", "function_sigs": ["0x266cf109", "0x095ea7b3", "0x65bc9481", "0xc657c718", "0x70a08231", "0xa9059cbb", "0x022c0d9f", "0x70ca10bb", "0x5c11d795"]}, {"calls": ["0xc657c718", "0xc657c718", "0xc657c718", "0xc657c718", "0xc657c718", "0x55c66ac1", "0x7476f748", "0x9fa0bc94", "0x485cc955", "0x1794bb3c", "0x201ae9db", "0x3c323a1b", "0x1f7b4f30", "0x99653fbe", "0x1f7b4f30", "0x7476f748"], "exploit_name": "<PERSON><PERSON>", "function_sigs": ["0x99653fbe", "0x7476f748", "0x095ea7b3", "0x1794bb3c", "0xc657c718", "0x3c323a1b", "0x485cc955", "0x201ae9db", "0x9fa0bc94", "0x1f7b4f30", "0x55c66ac1", "0x18cbafe5"]}, {"calls": ["0xf57ebc00"], "exploit_name": "BBOX_exp", "function_sigs": ["0xa9059cbb", "0xf57ebc00", "0x5c11d795", "0x095ea7b3"]}, {"calls": ["0x6c648fca"], "exploit_name": "Chainswap_exp1", "function_sigs": ["0x6c648fca"]}, {"calls": ["0xa0712d68", "0xc5ebeaec", "0xc5ebeaec", "0xd0e30db0", "0x3df02124"], "exploit_name": "HundredFinance_exp", "function_sigs": ["0xc5ebeaec", "0x095ea7b3", "0xa0712d68", "0x3df02124", "0xd0e30db0", "0x022c0d9f", "0xa9059cbb"]}, {"calls": ["0x1694505e", "0xd9bbf3a1", "0xd9bbf3a1", "0x32e6cd41", "0x40c10f19", "0x1694505e", "0x1694505e", "0x49bd5a5e", "0x49bd5a5e", "0x9dc29fac", "0x9dc29fac", "0x49bd5a5e", "0xfff6cae9", "0x1694505e", "0x1694505e"], "exploit_name": "safeMoon_exp", "function_sigs": ["0x9dc29fac", "0x095ea7b3", "0x1694505e", "0xd9bbf3a1", "0x49bd5a5e", "0x022c0d9f", "0x32e6cd41", "0xfff6cae9", "0xa9059cbb", "0x40c10f19", "0x9166aecd"]}, {"calls": ["0x3bd5d173"], "exploit_name": "FDP_exp", "function_sigs": ["0x095ea7b3", "0x3bd5d173", "0x022c0d9f", "0xa9059cbb", "0x5c11d795"]}, {"calls": ["0xb2b45df5", "0x42966c68", "0xf28dceb3", "0x40c10f19"], "exploit_name": "88mph_exp", "function_sigs": ["0x42966c68", "0x40c10f19", "0xf28dceb3", "0xb2b45df5"]}, {"calls": [], "exploit_name": "NowSwap_exp", "function_sigs": ["0xa9059cbb", "0x022c0d9f"]}, {"calls": ["0x06447d56", "0x993e1c42", "0x993e1c42"], "exploit_name": "Ronin_exp", "function_sigs": ["0x993e1c42", "0x06447d56"]}, {"calls": ["0xc657c718", "0xc657c718", "0xc657c718", "0xc657c718", "0xe5d6bf02", "0xbc25cf77", "0xbc25cf77", "0xbc25cf77", "0xbc25cf77", "0xbc25cf77", "0xbc25cf77", "0xbc25cf77", "0xbc25cf77", "0xbc25cf77", "0xbc25cf77", "0xbc25cf77", "0xbc25cf77", "0xbc25cf77", "0xbc25cf77", "0xbc25cf77", "0xbc25cf77", "0xbc25cf77", "0xbc25cf77", "0xbc25cf77", "0xbc25cf77", "0xbc25cf77", "0xbc25cf77", "0xbc25cf77", "0xbc25cf77", "0xbc25cf77", "0xbc25cf77", "0xbc25cf77", "0xbc25cf77", "0xbc25cf77", "0xbc25cf77", "0xbc25cf77", "0xbc25cf77", "0xbc25cf77", "0xbc25cf77", "0xbc25cf77", "0xbc25cf77", "0xbc25cf77", "0xbc25cf77", "0xbc25cf77", "0xbc25cf77", "0xe5d6bf02", "0x1d7876e7", "0x1d7876e7", "0x1d7876e7", "0x1d7876e7", "0x1d7876e7", "0x1d7876e7", "0x1d7876e7", "0x1d7876e7", "0x1d7876e7", "0x1d7876e7", "0x1d7876e7", "0x1d7876e7", "0x1d7876e7", "0x1d7876e7", "0x1d7876e7", "0x1d7876e7", "0x1d7876e7", "0x1d7876e7", "0x1d7876e7", "0x1d7876e7", "0x1d7876e7", "0x1d7876e7", "0x1d7876e7", "0x1d7876e7", "0x1d7876e7", "0x1d7876e7", "0x1d7876e7", "0x1d7876e7", "0x1d7876e7", "0x1d7876e7", "0x1d7876e7", "0x1d7876e7", "0x1d7876e7", "0x1d7876e7", "0x1d7876e7", "0x1d7876e7", "0x1d7876e7", "0x1d7876e7", "0x1d7876e7", "0x1d7876e7"], "exploit_name": "QTN_exp", "function_sigs": ["0xe5d6bf02", "0x095ea7b3", "0x1d7876e7", "0xc657c718", "0xa9059cbb", "0x5c11d795", "0xbc25cf77"]}, {"calls": ["0x5cffe9de", "0x029b2f34", "0xb6b55f25", "0xa0712d68", "0xc2998238", "0xe770d97c", "0xa6417ed6", "0xe7ea8cce", "0x2e1a7d4d", "0xc5ebeaec", "0xc5ebeaec", "0xc5ebeaec", "0xc5ebeaec", "0xc5ebeaec", "0xc5ebeaec", "0xc5ebeaec", "0xc5ebeaec", "0xc5ebeaec", "0xc5ebeaec", "0xc5ebeaec", "0xc5ebeaec", "0xc5ebeaec", "0xc5ebeaec", "0xc5ebeaec", "0x18a7bd76", "0x2e1a7d4d"], "exploit_name": "Cream_2_exp", "function_sigs": ["0x18a7bd76", "0xc5ebeaec", "0xdb3e2198", "0x095ea7b3", "0xa0712d68", "0x029b2f34", "0xc2998238", "0xe7ea8cce", "0x2e1a7d4d", "0xe770d97c", "0xb6b55f25", "0x5cffe9de", "0xa9059cbb", "0xa6417ed6"]}, {"calls": ["0xc657c718", "0xc657c718", "0xc657c718", "0xc657c718", "0xc657c718", "0xc657c718", "0xc657c718", "0xc657c718", "0xc657c718", "0xc657c718", "0xc657c718", "0xc657c718", "0x42b0b77c", "0x90d25074", "0xe2bbb158", "0x4b8a3529", "0x5312ea8e", "0x09a5fca3", "0x9908fc8b", "0x9908fc8b", "0x9908fc8b", "0x9908fc8b", "0x9908fc8b", "0x9908fc8b"], "exploit_name": "Platypus_exp", "function_sigs": ["0x095ea7b3", "0xc657c718", "0x09a5fca3", "0x4b8a3529", "0xe2bbb158", "0x90d25074", "0x5312ea8e", "0x42b0b77c", "0x9908fc8b"]}, {"calls": ["0xad594920", "0xfff6cae9"], "exploit_name": "MBC_exp", "function_sigs": ["0x095ea7b3", "0x022c0d9f", "0xfff6cae9", "0xa9059cbb", "0xad594920"]}, {"calls": ["0x06447d56", "0x5ca7c8a6", "0x58b36dac", "0xfaa2041f"], "exploit_name": "<PERSON><PERSON>", "function_sigs": ["0xfaa2041f", "0x5ca7c8a6", "0x06447d56", "0x58b36dac"]}, {"calls": ["0xc657c718", "0xc657c718", "0xc657c718", "0xc657c718", "0x430bf08a", "0xca669fa7", "0x40c10f19", "0xb4d6c782", "0xca669fa7", "0x70a08231"], "exploit_name": "USDs_exp", "function_sigs": ["0x430bf08a", "0xc657c718", "0x70a08231", "0x40c10f19", "0xb4d6c782", "0xa9059cbb", "0xca669fa7"]}, {"calls": ["0xc657c718", "0xc657c718", "0xc657c718", "0xc657c718", "0xc657c718", "0xc657c718", "0xc657c718", "0x70a08231", "0x266cf109", "0x65bc9481", "0x70ca10bb", "0x70ca10bb", "0x70ca10bb", "0x70ca10bb", "0x70ca10bb", "0x70a08231", "0x266cf109", "0x65bc9481", "0x70ca10bb", "0x70ca10bb", "0x70ca10bb", "0x70ca10bb", "0x70ca10bb", "0x70ca10bb", "0x70ca10bb", "0xc9c65396", "0xc9c65396", "0x6a627842", "0x6a627842", "0x88a0ec62", "0xdf0040c8", "0x88a0ec62", "0x8293e9af"], "exploit_name": "Orion_exp", "function_sigs": ["0x414bf389", "0x266cf109", "0x095ea7b3", "0x65bc9481", "0xc657c718", "0x8293e9af", "0x70a08231", "0xdf0040c8", "0xc9c65396", "0x88a0ec62", "0x6a627842", "0xa9059cbb", "0x022c0d9f", "0x70ca10bb"]}, {"calls": ["0xca669fa7", "0xd9fc4b61"], "exploit_name": "Bancor_exp", "function_sigs": ["0xca669fa7", "0xd9fc4b61"]}, {"calls": [], "exploit_name": "MEV_0ad8", "function_sigs": []}, {"calls": ["0xbc25cf77", "0xbc25cf77", "0xbc25cf77", "0xbc25cf77", "0xbc25cf77", "0xbc25cf77", "0xbc25cf77", "0xbc25cf77", "0xbc25cf77", "0xbc25cf77", "0xbc25cf77", "0xbc25cf77", "0xbc25cf77", "0xbc25cf77", "0xbc25cf77", "0xbc25cf77", "0xbc25cf77", "0xbc25cf77", "0xbc25cf77", "0xbc25cf77", "0xbc25cf77", "0xbc25cf77", "0xbc25cf77", "0xbc25cf77", "0xbc25cf77", "0xbc25cf77", "0xbc25cf77", "0xbc25cf77", "0xbc25cf77", "0xbc25cf77", "0xbc25cf77", "0xbc25cf77", "0xbc25cf77", "0xbc25cf77", "0xbc25cf77", "0xbc25cf77", "0xbc25cf77", "0xbc25cf77", "0x26c4e60d", "0xfff6cae9"], "exploit_name": "AES_exp", "function_sigs": ["0x095ea7b3", "0x26c4e60d", "0xfff6cae9", "0xa9059cbb", "0x5c11d795", "0xbc25cf77"]}, {"calls": ["0xd9bbf3a1", "0x06447d56", "0x90c5013b", "0xc657c718", "0x06447d56", "0xd55f2213"], "exploit_name": "FortressLoans", "function_sigs": ["0xc657c718", "0x90c5013b", "0xd9bbf3a1", "0xd55f2213", "0x06447d56"]}, {"calls": ["0x5cffe9de", "0x3df02124", "0x91695586", "0x91695586", "0x3df02124"], "exploit_name": "Saddle_exp", "function_sigs": ["0x5cffe9de", "0x3df02124", "0x095ea7b3", "0x91695586"]}, {"calls": ["0x06447d56", "0x35d0d5dc", "0x4c999f5e", "0x05e2ca17"], "exploit_name": "Qubit_exp", "function_sigs": ["0x35d0d5dc", "0x05e2ca17", "0x06447d56", "0x4c999f5e"]}, {"calls": ["0x70a08231", "0x70a08231", "0x266cf109", "0x266cf109", "0x65bc9481", "0x65bc9481", "0x70ca10bb", "0x70ca10bb", "0x70ca10bb", "0x70ca10bb", "0x70ca10bb", "0x70ca10bb", "0x70ca10bb", "0x70ca10bb", "0x70ca10bb", "0x70ca10bb", "0x70a08231", "0x70a08231", "0x266cf109", "0x266cf109", "0x65bc9481", "0x65bc9481", "0x70ca10bb", "0x70ca10bb", "0xd29ea1bf", "0xd29ea1bf", "0x70a08231", "0x266cf109", "0x65bc9481", "0x70ca10bb", "0x70ca10bb", "0x70ca10bb", "0x70ca10bb", "0x70ca10bb", "0x70a08231", "0x266cf109", "0x65bc9481", "0x70ca10bb", "0x70a08231", "0x266cf109", "0x65bc9481", "0x70ca10bb", "0x29a0a958", "0x1f7b4f30", "0xe5d6bf02", "0x29a0a958"], "exploit_name": "BonqDAO_exp", "function_sigs": ["0x266cf109", "0xe5d6bf02", "0x65bc9481", "0x29a0a958", "0xd29ea1bf", "0x70a08231", "0x1f7b4f30", "0x70ca10bb"]}, {"calls": ["0xbc25cf77", "0xfff6cae9", "0xbc25cf77"], "exploit_name": "HackDao_exp", "function_sigs": ["0x095ea7b3", "0xfff6cae9", "0x022c0d9f", "0xa9059cbb", "0x5c11d795", "0xbc25cf77"]}, {"calls": ["0xc657c718", "0xc657c718", "0xc657c718", "0xbb7bf89f", "0x23b872dd"], "exploit_name": "Carrot_exp", "function_sigs": ["0xbb7bf89f", "0xc657c718", "0x23b872dd"]}, {"calls": ["0xca669fa7", "0xfff4a386", "0xca669fa7", "0xca669fa7", "0x23b872dd", "0xca669fa7", "0x23b872dd"], "exploit_name": "RedactedCartel_exp", "function_sigs": ["0xca669fa7", "0x095ea7b3", "0xfff4a386", "0x23b872dd"]}, {"calls": ["0xc657c718", "0xc657c718", "0xc657c718", "0x1e9a6950"], "exploit_name": "OlympusDao", "function_sigs": ["0x1e9a6950", "0xc657c718"]}, {"calls": ["0xab9c4b5d", "0xc0a47c93", "0x02ce728f", "0x656f3d64", "0x7981c43e", "0x3df02124"], "exploit_name": "NXUSD_exp", "function_sigs": ["0x095ea7b3", "0xab9c4b5d", "0x38ed1739", "0x3df02124", "0x656f3d64", "0xc0a47c93", "0xe8e33700", "0x02ce728f", "0x7981c43e"]}, {"calls": ["0xc657c718", "0xc657c718", "0xc657c718", "0xc657c718", "0xc657c718", "0xc657c718", "0xc657c718", "0xc657c718"], "exploit_name": "Thena_exp", "function_sigs": ["0xc657c718"]}, {"calls": ["0x06447d56", "0xde5f6268", "0xd8d7f96f", "0x853828b6"], "exploit_name": "Eleven", "function_sigs": ["0x095ea7b3", "0xd8d7f96f", "0x38ed1739", "0x853828b6", "0x06447d56", "0xe8e33700", "0x022c0d9f", "0xbaa2abde", "0xa9059cbb", "0xde5f6268"]}, {"calls": ["0xbc25cf77", "0xfff6cae9", "0xbc25cf77", "0xfff6cae9", "0xbc25cf77", "0xfff6cae9", "0xbc25cf77", "0xfff6cae9", "0xbc25cf77", "0xfff6cae9", "0xbc25cf77", "0xfff6cae9", "0xbc25cf77", "0xfff6cae9", "0xbc25cf77", "0xfff6cae9", "0xbc25cf77", "0xfff6cae9", "0xbc25cf77", "0xfff6cae9", "0xbc25cf77", "0xfff6cae9", "0xbc25cf77", "0xfff6cae9", "0xbc25cf77", "0xfff6cae9", "0xbc25cf77", "0xfff6cae9", "0xbc25cf77", "0xfff6cae9", "0xbc25cf77", "0xfff6cae9", "0xbc25cf77", "0xfff6cae9", "0xbc25cf77", "0xfff6cae9", "0xbc25cf77", "0xfff6cae9", "0xbc25cf77", "0xfff6cae9"], "exploit_name": "Starlink_exp", "function_sigs": ["0x095ea7b3", "0xfff6cae9", "0x022c0d9f", "0xa9059cbb", "0x5c11d795", "0xbc25cf77"]}, {"calls": ["0x1be19560"], "exploit_name": "CompoundTusd_exp", "function_sigs": ["0x1be19560"]}, {"calls": ["0x84304ad7", "0xa441d067"], "exploit_name": "DaoMaker_exp", "function_sigs": ["0x84304ad7", "0xa441d067"]}, {"calls": ["0xb872dd0e"], "exploit_name": "BabySwap_exp", "function_sigs": ["0xb872dd0e", "0x095ea7b3", "0xb79159ab"]}, {"calls": ["0x57eba63c", "0xad594920"], "exploit_name": "SpaceGodzilla", "function_sigs": ["0xa9059cbb", "0xad594920", "0x57eba63c", "0x022c0d9f"]}, {"calls": ["0xc657c718", "0xc657c718", "0xc657c718", "0xc657c718", "0x8a43bb01", "0x8a43bb01", "0x8a43bb01", "0x8a43bb01", "0x8a43bb01", "0x8a43bb01", "0x8a43bb01", "0x8a43bb01", "0x8a43bb01", "0x8a43bb01", "0x8a43bb01", "0x8a43bb01", "0x8a43bb01", "0x8a43bb01", "0x8a43bb01", "0x8a43bb01", "0x8a43bb01", "0x8a43bb01", "0x8a43bb01", "0x8a43bb01", "0x8a43bb01", "0x8a43bb01", "0x8a43bb01", "0x8a43bb01", "0x8a43bb01", "0x8a43bb01", "0x8a43bb01", "0x8a43bb01", "0x8a43bb01", "0x8a43bb01", "0x8a43bb01", "0x8a43bb01", "0x8a43bb01", "0x8a43bb01", "0x8a43bb01", "0x8a43bb01", "0x8a43bb01", "0x8a43bb01", "0x8a43bb01", "0x8a43bb01", "0x8a43bb01", "0x8a43bb01", "0x8a43bb01", "0x8a43bb01", "0x8a43bb01", "0x8a43bb01", "0x8a43bb01", "0x8a43bb01", "0x8a43bb01", "0x8a43bb01", "0x8a43bb01", "0x8a43bb01", "0x8a43bb01", "0x8a43bb01", "0x8a43bb01", "0x8a43bb01", "0x8a43bb01", "0x8a43bb01", "0x8a43bb01", "0x8a43bb01", "0x8a43bb01", "0x8a43bb01", "0x8a43bb01", "0x8a43bb01", "0x8a43bb01", "0x8a43bb01", "0x8a43bb01", "0x8a43bb01", "0x8a43bb01", "0x8a43bb01", "0x8a43bb01", "0x8a43bb01", "0x8a43bb01", "0x8a43bb01", "0x8a43bb01", "0x8a43bb01", "0x8a43bb01", "0x8a43bb01", "0x8a43bb01", "0x8a43bb01", "0x8a43bb01", "0x8a43bb01", "0x8a43bb01", "0x8a43bb01", "0x8a43bb01", "0x8a43bb01", "0x8a43bb01", "0x8a43bb01", "0x8a43bb01", "0x8a43bb01", "0x8a43bb01", "0x8a43bb01", "0x8a43bb01", "0x8a43bb01", "0x8a43bb01", "0x8a43bb01", "0x8a43bb01", "0x8a43bb01", "0x8a43bb01", "0x8a43bb01"], "exploit_name": "ULME", "function_sigs": ["0x095ea7b3", "0xc657c718", "0x8a43bb01", "0xa9059cbb", "0x5c11d795"]}, {"calls": ["0xca669fa7", "0x40c10f19"], "exploit_name": "PAID_exp", "function_sigs": ["0xca669fa7", "0x40c10f19"]}, {"calls": ["0xbdcd9c80", "0x1c1c6fe5"], "exploit_name": "Templedao_exp", "function_sigs": ["0x1c1c6fe5", "0xbdcd9c80"]}, {"calls": ["0x5c38449e"], "exploit_name": "MEVa47b_exp", "function_sigs": ["0x5c38449e"]}, {"calls": ["0xca669fa7", "0x1b2ef1ca", "0x1f7b4f30", "0xe5225381"], "exploit_name": "Fantasm_exp", "function_sigs": ["0x095ea7b3", "0xe5225381", "0x1b2ef1ca", "0x1f7b4f30", "0xa9059cbb", "0xca669fa7"]}, {"calls": ["0xca669fa7", "0xec126c77", "0x2f943cb0", "0x47e7ef24", "0x6d75b9ee", "0xe5d6bf02", "0xc0bf02e5"], "exploit_name": "deus_exp", "function_sigs": ["0xe5d6bf02", "0x095ea7b3", "0x13dcfc59", "0xec126c77", "0x6d75b9ee", "0x5a47ddc3", "0x2f943cb0", "0xa9059cbb", "0xca669fa7", "0xc0bf02e5", "0x47e7ef24"]}, {"calls": ["0xca669fa7", "0x47e7ef24", "0xca669fa7", "0xef5cfb8c"], "exploit_name": "Cover_exp", "function_sigs": ["0xca669fa7", "0xef5cfb8c", "0x47e7ef24"]}, {"calls": ["0xc657c718", "0xc657c718", "0xc657c718", "0xc657c718", "0xc657c718", "0xab9c4b5d", "0xfab2c7f1", "0x86b9d81f"], "exploit_name": "Euler_exp", "function_sigs": ["0xab9c4b5d", "0x095ea7b3", "0xc657c718", "0x86b9d81f", "0xfab2c7f1", "0xa9059cbb"]}, {"calls": ["0x75ce258d", "0x956afd68", "0xe5d6bf02", "0xab9c4b5d", "0x4515cef3"], "exploit_name": "Beanstalk_exp", "function_sigs": ["0xe5d6bf02", "0x095ea7b3", "0x7ff36ab5", "0xab9c4b5d", "0x75ce258d", "0x4515cef3", "0x956afd68"]}, {"calls": ["0xe6dbd4d7", "0xe5d6bf02", "0xe2bbb158", "0x441a3e70", "0xe2bbb158", "0x441a3e70", "0xe2bbb158", "0x441a3e70", "0xe2bbb158", "0x441a3e70", "0xe2bbb158", "0x441a3e70", "0xe2bbb158", "0x441a3e70", "0xe2bbb158", "0x441a3e70", "0xe2bbb158", "0x441a3e70", "0xe2bbb158", "0x441a3e70", "0xe2bbb158", "0x441a3e70", "0xe2bbb158", "0x441a3e70", "0xe2bbb158", "0x441a3e70", "0xe2bbb158", "0x441a3e70", "0xe2bbb158", "0x441a3e70", "0xe2bbb158", "0x441a3e70", "0xe2bbb158", "0x441a3e70", "0xe2bbb158", "0x441a3e70", "0xe2bbb158", "0x441a3e70", "0xe2bbb158", "0x441a3e70", "0xe2bbb158", "0x441a3e70", "0xe2bbb158", "0x441a3e70", "0xe2bbb158", "0x441a3e70", "0xe2bbb158", "0x441a3e70", "0xe2bbb158", "0x441a3e70", "0xe2bbb158", "0x441a3e70", "0xe2bbb158", "0x441a3e70", "0xe2bbb158", "0x441a3e70", "0xe2bbb158", "0x441a3e70", "0xe2bbb158", "0x441a3e70", "0xe2bbb158", "0x441a3e70", "0xe2bbb158", "0x441a3e70", "0xe2bbb158", "0x441a3e70", "0xe2bbb158", "0x441a3e70", "0xe2bbb158", "0x441a3e70", "0xe2bbb158", "0x441a3e70", "0xe2bbb158", "0x441a3e70", "0xe2bbb158", "0x441a3e70", "0xe2bbb158", "0x441a3e70", "0xe2bbb158", "0x441a3e70", "0xe2bbb158", "0x441a3e70", "0xe2bbb158", "0x441a3e70", "0xe2bbb158", "0x441a3e70", "0xe2bbb158", "0x441a3e70", "0xe2bbb158", "0x441a3e70", "0xe2bbb158", "0x441a3e70", "0xe2bbb158", "0x441a3e70", "0xe2bbb158", "0x441a3e70", "0xe2bbb158", "0x441a3e70", "0xe2bbb158", "0x441a3e70", "0xe2bbb158", "0x441a3e70", "0xe2bbb158", "0x441a3e70", "0xe2bbb158", "0x441a3e70", "0xe2bbb158", "0x441a3e70", "0xe2bbb158", "0x441a3e70", "0xe2bbb158", "0x441a3e70", "0xe2bbb158", "0x441a3e70", "0xe2bbb158", "0x441a3e70", "0xe2bbb158", "0x441a3e70", "0xe2bbb158", "0x441a3e70", "0xe2bbb158", "0x441a3e70", "0xe2bbb158", "0x441a3e70", "0xe2bbb158", "0x441a3e70", "0xe2bbb158", "0x441a3e70", "0xe2bbb158", "0x441a3e70", "0xe2bbb158", "0x441a3e70", "0xe2bbb158", "0x441a3e70", "0xe2bbb158", "0x441a3e70", "0xe2bbb158", "0x441a3e70", "0xe2bbb158", "0x441a3e70", "0xe2bbb158", "0x441a3e70", "0xe2bbb158", "0x441a3e70", "0xe2bbb158", "0x441a3e70", "0xe2bbb158", "0x441a3e70", "0xe2bbb158", "0x441a3e70", "0xe2bbb158", "0x441a3e70", "0xe2bbb158", "0x441a3e70", "0xe2bbb158", "0x441a3e70", "0xe2bbb158", "0x441a3e70", "0xe2bbb158", "0x441a3e70", "0xe2bbb158", "0x441a3e70", "0xe2bbb158", "0x441a3e70", "0xe2bbb158", "0x441a3e70", "0xe2bbb158", "0x441a3e70", "0xe2bbb158", "0x441a3e70", "0xe2bbb158", "0x441a3e70", "0xe2bbb158", "0x441a3e70", "0xe2bbb158", "0x441a3e70", "0xe2bbb158", "0x441a3e70", "0xe2bbb158", "0x441a3e70", "0xe2bbb158", "0x441a3e70", "0xe2bbb158", "0x441a3e70", "0xe2bbb158", "0x441a3e70", "0xe2bbb158", "0x441a3e70", "0xe2bbb158", "0x441a3e70", "0xe2bbb158", "0x441a3e70", "0xe2bbb158", "0x441a3e70", "0xe2bbb158", "0x441a3e70", "0xe2bbb158", "0x441a3e70", "0xe2bbb158", "0x441a3e70", "0xe2bbb158", "0x441a3e70", "0xe2bbb158", "0x441a3e70", "0xe2bbb158", "0x441a3e70", "0xe2bbb158", "0x441a3e70", "0xe2bbb158", "0x441a3e70", "0xe2bbb158", "0x441a3e70", "0xe2bbb158", "0x441a3e70", "0xe2bbb158", "0x441a3e70", "0xe2bbb158", "0x441a3e70", "0xe2bbb158", "0x441a3e70", "0xe2bbb158", "0x441a3e70", "0xe2bbb158", "0x441a3e70", "0xe2bbb158", "0x441a3e70", "0xe2bbb158", "0x441a3e70", "0xe2bbb158", "0x441a3e70", "0xe2bbb158", "0x441a3e70", "0xe2bbb158", "0x441a3e70", "0xe2bbb158", "0x441a3e70", "0xe2bbb158", "0x441a3e70", "0xe2bbb158", "0x441a3e70", "0xe2bbb158", "0x441a3e70", "0xe2bbb158", "0x441a3e70", "0xe2bbb158", "0x441a3e70", "0xe2bbb158", "0x441a3e70", "0xe2bbb158", "0x441a3e70", "0xe2bbb158", "0x441a3e70", "0xe2bbb158", "0x441a3e70", "0xe2bbb158", "0x441a3e70", "0xe2bbb158", "0x441a3e70", "0xe2bbb158", "0x441a3e70", "0xe2bbb158", "0x441a3e70", "0xe2bbb158", "0x441a3e70", "0xe2bbb158", "0x441a3e70", "0xe2bbb158", "0x441a3e70", "0xe2bbb158", "0x441a3e70", "0xe2bbb158", "0x441a3e70", "0xe2bbb158", "0x441a3e70", "0xe2bbb158", "0x441a3e70", "0xe2bbb158", "0x441a3e70", "0xe2bbb158", "0x441a3e70", "0xe2bbb158", "0x441a3e70", "0xe2bbb158", "0x441a3e70", "0xe2bbb158", "0x441a3e70", "0xe2bbb158", "0x441a3e70", "0xe2bbb158", "0x441a3e70", "0xe2bbb158", "0x441a3e70", "0xe2bbb158", "0x441a3e70", "0xe2bbb158", "0x441a3e70", "0xe2bbb158", "0x441a3e70", "0xe2bbb158", "0x441a3e70", "0xe2bbb158", "0x441a3e70", "0xe2bbb158", "0x441a3e70", "0xe2bbb158", "0x441a3e70", "0xe2bbb158", "0x441a3e70", "0xe2bbb158", "0x441a3e70", "0xe2bbb158", "0x441a3e70", "0xe2bbb158", "0x441a3e70", "0xe2bbb158", "0x441a3e70", "0xe2bbb158", "0x441a3e70", "0xe2bbb158", "0x441a3e70", "0xe2bbb158", "0x441a3e70", "0xe2bbb158", "0x441a3e70", "0xe2bbb158", "0x441a3e70", "0xe2bbb158", "0x441a3e70", "0xe2bbb158", "0x441a3e70", "0xe2bbb158", "0x441a3e70", "0xe2bbb158", "0x441a3e70", "0xe2bbb158", "0x441a3e70", "0xe2bbb158", "0x441a3e70", "0xe2bbb158", "0x441a3e70", "0xe2bbb158", "0x441a3e70", "0xe2bbb158", "0x441a3e70", "0xe2bbb158", "0x441a3e70", "0xe2bbb158", "0x441a3e70", "0xe2bbb158", "0x441a3e70", "0xe2bbb158", "0x441a3e70", "0xe2bbb158", "0x441a3e70", "0xe2bbb158", "0x441a3e70", "0xe2bbb158", "0x441a3e70", "0xe2bbb158", "0x441a3e70", "0xe2bbb158", "0x441a3e70", "0xe2bbb158", "0x441a3e70", "0xe2bbb158", "0x441a3e70", "0xe2bbb158", "0x441a3e70", "0xe2bbb158", "0x441a3e70", "0xe2bbb158", "0x441a3e70", "0xe2bbb158", "0x441a3e70", "0xe2bbb158", "0x441a3e70", "0xe2bbb158", "0x441a3e70", "0xe2bbb158", "0x441a3e70", "0xe2bbb158", "0x441a3e70", "0xe2bbb158", "0x441a3e70", "0xe2bbb158", "0x441a3e70", "0xe2bbb158", "0x441a3e70", "0xe2bbb158", "0x441a3e70", "0xe2bbb158", "0x441a3e70", "0xe2bbb158", "0x441a3e70", "0xe2bbb158", "0x441a3e70", "0xe2bbb158", "0x441a3e70", "0xe2bbb158", "0x441a3e70", "0xe2bbb158", "0x441a3e70", "0xe2bbb158", "0x441a3e70", "0xe2bbb158", "0x441a3e70", "0xe2bbb158", "0x441a3e70", "0xe2bbb158", "0x441a3e70", "0xe2bbb158", "0x441a3e70", "0xe2bbb158", "0x441a3e70", "0xe2bbb158", "0x441a3e70", "0xe2bbb158", "0x441a3e70", "0xe2bbb158", "0x441a3e70", "0xe2bbb158", "0x441a3e70", "0xe2bbb158", "0x441a3e70", "0xe2bbb158", "0x441a3e70", "0xe2bbb158", "0x441a3e70", "0xe2bbb158", "0x441a3e70", "0xe2bbb158", "0x441a3e70", "0xe2bbb158", "0x441a3e70", "0xe2bbb158", "0x441a3e70", "0xe2bbb158", "0x441a3e70", "0xe2bbb158", "0x441a3e70", "0xe2bbb158", "0x441a3e70", "0xe2bbb158", "0x441a3e70", "0xe2bbb158", "0x441a3e70", "0xe2bbb158", "0x441a3e70", "0xe2bbb158", "0x441a3e70", "0xe2bbb158", "0x441a3e70", "0xe2bbb158", "0x441a3e70", "0xe2bbb158", "0x441a3e70", "0xe2bbb158", "0x441a3e70", "0xe2bbb158", "0x441a3e70", "0xe2bbb158", "0x441a3e70", "0xe2bbb158", "0x441a3e70", "0xe2bbb158", "0x441a3e70", "0xe2bbb158", "0x441a3e70", "0xe2bbb158", "0x441a3e70", "0xe2bbb158", "0x441a3e70", "0xe2bbb158", "0x441a3e70", "0xe2bbb158", "0x441a3e70", "0xe2bbb158", "0x441a3e70", "0xe2bbb158", "0x441a3e70", "0xe2bbb158", "0x441a3e70", "0xe2bbb158", "0x441a3e70", "0xe2bbb158", "0x441a3e70", "0xe2bbb158", "0x441a3e70", "0xe2bbb158", "0x441a3e70", "0xe2bbb158", "0x441a3e70", "0xe2bbb158", "0x441a3e70", "0xe2bbb158", "0x441a3e70", "0xe2bbb158", "0x441a3e70", "0xe2bbb158", "0x441a3e70", "0xe2bbb158", "0x441a3e70", "0xe2bbb158", "0x441a3e70", "0xe2bbb158", "0x441a3e70", "0xe2bbb158", "0x441a3e70", "0xe2bbb158", "0x441a3e70", "0xe2bbb158", "0x441a3e70", "0xe2bbb158", "0x441a3e70", "0xe2bbb158", "0x441a3e70", "0xe2bbb158", "0x441a3e70", "0xe2bbb158", "0x441a3e70", "0xe2bbb158", "0x441a3e70", "0xe2bbb158", "0x441a3e70", "0xe2bbb158", "0x441a3e70", "0xe2bbb158", "0x441a3e70", "0xe2bbb158", "0x441a3e70", "0xe2bbb158", "0x441a3e70", "0xe2bbb158", "0x441a3e70", "0xe2bbb158", "0x441a3e70", "0xe2bbb158", "0x441a3e70", "0xe2bbb158", "0x441a3e70", "0xe2bbb158", "0x441a3e70", "0xe2bbb158", "0x441a3e70", "0xe2bbb158", "0x441a3e70", "0xe2bbb158", "0x441a3e70", "0xe2bbb158", "0x441a3e70", "0xe2bbb158", "0x441a3e70", "0xe2bbb158", "0x441a3e70", "0xe2bbb158", "0x441a3e70", "0xe2bbb158", "0x441a3e70", "0xe2bbb158", "0x441a3e70", "0xe2bbb158", "0x441a3e70", "0xe2bbb158", "0x441a3e70", "0xe2bbb158", "0x441a3e70", "0xe2bbb158", "0x441a3e70", "0xe2bbb158", "0x441a3e70", "0xe2bbb158", "0x441a3e70", "0xe2bbb158", "0x441a3e70", "0xe2bbb158", "0x441a3e70", "0xe2bbb158", "0x441a3e70", "0xe2bbb158", "0x441a3e70", "0xe2bbb158", "0x441a3e70", "0xe2bbb158", "0x441a3e70", "0xe2bbb158", "0x441a3e70", "0xe2bbb158", "0x441a3e70", "0xe2bbb158", "0x441a3e70", "0xe2bbb158", "0x441a3e70", "0xe2bbb158", "0x441a3e70", "0xe2bbb158", "0x441a3e70", "0xe2bbb158", "0x441a3e70", "0xe5d6bf02", "0x98969e82", "0xa852365f", "0x6d118230"], "exploit_name": "SafeDollar_exp", "function_sigs": ["0x98969e82", "0xe5d6bf02", "0x095ea7b3", "0x8803dbee", "0xa852365f", "0xe2bbb158", "0x6d118230", "0x022c0d9f", "0x441a3e70", "0xe6dbd4d7", "0xa9059cbb"]}, {"calls": ["0xc12de71c", "0x1f7b4f30", "0xe2bbb158", "0x441a3e70", "0xe2bbb158", "0x441a3e70", "0xe2bbb158", "0x441a3e70", "0xe2bbb158", "0x441a3e70", "0xe2bbb158", "0x441a3e70", "0xe2bbb158", "0x441a3e70", "0x1f7b4f30", "0xa4db0322", "0x615787ba"], "exploit_name": "ZABU_exp", "function_sigs": ["0x095ea7b3", "0x441a3e70", "0x8803dbee", "0x615787ba", "0xe2bbb158", "0xa4db0322", "0x022c0d9f", "0x1f7b4f30", "0xc12de71c", "0xa9059cbb"]}, {"calls": ["0x580ab673", "0x00f714ce"], "exploit_name": "OneRing_exp", "function_sigs": ["0x095ea7b3", "0x00f714ce", "0x022c0d9f", "0xa9059cbb", "0x580ab673"]}, {"calls": ["0x1249c58b"], "exploit_name": "Uerii_exp", "function_sigs": ["0x414bf389", "0x1249c58b", "0x095ea7b3"]}, {"calls": ["0xdb006a75", "0xa694fc3a", "0xa694fc3a", "0xa694fc3a", "0xa694fc3a", "0xa694fc3a", "0xa694fc3a", "0xa694fc3a", "0xa694fc3a", "0xa694fc3a", "0xa694fc3a", "0xa694fc3a", "0xa694fc3a", "0xa694fc3a", "0xa694fc3a", "0xa694fc3a", "0xa694fc3a", "0xa694fc3a", "0xa694fc3a", "0xa694fc3a", "0xa694fc3a", "0xa694fc3a", "0xa694fc3a", "0xa694fc3a", "0xa694fc3a", "0xa694fc3a", "0xa694fc3a", "0xa694fc3a", "0xa694fc3a", "0xa694fc3a", "0xa694fc3a", "0xa694fc3a", "0xa694fc3a", "0xa694fc3a", "0xa694fc3a", "0xa694fc3a", "0xa694fc3a", "0xa694fc3a", "0xa694fc3a", "0xa694fc3a", "0xa694fc3a", "0xa694fc3a", "0xa694fc3a", "0xa694fc3a", "0xa694fc3a", "0xa694fc3a", "0xa694fc3a", "0xa694fc3a", "0xa694fc3a", "0xa694fc3a", "0xa694fc3a", "0xa694fc3a", "0xa694fc3a", "0xa694fc3a", "0xa694fc3a", "0xa694fc3a", "0xa694fc3a", "0xa694fc3a", "0xa694fc3a", "0xa694fc3a", "0xa694fc3a", "0xa694fc3a", "0xa694fc3a", "0xa694fc3a", "0xa694fc3a", "0xa694fc3a", "0xa694fc3a", "0xa694fc3a", "0xa694fc3a"], "exploit_name": "WaultFinance_exp", "function_sigs": ["0x095ea7b3", "0xdb006a75", "0xa694fc3a", "0x022c0d9f", "0xa9059cbb", "0x5c11d795"]}, {"calls": ["0xca669fa7", "0xc6427474", "0xca669fa7", "0xc01a8c84"], "exploit_name": "Harmony_multisig", "function_sigs": ["0xc01a8c84", "0xca669fa7", "0xc6427474"]}, {"calls": ["0xc657c718", "0xc657c718", "0xc657c718", "0xc657c718", "0x06447d56", "0x01d5062a", "0x8065657f", "0x584b153e", "0x1f7b4f30", "0xe5d6bf02", "0x134008d3", "0x2ab0f529", "0x90c5013b", "0x06447d56", "0xbf81cda3", "0x40c10f19", "0x90c5013b", "0xf4844814", "0x06447d56", "0x1058d281", "0xf4844814", "0x441a3e70", "0x90c5013b", "0x06447d56", "0x5312ea8e", "0x90c5013b", "0x06447d56", "0x5312ea8e", "0x90c5013b"], "exploit_name": "<PERSON>athan_poc", "function_sigs": ["0xe5d6bf02", "0x2ab0f529", "0xc657c718", "0x90c5013b", "0x1058d281", "0x441a3e70", "0x584b153e", "0x8065657f", "0x01d5062a", "0x06447d56", "0x5312ea8e", "0x1f7b4f30", "0xf4844814", "0xbf81cda3", "0x40c10f19", "0x134008d3"]}, {"calls": ["0xfff6cae9", "0xbc25cf77", "0xbc25cf77", "0xbc25cf77", "0xbc25cf77", "0xbc25cf77", "0xbc25cf77", "0xbc25cf77", "0xbc25cf77", "0xbc25cf77", "0xbc25cf77", "0xbc25cf77", "0xbc25cf77", "0xbc25cf77", "0xbc25cf77", "0xbc25cf77", "0xbc25cf77", "0x2e1a7d4d"], "exploit_name": "XST", "function_sigs": ["0xfff6cae9", "0x022c0d9f", "0x2e1a7d4d", "0xa9059cbb", "0xbc25cf77"]}, {"calls": ["0xc657c718", "0xc657c718", "0xc657c718", "0xafd7223f"], "exploit_name": "CowSwap_exp", "function_sigs": ["0xafd7223f", "0xc657c718"]}, {"calls": ["0xc657c718", "0xc657c718", "0x799a5359", "0x799a5359", "0x799a5359", "0x799a5359", "0x799a5359", "0x799a5359", "0x799a5359", "0x799a5359", "0x799a5359", "0x799a5359", "0x799a5359", "0x799a5359", "0x799a5359", "0x799a5359", "0x799a5359", "0x799a5359", "0x799a5359", "0x799a5359", "0x799a5359", "0x799a5359", "0x799a5359", "0x799a5359", "0x799a5359", "0x799a5359", "0x799a5359", "0x799a5359", "0x799a5359", "0x799a5359", "0x799a5359", "0x799a5359", "0x799a5359", "0x799a5359", "0x799a5359", "0x799a5359", "0x799a5359", "0x799a5359", "0x799a5359", "0x799a5359", "0x799a5359", "0x799a5359", "0x799a5359", "0x799a5359", "0x799a5359", "0x799a5359", "0x799a5359", "0x799a5359", "0x799a5359", "0x799a5359", "0x799a5359", "0x799a5359", "0x799a5359", "0x799a5359", "0x799a5359", "0x799a5359", "0x799a5359", "0x799a5359", "0x799a5359", "0x799a5359", "0x799a5359", "0x799a5359", "0x799a5359", "0x799a5359", "0x799a5359", "0x799a5359", "0x799a5359", "0x799a5359", "0x799a5359", "0x799a5359", "0x799a5359", "0x799a5359", "0x799a5359", "0x799a5359", "0x799a5359", "0x799a5359", "0x799a5359", "0x799a5359", "0x799a5359", "0x799a5359", "0x799a5359", "0x799a5359", "0x799a5359", "0x799a5359", "0x799a5359", "0x799a5359", "0x799a5359", "0x799a5359", "0x799a5359", "0x799a5359", "0x799a5359", "0x799a5359", "0x799a5359", "0x799a5359", "0x799a5359", "0x799a5359", "0x799a5359", "0x799a5359", "0x799a5359", "0x799a5359", "0x799a5359", "0x799a5359", "0x1f7b4f30", "0x5cffe9de", "0x3ccfd60b", "0x3ccfd60b", "0x3ccfd60b", "0x3ccfd60b", "0x3ccfd60b", "0x3ccfd60b", "0x3ccfd60b", "0x3ccfd60b", "0x3ccfd60b", "0x3ccfd60b", "0x3ccfd60b", "0x3ccfd60b", "0x3ccfd60b", "0x3ccfd60b", "0x3ccfd60b", "0x3ccfd60b", "0x3ccfd60b", "0x3ccfd60b", "0x3ccfd60b", "0x3ccfd60b", "0x3ccfd60b", "0x3ccfd60b", "0x3ccfd60b", "0x3ccfd60b", "0x3ccfd60b", "0x3ccfd60b", "0x3ccfd60b", "0x3ccfd60b", "0x3ccfd60b", "0x3ccfd60b", "0x3ccfd60b", "0x3ccfd60b", "0x3ccfd60b", "0x3ccfd60b", "0x3ccfd60b", "0x3ccfd60b", "0x3ccfd60b", "0x3ccfd60b", "0x3ccfd60b", "0x3ccfd60b", "0x3ccfd60b", "0x3ccfd60b", "0x3ccfd60b", "0x3ccfd60b", "0x3ccfd60b", "0x3ccfd60b", "0x3ccfd60b", "0x3ccfd60b", "0x3ccfd60b", "0x3ccfd60b", "0x3ccfd60b", "0x3ccfd60b", "0x3ccfd60b", "0x3ccfd60b", "0x3ccfd60b", "0x3ccfd60b", "0x3ccfd60b", "0x3ccfd60b", "0x3ccfd60b", "0x3ccfd60b", "0x3ccfd60b", "0x3ccfd60b", "0x3ccfd60b", "0x3ccfd60b", "0x3ccfd60b", "0x3ccfd60b", "0x3ccfd60b", "0x3ccfd60b", "0x3ccfd60b", "0x3ccfd60b", "0x3ccfd60b", "0x3ccfd60b", "0x3ccfd60b", "0x3ccfd60b", "0x3ccfd60b", "0x3ccfd60b", "0x3ccfd60b", "0x3ccfd60b", "0x3ccfd60b", "0x3ccfd60b", "0x3ccfd60b", "0x3ccfd60b", "0x3ccfd60b", "0x3ccfd60b", "0x3ccfd60b", "0x3ccfd60b", "0x3ccfd60b", "0x3ccfd60b", "0x3ccfd60b", "0x3ccfd60b", "0x3ccfd60b", "0x3ccfd60b", "0x3ccfd60b", "0x3ccfd60b", "0x3ccfd60b", "0x3ccfd60b", "0x3ccfd60b", "0x3ccfd60b", "0x3ccfd60b", "0x3ccfd60b"], "exploit_name": "GDS_exp", "function_sigs": ["0x095ea7b3", "0xc657c718", "0x799a5359", "0x3ccfd60b", "0xe8e33700", "0x1f7b4f30", "0xbaa2abde", "0x5cffe9de", "0xa9059cbb", "0x5c11d795"]}, {"calls": [], "exploit_name": "NUM_exp", "function_sigs": ["0x472b43f3", "0xa9059cbb", "0x8d7d3eea", "0x095ea7b3"]}, {"calls": ["0xbc25cf77", "0xbc25cf77", "0xbc25cf77", "0xbc25cf77", "0xbc25cf77", "0xbc25cf77", "0xbc25cf77", "0xbc25cf77", "0xbc25cf77", "0xbc25cf77", "0xbc25cf77", "0xbc25cf77", "0xbc25cf77", "0xbc25cf77", "0xbc25cf77", "0xbc25cf77", "0xbc25cf77", "0xbc25cf77", "0xbc25cf77", "0xbc25cf77", "0xbc25cf77", "0xbc25cf77", "0xbc25cf77", "0xbc25cf77", "0xbc25cf77", "0xbc25cf77", "0xbc25cf77", "0xbc25cf77", "0xbc25cf77", "0xbc25cf77", "0xbc25cf77", "0xbc25cf77", "0xbc25cf77", "0xbc25cf77", "0xbc25cf77", "0xbc25cf77", "0xbc25cf77", "0xbc25cf77", "0xbc25cf77", "0xbc25cf77", "0xbc25cf77", "0xbc25cf77", "0xbc25cf77", "0xbc25cf77", "0xbc25cf77", "0xbc25cf77", "0xbc25cf77", "0xbc25cf77", "0xbc25cf77", "0xbc25cf77", "0xbc25cf77", "0xbc25cf77", "0xbc25cf77", "0xbc25cf77", "0xbc25cf77", "0xbc25cf77", "0xbc25cf77", "0xbc25cf77", "0xbc25cf77", "0xbc25cf77", "0xbc25cf77"], "exploit_name": "ANCH_exp", "function_sigs": ["0xa9059cbb", "0x5c11d795", "0x095ea7b3", "0xbc25cf77"]}, {"calls": ["0x5c38449e", "0x3bd5d173", "0xbc25cf77", "0x3bd5d173"], "exploit_name": "TINU_exp", "function_sigs": ["0x095ea7b3", "0x3bd5d173", "0x5c38449e", "0x022c0d9f", "0xa9059cbb", "0x5c11d795", "0xbc25cf77"]}, {"calls": ["0xe5da2cb2", "0x23b872dd", "0x3a838636", "0x3d18b912"], "exploit_name": "SDAO_exp", "function_sigs": ["0x3a838636", "0x3d18b912", "0x095ea7b3", "0xe5da2cb2", "0xe8e33700", "0xa9059cbb", "0x5c11d795", "0x23b872dd"]}, {"calls": ["0x2e1a7d4d"], "exploit_name": "Umbrella_exp", "function_sigs": ["0x2e1a7d4d"]}, {"calls": ["0x50b1e342", "0x8201aa3f", "0x8201aa3f", "0x8201aa3f", "0x8201aa3f", "0x8201aa3f", "0x8201aa3f", "0x8201aa3f", "0x8201aa3f", "0x8201aa3f", "0x8201aa3f", "0x8201aa3f", "0x8201aa3f", "0x8201aa3f", "0x8201aa3f", "0x8201aa3f", "0x8201aa3f", "0x8201aa3f", "0x8201aa3f", "0x8201aa3f", "0x8201aa3f", "0x8201aa3f", "0x8201aa3f", "0x8201aa3f", "0x8201aa3f", "0x8201aa3f", "0x8201aa3f", "0x8201aa3f", "0x8201aa3f", "0x8201aa3f", "0x8201aa3f", "0x8201aa3f", "0x8201aa3f", "0x8201aa3f", "0x8201aa3f", "0x034b904e", "0x5db34277", "0x5db34277", "0x5db34277", "0x5db34277", "0x5db34277", "0x5db34277", "0x5db34277", "0x5db34277", "0x5db34277", "0x5db34277", "0x5db34277", "0x5db34277", "0x5db34277", "0x5db34277", "0x5db34277", "0x5db34277", "0x8c28cbe8", "0xb02f0b73", "0x5db34277", "0x5db34277", "0x5db34277", "0x5db34277", "0x5db34277", "0x5db34277", "0xb02f0b73", "0x5db34277", "0x5db34277", "0x5db34277", "0x5db34277", "0x5db34277", "0xb02f0b73"], "exploit_name": "IndexedFinance_exp", "function_sigs": ["0x8c28cbe8", "0x095ea7b3", "0xb02f0b73", "0x8803dbee", "0x034b904e", "0x5db34277", "0x50b1e342", "0x022c0d9f", "0x8201aa3f", "0xa9059cbb"]}, {"calls": ["0xa0712d68", "0x23b872dd"], "exploit_name": "Annex_exp", "function_sigs": ["0x095ea7b3", "0xa0712d68", "0xe8e33700", "0x022c0d9f", "0xbaa2abde", "0xa9059cbb", "0x23b872dd"]}, {"calls": ["0xc657c718", "0xc657c718", "0xc657c718", "0xc657c718", "0xc657c718", "0xc657c718", "0x3a4b66f1", "0xe5d6bf02", "0x4641257d"], "exploit_name": "EGD-Finance", "function_sigs": ["0x4641257d", "0x3a4b66f1", "0xe5d6bf02", "0xc657c718"]}, {"calls": ["0x06447d56", "0x6ee678ae", "0x6ee678ae", "0x6ee678ae", "0x6ee678ae", "0x6ee678ae", "0x6ee678ae", "0x6ee678ae", "0x6ee678ae", "0x6ee678ae", "0x6ee678ae", "0x6ee678ae", "0x6ee678ae", "0x6ee678ae", "0x6ee678ae", "0x6ee678ae", "0x6ee678ae", "0x6ee678ae", "0x6ee678ae", "0x6ee678ae", "0x6ee678ae", "0x6ee678ae", "0x6ee678ae", "0x6ee678ae", "0x6ee678ae", "0x6ee678ae", "0x6ee678ae", "0x6ee678ae", "0x6ee678ae", "0x6ee678ae", "0x6ee678ae", "0x6ee678ae", "0x6ee678ae", "0x6ee678ae", "0x6ee678ae", "0x6ee678ae", "0x6ee678ae", "0x6ee678ae", "0x6ee678ae", "0x6ee678ae", "0x6ee678ae", "0x6ee678ae", "0x6ee678ae", "0x6ee678ae", "0x6ee678ae", "0x6ee678ae", "0x6ee678ae", "0x6ee678ae", "0x6ee678ae", "0x6ee678ae", "0x6ee678ae", "0x6ee678ae", "0x6ee678ae", "0x6ee678ae", "0x6ee678ae", "0x6ee678ae", "0x6ee678ae", "0x6ee678ae", "0x6ee678ae", "0x6ee678ae", "0x6ee678ae", "0x6ee678ae", "0x6ee678ae", "0x6ee678ae", "0x6ee678ae", "0x6ee678ae", "0x6ee678ae", "0x6ee678ae", "0x6ee678ae", "0x6ee678ae", "0x6ee678ae", "0x6ee678ae", "0x6ee678ae", "0x6ee678ae", "0x6ee678ae", "0x6ee678ae", "0x6ee678ae", "0x6ee678ae", "0x6ee678ae", "0x6ee678ae", "0x6ee678ae", "0x6ee678ae", "0x6ee678ae", "0x6ee678ae", "0x6ee678ae", "0x6ee678ae", "0x6ee678ae", "0x6ee678ae", "0x6ee678ae", "0x6ee678ae", "0x6ee678ae", "0x6ee678ae", "0x6ee678ae", "0x6ee678ae", "0x6ee678ae", "0x6ee678ae", "0x6ee678ae", "0x6ee678ae", "0x6ee678ae", "0x6ee678ae", "0x6ee678ae"], "exploit_name": "Sandbox_exp", "function_sigs": ["0x06447d56", "0x6ee678ae"]}, {"calls": ["0x2e1a7d4d", "0xbc25cf77", "0xbc25cf77", "0xbc25cf77", "0xbc25cf77", "0xbc25cf77", "0xbc25cf77", "0xbc25cf77", "0xbc25cf77", "0xbc25cf77", "0xbc25cf77", "0xbc25cf77", "0xbc25cf77", "0xbc25cf77", "0xbc25cf77", "0xbc25cf77", "0xbc25cf77", "0xbc25cf77", "0xbc25cf77", "0xbc25cf77", "0xbc25cf77", "0xbc25cf77", "0xbc25cf77", "0xbc25cf77", "0xbc25cf77", "0xbc25cf77", "0xbc25cf77", "0xbc25cf77", "0xbc25cf77", "0xbc25cf77", "0xbc25cf77", "0xbc25cf77", "0xbc25cf77", "0xbc25cf77", "0xbc25cf77", "0xbc25cf77", "0xbc25cf77", "0xbc25cf77", "0xbc25cf77", "0xbc25cf77", "0xbc25cf77", "0xbc25cf77", "0xbc25cf77", "0xbc25cf77", "0xbc25cf77", "0xbc25cf77", "0xbc25cf77", "0xbc25cf77", "0xbc25cf77", "0xbc25cf77", "0xbc25cf77", "0xbc25cf77", "0xbc25cf77", "0xbc25cf77", "0xbc25cf77", "0xbc25cf77", "0xbc25cf77", "0xbc25cf77", "0xbc25cf77", "0xbc25cf77", "0xbc25cf77", "0xbc25cf77", "0xbc25cf77", "0xbc25cf77", "0xbc25cf77", "0xbc25cf77", "0xbc25cf77", "0xbc25cf77", "0xbc25cf77", "0xbc25cf77", "0xbc25cf77", "0xbc25cf77", "0xbc25cf77", "0xbc25cf77", "0xbc25cf77", "0xbc25cf77", "0xbc25cf77", "0xbc25cf77", "0xbc25cf77", "0xbc25cf77", "0xbc25cf77", "0xbc25cf77", "0xbc25cf77", "0xbc25cf77", "0xbc25cf77", "0xbc25cf77", "0xbc25cf77", "0xbc25cf77", "0xbc25cf77", "0xbc25cf77", "0xbc25cf77", "0xbc25cf77", "0xbc25cf77", "0xbc25cf77", "0xbc25cf77", "0xbc25cf77", "0xbc25cf77", "0xbc25cf77", "0xbc25cf77", "0xbc25cf77", "0xbc25cf77", "0xbc25cf77", "0xd0e30db0"], "exploit_name": "BRA", "function_sigs": ["0x095ea7b3", "0x7ff36ab5", "0xd0e30db0", "0x022c0d9f", "0x2e1a7d4d", "0xa9059cbb", "0xbc25cf77", "0x18cbafe5"]}, {"calls": ["0xc657c718", "0xc657c718", "0x928bc4b2"], "exploit_name": "NomadBridge", "function_sigs": ["0xc657c718", "0x928bc4b2"]}, {"calls": ["0x490e6cbc", "0xe2bbb158", "0x441a3e70"], "exploit_name": "DFX_exp", "function_sigs": ["0x414bf389", "0x095ea7b3", "0xe2bbb158", "0x490e6cbc", "0x441a3e70"]}, {"calls": ["0xffa18649", "0xca669fa7", "0x1998aeef", "0x84097393"], "exploit_name": "FlippazOne", "function_sigs": ["0xffa18649", "0xca669fa7", "0x1998aeef", "0x84097393"]}, {"calls": ["0xc657c718", "0xc657c718", "0xc657c718", "0xc657c718", "0xc657c718", "0xc657c718", "0xc657c718", "0x4f1f05bc", "0x70a08231", "0x266cf109", "0x65bc9481", "0x70ca10bb"], "exploit_name": "LaunchZone_exp", "function_sigs": ["0x266cf109", "0x095ea7b3", "0x65bc9481", "0xc657c718", "0x70a08231", "0x38ed1739", "0x70ca10bb", "0x4f1f05bc"]}, {"calls": ["0x6115880a", "0x23b872dd", "0x23b872dd"], "exploit_name": "RES02_exp", "function_sigs": ["0x095ea7b3", "0x5c11d795", "0x022c0d9f", "0xa9059cbb", "0x6115880a", "0x23b872dd"]}, {"calls": ["0xc657c718", "0xc657c718", "0xc657c718", "0xc657c718", "0xd2ea9853", "0xec5a4bdd", "0xd2ea9853", "0xec5a4bdd"], "exploit_name": "UFDao_exp", "function_sigs": ["0x095ea7b3", "0xc657c718", "0xb6f9de95", "0xec5a4bdd", "0xd2ea9853"]}, {"calls": ["0xc88a5e6d", "0xc88a5e6d", "0xe5d6bf02"], "exploit_name": "LuckyTiger_exp", "function_sigs": ["0xc88a5e6d", "0xe5d6bf02"]}, {"calls": ["0x30e0789e"], "exploit_name": "cftoken_exp", "function_sigs": ["0x30e0789e"]}, {"calls": ["0x4420e486", "0x4420e486", "0x4420e486", "0x4420e486", "0x4420e486", "0x4420e486", "0x4420e486", "0x4420e486", "0x4420e486", "0x4420e486", "0x4420e486", "0x4420e486", "0x4420e486", "0x4420e486", "0x4420e486", "0x4420e486", "0x4420e486", "0x4420e486", "0x4420e486", "0x4420e486", "0x4420e486", "0x4420e486", "0x4420e486", "0x4420e486", "0x4420e486", "0x4420e486", "0x4420e486", "0x4420e486", "0x4420e486", "0x4420e486", "0x4420e486", "0x4420e486", "0x4420e486", "0x4420e486", "0x4420e486", "0x4420e486", "0x4420e486", "0x4420e486", "0x4420e486", "0x4420e486", "0x4420e486", "0x4420e486", "0x4420e486", "0x4420e486", "0x4420e486", "0x4420e486", "0x4420e486", "0x4420e486", "0x4420e486", "0x4420e486", "0x4420e486", "0x4420e486", "0x4420e486", "0x4420e486", "0x4420e486", "0x4420e486", "0x4420e486", "0x4420e486", "0x4420e486", "0x4420e486", "0x4420e486", "0x4420e486", "0x4420e486", "0x4420e486", "0x4420e486", "0x4420e486", "0x4420e486", "0x4420e486", "0x4420e486", "0x4420e486", "0x4420e486", "0x4420e486", "0x4420e486", "0x4420e486", "0x4420e486", "0x4420e486", "0x4420e486", "0x4420e486", "0x4420e486", "0x4420e486", "0x4420e486", "0x4420e486", "0x4420e486", "0x4420e486", "0x4420e486", "0x4420e486", "0x4420e486", "0x4420e486", "0x4420e486", "0x4420e486", "0x4420e486", "0x4420e486", "0x4420e486", "0x4420e486", "0x4420e486", "0x4420e486", "0x4420e486", "0x4420e486", "0x4420e486", "0x4420e486", "0x4420e486", "0x4420e486", "0x4420e486", "0x4420e486", "0x4420e486", "0x4420e486", "0x4420e486", "0x4420e486", "0x4420e486", "0x4420e486", "0x4420e486", "0x4420e486", "0x4420e486", "0x4420e486", "0x4420e486", "0x4420e486", "0x4420e486", "0x4420e486", "0x4420e486", "0x4420e486", "0x4420e486", "0x4420e486", "0x4420e486", "0x4420e486", "0x4420e486", "0x4420e486", "0x4420e486", "0x4420e486", "0x4420e486", "0x4420e486", "0x4420e486", "0x4420e486", "0x4420e486", "0x4420e486", "0x4420e486", "0x4420e486", "0x4420e486", "0x4420e486", "0x4420e486", "0x4420e486", "0x4420e486", "0x4420e486", "0x4420e486", "0x4420e486", "0x4420e486", "0x4420e486", "0x4420e486", "0x4420e486", "0x4420e486", "0x4420e486", "0x4420e486", "0x4420e486", "0x4420e486", "0x4420e486", "0x4420e486", "0x4420e486", "0x4420e486", "0x4420e486", "0x4420e486", "0x4420e486", "0x4420e486", "0x4420e486", "0x4420e486", "0x4420e486", "0x4420e486", "0x4420e486", "0x4420e486", "0x4420e486", "0x4420e486", "0x4420e486", "0x4420e486", "0x4420e486", "0x4420e486", "0x4420e486", "0x4420e486", "0x4420e486", "0x4420e486", "0x4420e486", "0x4420e486", "0x4420e486", "0x4420e486", "0x4420e486", "0x4420e486", "0x4420e486", "0x4420e486", "0x4420e486", "0x4420e486", "0x4420e486", "0x4420e486", "0x4420e486", "0x4420e486", "0x4420e486", "0x4420e486", "0x4420e486", "0x4420e486", "0x4420e486", "0x4420e486", "0x4420e486", "0x4420e486", "0x4420e486", "0xf9b19733", "0x1b1fdd58", "0x1b1fdd58", "0x1b1fdd58", "0x851d1e05", "0x54876921"], "exploit_name": "SheepFram_exp", "function_sigs": ["0x54876921", "0xf9b19733", "0x1b1fdd58", "0x851d1e05", "0x4420e486"]}, {"calls": ["0xca669fa7", "0xe6bd7ed1"], "exploit_name": "NovaExchange_exp", "function_sigs": ["0xca669fa7", "0xe6bd7ed1"]}, {"calls": [], "exploit_name": "Nimbus_exp", "function_sigs": ["0xa9059cbb", "0x022c0d9f"]}, {"calls": ["0xca669fa7", "0x0917c4e1", "0xca669fa7", "0xfb4a9be0"], "exploit_name": "BadGuysbyRPF_exp", "function_sigs": ["0x0917c4e1", "0xca669fa7", "0xfb4a9be0"]}, {"calls": ["0xc88a5e6d", "0x8013858b", "0xe6aeb87d", "0x1f7b4f30", "0xe5d6bf02", "0x7b770392", "0xebb8035a"], "exploit_name": "AUR_exp", "function_sigs": ["0xe5d6bf02", "0x095ea7b3", "0xe6aeb87d", "0xebb8035a", "0x8013858b", "0x791ac947", "0xb6f9de95", "0xc88a5e6d", "0x7b770392", "0x1f7b4f30"]}, {"calls": ["0x29965a1d", "0xa59f3e0c", "0xa59f3e0c", "0xa59f3e0c", "0x67dfd4c9", "0xa59f3e0c", "0xa59f3e0c", "0xa59f3e0c", "0x67dfd4c9", "0xa59f3e0c", "0xa59f3e0c", "0xa59f3e0c", "0x67dfd4c9", "0xa59f3e0c", "0xa59f3e0c", "0xa59f3e0c", "0x67dfd4c9", "0xa59f3e0c", "0xa59f3e0c", "0xa59f3e0c", "0x67dfd4c9"], "exploit_name": "N00d_exp", "function_sigs": ["0x095ea7b3", "0x29965a1d", "0x67dfd4c9", "0x022c0d9f", "0xa59f3e0c", "0xa9059cbb"]}, {"calls": ["0xb6b55f25", "0x5c38449e", "0x2e1a7d4d", "0x48c73f68"], "exploit_name": "EFLeverVault_exp", "function_sigs": ["0x2e1a7d4d", "0x48c73f68", "0x5c38449e", "0xb6b55f25"]}, {"calls": ["0x5c38449e", "0x2e1a7d4d", "0x666566e8", "0x666566e8", "0xe4849b32", "0xe4849b32", "0x666566e8", "0x666566e8", "0xe4849b32", "0xe4849b32", "0x6465706f"], "exploit_name": "JAY_exp", "function_sigs": ["0x666566e8", "0xe4849b32", "0x6465706f", "0x5c38449e", "0x2e1a7d4d", "0xa9059cbb"]}, {"calls": ["0x9b594f51", "0x5622555d"], "exploit_name": "MUMUG_exp", "function_sigs": ["0x095ea7b3", "0x9b594f51", "0x5622555d", "0x022c0d9f", "0xa9059cbb", "0x5c11d795"]}, {"calls": ["0xc657c718", "0xc657c718", "0xc657c718", "0xc657c718", "0xc657c718", "0xc657c718", "0xc657c718", "0xc657c718", "0xc657c718", "0xc657c718", "0xc657c718", "0xc657c718", "0xc657c718", "0xc657c718", "0xc657c718", "0xc657c718", "0xc657c718", "0xc657c718", "0xc657c718", "0xc657c718", "0xc657c718", "0xc657c718", "0xc657c718", "0xc657c718", "0x5c38449e", "0xab9c4b5d", "0xab9c4b5d", "0x490e6cbc", "0x5cffe9de", "0x2e1a7d4d", "0x0b4c7e4d", "0xc1c0e9c4", "0xfc57d4df", "0x5b36389c", "0xfc57d4df", "0x95dd9193", "0xf5e3c462", "0x95dd9193", "0xf5e3c462", "0x1e9a6950", "0x2e1a7d4d", "0x5b36389c", "0x3df02124", "0xd0e30db0", "0xa6417ed6", "0x93316212"], "exploit_name": "dForce_exp", "function_sigs": ["0x095ea7b3", "0x3df02124", "0x5c38449e", "0x93316212", "0x2e1a7d4d", "0xf5e3c462", "0xc657c718", "0xd0e30db0", "0x95dd9193", "0x022c0d9f", "0xc1c0e9c4", "0x5cffe9de", "0xa9059cbb", "0xa6417ed6", "0x5b36389c", "0x0b4c7e4d", "0xab9c4b5d", "0x1e9a6950", "0xfc57d4df", "0x490e6cbc"]}, {"calls": ["0xc657c718", "0xc657c718", "0xc657c718", "0xc657c718", "0xc657c718", "0xc657c718", "0xc657c718", "0xc657c718", "0xab9c4b5d", "0xc657c718", "0x1249c58b", "0x2e1a7d4d", "0xa0712d68", "0xc2998238", "0xc5ebeaec", "0xdb006a75", "0xc5ebeaec", "0xc5ebeaec", "0xbe040fb0", "0x5b41b908", "0x5b41b908"], "exploit_name": "Paribus_exp", "function_sigs": ["0x5b41b908", "0xc5ebeaec", "0xab9c4b5d", "0x095ea7b3", "0xc657c718", "0xa0712d68", "0xc2998238", "0xbe040fb0", "0xdb006a75", "0x2e1a7d4d", "0xa9059cbb", "0x1249c58b"]}, {"calls": ["0xc2998238", "0x1249c58b", "0xead99147", "0xc5ebeaec", "0xead99147"], "exploit_name": "Rikkei_exp", "function_sigs": ["0xc5ebeaec", "0x095ea7b3", "0xc2998238", "0xead99147", "0xa9059cbb", "0x1249c58b"]}, {"calls": ["0x7d1a6533"], "exploit_name": "TreasureDAO_exp", "function_sigs": ["0x7d1a6533"]}, {"calls": ["0xc657c718", "0xc657c718", "0xc657c718", "0xc657c718", "0xc657c718", "0xc657c718", "0xc657c718", "0xc657c718", "0xc657c718", "0x6a627842", "0x6a627842", "0x6a627842", "0x6a627842", "0x6a627842", "0x6a627842", "0x6a627842", "0x6a627842", "0x6a627842", "0x6a627842", "0x6a627842", "0x6a627842", "0x6a627842", "0x6a627842", "0x6a627842", "0x6a627842", "0x6a627842", "0x6a627842"], "exploit_name": "DBW_exp", "function_sigs": ["0x095ea7b3", "0xc657c718", "0x6a627842", "0xe8e33700", "0x022c0d9f", "0xbaa2abde", "0xa9059cbb", "0x5c11d795"]}, {"calls": ["0xbc25cf77", "0xfff6cae9", "0x8fd3ab80", "0xbc25cf77", "0xfff6cae9", "0x8fd3ab80"], "exploit_name": "BGLD_exp", "function_sigs": ["0x095ea7b3", "0x8fd3ab80", "0x022c0d9f", "0xfff6cae9", "0xa9059cbb", "0x5c11d795", "0xbc25cf77"]}, {"calls": ["0x0e3d1093", "0xe5d6bf02", "0x2e1a7d4d"], "exploit_name": "Gym_2_exp", "function_sigs": ["0x2e1a7d4d", "0x0e3d1093", "0xe5d6bf02"]}, {"calls": ["0xbc25cf77"], "exploit_name": "PLTD_exp", "function_sigs": ["0xa9059cbb", "0x5c11d795", "0x095ea7b3", "0xbc25cf77"]}, {"calls": ["0xd0e30db0", "0x3a4b66f1", "0x3a4b66f1", "0x3a4b66f1", "0xe5d6bf02", "0x2e1a7d4d", "0xd0e30db0", "0x3d18b912", "0x3d18b912", "0x3d18b912", "0x2e1a7d4d"], "exploit_name": "Nmbplatform_exp", "function_sigs": ["0x3d18b912", "0xe5d6bf02", "0x095ea7b3", "0x3a4b66f1", "0xd0e30db0", "0x022c0d9f", "0x2e1a7d4d", "0xa9059cbb", "0x5c11d795"]}, {"calls": ["0x2e1a7d4d", "0xe4849b32", "0xe4849b32", "0xe4849b32", "0xe4849b32", "0xe4849b32", "0xe4849b32", "0xe4849b32", "0xd0e30db0"], "exploit_name": "XSURGE_exp", "function_sigs": ["0xe4849b32", "0xd0e30db0", "0x022c0d9f", "0x2e1a7d4d", "0xa9059cbb"]}, {"calls": ["0x5cffe9de", "0x6e553f65", "0xba087652"], "exploit_name": "Defrost_exp", "function_sigs": ["0xba087652", "0x6e553f65", "0x095ea7b3", "0x022c0d9f", "0x5cffe9de", "0xa9059cbb"]}, {"calls": ["0xc657c718", "0xc657c718", "0xc657c718", "0xc657c718", "0xc657c718", "0xc657c718", "0xc657c718", "0xc657c718", "0xc657c718", "0xc657c718", "0xc657c718", "0xc657c718", "0xc657c718", "0xc657c718", "0xc657c718", "0xc657c718", "0xc657c718", "0xc657c718", "0xc657c718", "0xc657c718", "0xc657c718", "0xc657c718", "0xc657c718", "0xc657c718", "0xc657c718", "0xc657c718", "0xc657c718", "0xc657c718", "0xc657c718", "0xab9c4b5d", "0xab9c4b5d", "0x490e6cbc", "0x490e6cbc", "0x2e1a7d4d", "0xa0712d68", "0xc2998238", "0xc5ebeaec", "0xa0712d68", "0xc5ebeaec", "0xa0712d68", "0xc5ebeaec", "0xa0712d68", "0xc5ebeaec", "0xa0712d68", "0xc5ebeaec", "0xa0712d68", "0xc5ebeaec", "0xa0712d68", "0xc5ebeaec", "0xa0712d68", "0xc5ebeaec", "0xa0712d68", "0xc5ebeaec", "0xa0712d68", "0xc5ebeaec", "0xa0712d68", "0xc5ebeaec", "0xa0712d68", "0xc5ebeaec", "0xa0712d68", "0xc5ebeaec", "0xa0712d68", "0xc5ebeaec", "0xa0712d68", "0xc5ebeaec", "0xa0712d68", "0xc5ebeaec", "0xa0712d68", "0xc5ebeaec", "0x70a08231", "0x266cf109", "0x65bc9481", "0x70ca10bb", "0x70ca10bb", "0x70ca10bb", "0x70ca10bb", "0x70ca10bb", "0x06447d56"], "exploit_name": "Lodestar_exp", "function_sigs": ["0xc5ebeaec", "0x266cf109", "0xab9c4b5d", "0x095ea7b3", "0xc657c718", "0xa0712d68", "0x65bc9481", "0x70a08231", "0xc2998238", "0x490e6cbc", "0x06447d56", "0xa9059cbb", "0x022c0d9f", "0xabe68eaa", "0x2e1a7d4d", "0x70ca10bb"]}, {"calls": ["0x8dcd2661", "0xdc89a198", "0x77f325df", "0xe5d6bf02", "0x06447d56", "0x14604b8c", "0x14604b8c", "0x14604b8c", "0x14604b8c", "0x90c5013b"], "exploit_name": "XaveFinance_exp", "function_sigs": ["0xe5d6bf02", "0xdc89a198", "0x90c5013b", "0x14604b8c", "0x06447d56", "0x8dcd2661", "0x77f325df"]}, {"calls": ["0x006de4df"], "exploit_name": "TransitSwap_exp", "function_sigs": ["0x006de4df"]}, {"calls": ["0xff09731b", "0x0ffa4104", "0x20ffe488", "0xbe040fb0"], "exploit_name": "Paraluni_exp", "function_sigs": ["0x0ffa4104", "0x20ffe488", "0xbe040fb0", "0x022c0d9f", "0xff09731b", "0xa9059cbb"]}, {"calls": ["0xbc25cf77", "0xbc25cf77", "0xbc25cf77", "0xbc25cf77", "0xbc25cf77", "0xbc25cf77", "0xbc25cf77", "0xbc25cf77", "0xbc25cf77", "0xbc25cf77", "0xbc25cf77", "0xbc25cf77", "0xbc25cf77", "0xbc25cf77", "0xbc25cf77", "0xbc25cf77", "0xbc25cf77", "0xbc25cf77", "0xbc25cf77", "0xbc25cf77", "0xbc25cf77", "0xbc25cf77", "0xbc25cf77", "0xbc25cf77", "0xbc25cf77", "0xbc25cf77", "0xbc25cf77", "0xbc25cf77", "0xbc25cf77", "0xbc25cf77", "0xbc25cf77", "0xbc25cf77", "0xbc25cf77"], "exploit_name": "Zeed_exp", "function_sigs": ["0x095ea7b3", "0x38ed1739", "0x022c0d9f", "0xa9059cbb", "0xbc25cf77"]}, {"calls": ["0xbc25cf77", "0xfff6cae9"], "exploit_name": "Wdoge_exp", "function_sigs": ["0xa9059cbb", "0xfff6cae9", "0x022c0d9f", "0xbc25cf77"]}, {"calls": ["0x45b56078", "0xd72ef771"], "exploit_name": "ValueDefi_exp", "function_sigs": ["0xd72ef771", "0x45b56078"]}, {"calls": ["0xc657c718", "0xc657c718", "0xc657c718", "0xc657c718", "0xc657c718", "0xc657c718", "0xc657c718", "0xc657c718", "0xc657c718", "0x42b0b77c", "0x4cc82215", "0x617ba037", "0x4cc82215", "0x617ba037", "0x4cc82215", "0x617ba037", "0x4cc82215", "0x617ba037", "0x4cc82215", "0x617ba037", "0x4cc82215", "0x617ba037", "0x4cc82215", "0x617ba037", "0x4cc82215", "0x2e1a7d4d", "0xbd5023a9", "0x1d5d7237", "0x1d5d7237", "0x1d5d7237"], "exploit_name": "Paraspace_exp_2", "function_sigs": ["0x414bf389", "0xdb3e2198", "0x095ea7b3", "0xc657c718", "0x2e1a7d4d", "0xbd5023a9", "0x617ba037", "0x4cc82215", "0x42b0b77c", "0xa9059cbb", "0x1d5d7237"]}, {"calls": ["0xc657c718", "0xc657c718", "0xc657c718", "0xc657c718", "0xc657c718", "0x5cffe9de", "0x42966c68", "0xfff6cae9"], "exploit_name": "BIGFI_exp", "function_sigs": ["0x42966c68", "0x095ea7b3", "0xc657c718", "0xfff6cae9", "0x5cffe9de", "0xa9059cbb", "0x5c11d795"]}, {"calls": ["0xf660337a"], "exploit_name": "MulticallWithoutCheck_exp", "function_sigs": ["0xf660337a"]}, {"calls": ["0x23b872dd", "0xfff6cae9", "0x0902f1ac"], "exploit_name": "Snood_poc", "function_sigs": ["0x0902f1ac", "0x022c0d9f", "0xfff6cae9", "0xa9059cbb", "0x23b872dd"]}, {"calls": ["0xc657c718", "0xc657c718", "0xc657c718", "0xc657c718", "0xc657c718", "0x70a08231", "0x266cf109", "0x65bc9481", "0x70ca10bb", "0xb6b55f25", "0xb6b55f25", "0xb6b55f25", "0xb6b55f25", "0xb6b55f25", "0xb6b55f25", "0xb6b55f25", "0xb6b55f25", "0xb6b55f25", "0xb6b55f25", "0xb6b55f25", "0xb6b55f25", "0xb6b55f25", "0xb6b55f25", "0xb6b55f25", "0xb6b55f25", "0xb6b55f25", "0xb6b55f25", "0xb6b55f25", "0xb6b55f25", "0xb6b55f25", "0xb6b55f25", "0xb6b55f25", "0xb6b55f25", "0xb6b55f25", "0xb6b55f25", "0xb6b55f25", "0xb6b55f25", "0xb6b55f25", "0xb6b55f25", "0xb6b55f25", "0xb6b55f25", "0xb6b55f25", "0xb6b55f25", "0xb6b55f25", "0xb6b55f25", "0xb6b55f25", "0xb6b55f25", "0xb6b55f25", "0xb6b55f25", "0xb6b55f25", "0xb6b55f25", "0xb6b55f25", "0xb6b55f25", "0xb6b55f25", "0xb6b55f25", "0xb6b55f25", "0xb6b55f25", "0xb6b55f25", "0xb6b55f25", "0xb6b55f25", "0xb6b55f25", "0xb6b55f25", "0xb6b55f25", "0xb6b55f25", "0xb6b55f25", "0xb6b55f25", "0xb6b55f25", "0xb6b55f25", "0xb6b55f25", "0xb6b55f25", "0xb6b55f25", "0xb6b55f25", "0xb6b55f25", "0xb6b55f25", "0xb6b55f25", "0xb6b55f25", "0xb6b55f25", "0xb6b55f25", "0xb6b55f25", "0xb6b55f25", "0xb6b55f25", "0xb6b55f25", "0xb6b55f25", "0xb6b55f25", "0xb6b55f25", "0xb6b55f25", "0xb6b55f25", "0xb6b55f25", "0xb6b55f25", "0xb6b55f25", "0xb6b55f25", "0xb6b55f25", "0xb6b55f25", "0xb6b55f25", "0xb6b55f25", "0xb6b55f25", "0xb6b55f25", "0xb6b55f25", "0xb6b55f25", "0xb6b55f25", "0xb6b55f25", "0xb6b55f25", "0xb6b55f25", "0xb6b55f25", "0xb6b55f25", "0xb6b55f25", "0xb6b55f25", "0xb6b55f25", "0xb6b55f25", "0xb6b55f25", "0xb6b55f25", "0xb6b55f25", "0xb6b55f25", "0xb6b55f25", "0xb6b55f25", "0xb6b55f25", "0xb6b55f25", "0xb6b55f25", "0xb6b55f25", "0xb6b55f25", "0xb6b55f25", "0xb6b55f25", "0xb6b55f25", "0xb6b55f25", "0xb6b55f25", "0xb6b55f25", "0xb6b55f25", "0xb6b55f25", "0xb6b55f25", "0xb6b55f25", "0xb6b55f25", "0xb6b55f25", "0xb6b55f25", "0xb6b55f25", "0xb6b55f25", "0xb6b55f25", "0xb6b55f25", "0xb6b55f25", "0xb6b55f25", "0xb6b55f25", "0xb6b55f25", "0xb6b55f25", "0xb6b55f25", "0xb6b55f25", "0xb6b55f25", "0xb6b55f25", "0xb6b55f25", "0xb6b55f25", "0xb6b55f25", "0xb6b55f25", "0xb6b55f25", "0xb6b55f25", "0xb6b55f25", "0xb6b55f25", "0xb6b55f25", "0xb6b55f25", "0xb6b55f25", "0xb6b55f25", "0xb6b55f25", "0xb6b55f25", "0xb6b55f25", "0xb6b55f25", "0xb6b55f25", "0xb6b55f25", "0xb6b55f25", "0xb6b55f25", "0xb6b55f25", "0xb6b55f25", "0xb6b55f25", "0xb6b55f25", "0xb6b55f25", "0xb6b55f25", "0xb6b55f25", "0xb6b55f25", "0xb6b55f25", "0xb6b55f25", "0xb6b55f25", "0xb6b55f25", "0xb6b55f25", "0xb6b55f25", "0xb6b55f25", "0xb6b55f25", "0xb6b55f25", "0xb6b55f25", "0xb6b55f25", "0xb6b55f25", "0xb6b55f25", "0xb6b55f25", "0xb6b55f25", "0xb6b55f25", "0xb6b55f25", "0xb6b55f25", "0xb6b55f25", "0xb6b55f25", "0xb6b55f25", "0xb6b55f25", "0xb6b55f25", "0xb6b55f25", "0xb6b55f25", "0xb6b55f25", "0xb6b55f25", "0xb6b55f25", "0xb6b55f25", "0xb6b55f25", "0xb6b55f25", "0xb6b55f25", "0xb6b55f25", "0xb6b55f25", "0xb6b55f25", "0x06447d56", "0x90c5013b", "0xe5d6bf02", "0xb6b55f25", "0x2e1a7d4d", "0xb6b55f25", "0x2e1a7d4d", "0xb6b55f25", "0x2e1a7d4d", "0xb6b55f25", "0x2e1a7d4d", "0xb6b55f25", "0x2e1a7d4d", "0xb6b55f25", "0x2e1a7d4d", "0xb6b55f25", "0x2e1a7d4d", "0xb6b55f25", "0x2e1a7d4d", "0xb6b55f25", "0x2e1a7d4d", "0xb6b55f25", "0x2e1a7d4d", "0xb6b55f25", "0x2e1a7d4d", "0xb6b55f25", "0x2e1a7d4d", "0xb6b55f25", "0x2e1a7d4d", "0xb6b55f25", "0x2e1a7d4d", "0xb6b55f25", "0x2e1a7d4d", "0xb6b55f25", "0x2e1a7d4d", "0xb6b55f25", "0x2e1a7d4d", "0xb6b55f25", "0x2e1a7d4d", "0xb6b55f25", "0x2e1a7d4d", "0xb6b55f25", "0x2e1a7d4d", "0xb6b55f25", "0x2e1a7d4d", "0xb6b55f25", "0x2e1a7d4d", "0xb6b55f25", "0x2e1a7d4d", "0xb6b55f25", "0x2e1a7d4d", "0xb6b55f25", "0x2e1a7d4d", "0xb6b55f25", "0x2e1a7d4d", "0xb6b55f25", "0x2e1a7d4d", "0xb6b55f25", "0x2e1a7d4d", "0xb6b55f25", "0x2e1a7d4d", "0xb6b55f25", "0x2e1a7d4d", "0xb6b55f25", "0x2e1a7d4d", "0xb6b55f25", "0x2e1a7d4d", "0xb6b55f25", "0x2e1a7d4d", "0xb6b55f25", "0x2e1a7d4d", "0xb6b55f25", "0x2e1a7d4d", "0xb6b55f25", "0x2e1a7d4d", "0xb6b55f25", "0x2e1a7d4d", "0xb6b55f25", "0x2e1a7d4d", "0xb6b55f25", "0x2e1a7d4d", "0xb6b55f25", "0x2e1a7d4d", "0xb6b55f25", "0x2e1a7d4d", "0xb6b55f25", "0x2e1a7d4d", "0xb6b55f25", "0x2e1a7d4d", "0xb6b55f25", "0x2e1a7d4d", "0xb6b55f25", "0x2e1a7d4d", "0xb6b55f25", "0x2e1a7d4d", "0xb6b55f25", "0x2e1a7d4d", "0xb6b55f25", "0x2e1a7d4d", "0xb6b55f25", "0x2e1a7d4d", "0xb6b55f25", "0x2e1a7d4d", "0xb6b55f25", "0x2e1a7d4d", "0xb6b55f25", "0x2e1a7d4d", "0xb6b55f25", "0x2e1a7d4d", "0xb6b55f25", "0x2e1a7d4d", "0xb6b55f25", "0x2e1a7d4d", "0xb6b55f25", "0x2e1a7d4d", "0xb6b55f25", "0x2e1a7d4d", "0xb6b55f25", "0x2e1a7d4d", "0xb6b55f25", "0x2e1a7d4d", "0xb6b55f25", "0x2e1a7d4d", "0xb6b55f25", "0x2e1a7d4d", "0xb6b55f25", "0x2e1a7d4d", "0xb6b55f25", "0x2e1a7d4d", "0xb6b55f25", "0x2e1a7d4d", "0xb6b55f25", "0x2e1a7d4d", "0xb6b55f25", "0x2e1a7d4d", "0xb6b55f25", "0x2e1a7d4d", "0xb6b55f25", "0x2e1a7d4d", "0xb6b55f25", "0x2e1a7d4d", "0xb6b55f25", "0x2e1a7d4d", "0xb6b55f25", "0x2e1a7d4d", "0xb6b55f25", "0x2e1a7d4d", "0xb6b55f25", "0x2e1a7d4d", "0xb6b55f25", "0x2e1a7d4d", "0xb6b55f25", "0x2e1a7d4d", "0xb6b55f25", "0x2e1a7d4d", "0xb6b55f25", "0x2e1a7d4d", "0xb6b55f25", "0x2e1a7d4d", "0xb6b55f25", "0x2e1a7d4d", "0xb6b55f25", "0x2e1a7d4d", "0xb6b55f25", "0x2e1a7d4d", "0xb6b55f25", "0x2e1a7d4d", "0xb6b55f25", "0x2e1a7d4d", "0xb6b55f25", "0x2e1a7d4d", "0xb6b55f25", "0x2e1a7d4d", "0xb6b55f25", "0x2e1a7d4d", "0xb6b55f25", "0x2e1a7d4d", "0xb6b55f25", "0x2e1a7d4d", "0xb6b55f25", "0x2e1a7d4d", "0xb6b55f25", "0x2e1a7d4d", "0xb6b55f25", "0x2e1a7d4d", "0xb6b55f25", "0x2e1a7d4d", "0xb6b55f25", "0x2e1a7d4d", "0xb6b55f25", "0x2e1a7d4d", "0xb6b55f25", "0x2e1a7d4d", "0xb6b55f25", "0x2e1a7d4d", "0xb6b55f25", "0x2e1a7d4d", "0xb6b55f25", "0x2e1a7d4d", "0xb6b55f25", "0x2e1a7d4d", "0xb6b55f25", "0x2e1a7d4d", "0xb6b55f25", "0x2e1a7d4d", "0xb6b55f25", "0x2e1a7d4d", "0xb6b55f25", "0x2e1a7d4d", "0xb6b55f25", "0x2e1a7d4d", "0xb6b55f25", "0x2e1a7d4d", "0xb6b55f25", "0x2e1a7d4d", "0xb6b55f25", "0x2e1a7d4d", "0xb6b55f25", "0x2e1a7d4d", "0xb6b55f25", "0x2e1a7d4d", "0xb6b55f25", "0x2e1a7d4d", "0xb6b55f25", "0x2e1a7d4d", "0xb6b55f25", "0x2e1a7d4d", "0xb6b55f25", "0x2e1a7d4d", "0xb6b55f25", "0x2e1a7d4d", "0xb6b55f25", "0x2e1a7d4d", "0xb6b55f25", "0x2e1a7d4d", "0xb6b55f25", "0x2e1a7d4d", "0xb6b55f25", "0x2e1a7d4d", "0xb6b55f25", "0x2e1a7d4d", "0xb6b55f25", "0x2e1a7d4d", "0xb6b55f25", "0x2e1a7d4d", "0xb6b55f25", "0x2e1a7d4d", "0xb6b55f25", "0x2e1a7d4d", "0xb6b55f25", "0x2e1a7d4d", "0xb6b55f25", "0x2e1a7d4d", "0xb6b55f25", "0x2e1a7d4d", "0xb6b55f25", "0x2e1a7d4d", "0xb6b55f25", "0x2e1a7d4d", "0xb6b55f25", "0x2e1a7d4d", "0xb6b55f25", "0x2e1a7d4d", "0xb6b55f25", "0x2e1a7d4d", "0xb6b55f25", "0x2e1a7d4d", "0xb6b55f25", "0x2e1a7d4d", "0xb6b55f25", "0x2e1a7d4d", "0xb6b55f25", "0x2e1a7d4d", "0xb6b55f25", "0x2e1a7d4d", "0xb6b55f25", "0x2e1a7d4d", "0xb6b55f25", "0x2e1a7d4d", "0xb6b55f25", "0x2e1a7d4d", "0xb6b55f25", "0x2e1a7d4d", "0xb6b55f25", "0x2e1a7d4d", "0xb6b55f25", "0x2e1a7d4d", "0xb6b55f25", "0x2e1a7d4d", "0xb6b55f25", "0x2e1a7d4d", "0xb6b55f25", "0x2e1a7d4d", "0xb6b55f25", "0x2e1a7d4d", "0xb6b55f25", "0x2e1a7d4d", "0xb6b55f25", "0x2e1a7d4d", "0xb6b55f25", "0x2e1a7d4d", "0xb6b55f25", "0x2e1a7d4d", "0xb6b55f25", "0x2e1a7d4d", "0xb6b55f25", "0x2e1a7d4d", "0xb6b55f25", "0x2e1a7d4d", "0xb6b55f25", "0x2e1a7d4d", "0xb6b55f25", "0x2e1a7d4d", "0xb6b55f25", "0x2e1a7d4d", "0xb6b55f25", "0x2e1a7d4d", "0xb6b55f25", "0x2e1a7d4d", "0xb6b55f25", "0x2e1a7d4d", "0xb6b55f25", "0x2e1a7d4d", "0xb6b55f25", "0x2e1a7d4d", "0xb6b55f25", "0x2e1a7d4d", "0xb6b55f25", "0x2e1a7d4d", "0xb6b55f25", "0x2e1a7d4d", "0xb6b55f25", "0x2e1a7d4d", "0xb6b55f25", "0x2e1a7d4d", "0xb6b55f25", "0x2e1a7d4d", "0xb6b55f25", "0x2e1a7d4d", "0xb6b55f25", "0x2e1a7d4d", "0xb6b55f25", "0x2e1a7d4d", "0xb6b55f25", "0x2e1a7d4d", "0xb6b55f25", "0x2e1a7d4d", "0xb6b55f25", "0x2e1a7d4d", "0xb6b55f25", "0x2e1a7d4d", "0xb6b55f25", "0x2e1a7d4d", "0xb6b55f25", "0x2e1a7d4d", "0xb6b55f25", "0x2e1a7d4d", "0xb6b55f25", "0x2e1a7d4d", "0xb6b55f25", "0x2e1a7d4d", "0xb6b55f25", "0x2e1a7d4d", "0xb6b55f25", "0x2e1a7d4d", "0xb6b55f25", "0x2e1a7d4d", "0xb6b55f25", "0x2e1a7d4d", "0xb6b55f25", "0x2e1a7d4d", "0xb6b55f25", "0x2e1a7d4d", "0xb6b55f25", "0x2e1a7d4d", "0xb6b55f25", "0x2e1a7d4d", "0xb6b55f25", "0x2e1a7d4d", "0xb6b55f25", "0x2e1a7d4d", "0xb6b55f25", "0x2e1a7d4d", "0xb6b55f25", "0x2e1a7d4d", "0xb6b55f25", "0x2e1a7d4d", "0xb6b55f25", "0x2e1a7d4d", "0xb6b55f25", "0x2e1a7d4d", "0xb6b55f25", "0x2e1a7d4d", "0xb6b55f25", "0x2e1a7d4d", "0xb6b55f25", "0x2e1a7d4d", "0xb6b55f25", "0x2e1a7d4d", "0xb6b55f25", "0x2e1a7d4d", "0xb6b55f25", "0x2e1a7d4d", "0x06447d56", "0x90c5013b"], "exploit_name": "DYNA_exp", "function_sigs": ["0x266cf109", "0xe5d6bf02", "0x65bc9481", "0xc657c718", "0x90c5013b", "0x095ea7b3", "0x70a08231", "0x06447d56", "0xa9059cbb", "0x022c0d9f", "0xb6b55f25", "0x2e1a7d4d", "0x70ca10bb", "0x5c11d795"]}, {"calls": ["0x06447d56", "0x01c0a31a"], "exploit_name": "LiFi_exp", "function_sigs": ["0x01c0a31a", "0x06447d56"]}]