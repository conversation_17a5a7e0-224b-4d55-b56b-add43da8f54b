// SPDX-License-Identifier: UNLICENSED
pragma solidity ^0.8.13;

import "forge-std/Test.sol";

{{#if is_onchain}}
// ityfuzz evm -o -t {{target}} -c {{chain}} --onchain-block-number {{block_number}} -f -i -p --onchain-etherscan-api-key ${{etherscan_keyname}}
{{else}}
// ityfuzz evm -t '{{target}}' -f
{{/if}}
/*

😊😊 Found violations!


{{{solution}}}
 */

contract {{contract_name}} is Test {
{{#if struct_defs}}
{{#each struct_defs}}
    struct {{this.name}} {
        {{#each this.props}}
        {{{this}}};
        {{/each}}
    }
{{/each}}

{{/if}}
    function setUp() public {
        {{#if is_onchain}}
        vm.createSelectFork("{{chain}}", {{block_number}});
        {{/if}}
    }

    function test() public {
    {{#if router}}
        address router = {{router}};

    {{/if}}
{{#each trace}}
        vm.prank({{caller}});
{{#with this}}
    {{#if interface_calls}}
        {{#each interface_calls}}
        {{{this}}}
        {{/each}}
    {{else}}
    {{#if (is_deposit buy_type)}}
        vm.deal({{caller}}, {{value}});
        {{#with (lookup swap_data "deposit")~}}I({{target}}).deposit{value: {{../value}}}();{{/with}}
    {{else}}
    {{#if (is_buy buy_type)}}
        address[] memory path{{borrow_idx}} = new address[]();
    {{#with (lookup swap_data "buy")~}}
        {{#each path}}
        path{{../../borrow_idx}}[{{@index}}] = {{{this}}};
        {{/each}}
    {{/with}}
        vm.deal({{caller}}, {{value}});
        IUniswapV2Router(router).swapExactETHForTokensSupportingFeeOnTransferTokens{
            value: {{value}}
        }(0, path{{borrow_idx}}, address(this), block.timestamp);
    {{else}}
    {{#if value}}
        vm.deal({{caller}}, {{value}});
    {{/if}}
        {{contract}}.call{{#if value}}{value: {{value}}}{{/if}}(abi.encodeWithSelector(
            {{fn_selector}}{{#if fn_args}}, {{{fn_args}}}{{/if}}
        ));
    {{/if}}
    {{/if}}
    {{/if}}
    {{#if (is_withdraw sell_type)}}
        vm.startPrank({{caller}});
        uint256 amount{{balance_idx}} = IERC20({{contract}}).balanceOf(address(this));
        {{#with (lookup swap_data "withdraw")~}}I({{target}}).withdraw(amount{{../balance_idx}});{{/with}}
        vm.stopPrank();
    {{else}}
    {{#if (is_sell sell_type)}}
        vm.startPrank({{caller}});
        uint256 amount{{balance_idx}} = IERC20({{contract}}).balanceOf(address(this));
        IERC20({{contract}}).approve(router, amount{{balance_idx}});
        address[] memory liq_path{{balance_idx}} = new address[]();
    {{#with (lookup swap_data "sell")~}}
        {{#each path}}
        liq_path{{../../balance_idx}}[{{@index}}] = {{{this}}};
        {{/each}}
    {{/with}}
        vm.deal({{caller}}, amount{{balance_idx}});
        IUniswapV2Router(router).swapExactTokensForETHSupportingFeeOnTransferTokens(
            amount{{balance_idx}}, 0, liq_path{{balance_idx}}, address(this), block.timestamp
        );
        vm.stopPrank();
    {{/if}}
    {{/if}}
{{/with}}
{{/each}}
    }

{{#if stepping_with_return}}
    // Stepping with return
    receive() external payable {}
{{/if}}
}
{{#if interface}}

interface I {
{{#each interface}}
    {{{this}}}
{{/each}}
}
{{/if}}
{{#if include_interface}}

interface IERC20 {
    function balanceOf(address owner) external view returns (uint256);
    function approve(address spender, uint256 value) external returns (bool);
    function transfer(address to, uint256 value) external returns (bool);
    function transferFrom(address from, address to, uint256 value) external returns (bool);

    function mint(address to) external returns (uint liquidity);
    function burn(address to) external returns (uint amount0, uint amount1);
    function skim(address to) external;
    function sync() external;
}

interface IUniswapV2Router {
    function swapExactTokensForTokensSupportingFeeOnTransferTokens(
        uint256 amountIn,
        uint256 amountOutMin,
        address[] calldata path,
        address to,
        uint256 deadline
    ) external;
    function swapExactETHForTokensSupportingFeeOnTransferTokens(
        uint256 amountOutMin,
        address[] calldata path,
        address to,
        uint256 deadline
    ) external payable;
    function swapExactTokensForETHSupportingFeeOnTransferTokens(
        uint256 amountIn,
        uint256 amountOutMin,
        address[] calldata path,
        address to,
        uint256 deadline
    ) external;
}
{{/if}}
