[package]
name = "ityfuzz"
version = "0.1.0"
edition = "2021"
build = "src/build.rs"

# See more keys and their definitions at https://doc.rust-lang.org/cargo/reference/manifest.html
[dev-dependencies]
criterion = "0.4.0"

[features]
default = [
    "cmp",
    "dataflow",
    "evm",
    "print_txn_corpus",
    "full_trace",
    "force_cache",
    "real_balance",
]
evm = []
cmp = []
dataflow = []
evaluation = []
ondisk_corpus = []
# need to turn off when running DV contracts
deployer_is_attacker = []
print_infant_corpus = []
print_txn_corpus = []
fuzz_static = []
real_balance = []
full_trace = []
force_cache = []
use_presets = []
print_logs = []
z3_debug = []
sui_support = [
    "dep:move-binary-format",
    "dep:move-core-types",
    "dep:move-stdlib",
    "dep:move-vm-runtime",
    "dep:move-vm-types",
    "dep:sui-move-natives-latest",
    "dep:sui-protocol-config",
    "dep:sui-types",
]
debug = []
flashloan_debug = []
no_etherscan = []


[dependencies]
bytes = { version = "1.2.1", features = ["serde"] }
retry = "2.0.0"
serde_cbor = "0.11.2"
clap = { version = "4.4.4", features = ["derive"] }
sentry = "0.32.1"
rlp = "0.5.2"
ethers = "2.0.14"

hex = "0.4"
primitive-types = { version = "0.12.1", features = ["rlp", "serde"] }
libafl = "=0.11.2"
libafl_bolts = "=0.11.2"
rand = "0.8.5"
nix = "0.27.1"
serde = "1.0.147"
serde_traitobject = "0.2.8"
serde_json = "1.0.73"
z3 = { version = "0.12.0", features = ["static-link-z3"] }
z3-sys = "0.8.1"
glob = "0.3.0"
rust-crypto = "0.2"
itertools = "0.10.2"
reqwest = { version = "0.11", features = ["blocking", "json"] }
once_cell = "1.8.0"
permutator = "0.4.3"
either = "1.8.0"
regex = "1"
typetag = "0.2.13"
lazy_static = "1.4.0"
num_cpus = "1.0"

revm = { git = "https://github.com/fuzzland/revm", rev = "1dead51", features = [
    "no_gas_measuring",
    "serde",
    "memory_limit",
] }
revm-primitives = { git = "https://github.com/fuzzland/revm", rev = "1dead51", features = [
    "no_gas_measuring",
    "serde",
    "memory_limit",
] }
revm-interpreter = { git = "https://github.com/fuzzland/revm", rev = "1dead51", features = [
    "no_gas_measuring",
    "serde",
    "memory_limit",
] }

move-binary-format = { git = "https://github.com/fuzzland/ityfuzz-sui-fork.git", optional = true }
move-core-types = { git = "https://github.com/fuzzland/ityfuzz-sui-fork.git", features = [
    "address32",
], optional = true }
move-stdlib = { git = "https://github.com/fuzzland/ityfuzz-sui-fork.git", optional = true }
move-vm-runtime = { git = "https://github.com/fuzzland/ityfuzz-sui-fork.git", features = [
    "lazy_natives",
], optional = true }
move-vm-types = { git = "https://github.com/fuzzland/ityfuzz-sui-fork.git", optional = true }
sui-move-natives-latest = { git = "https://github.com/fuzzland/ityfuzz-sui-fork.git", optional = true }
sui-protocol-config = { git = "https://github.com/fuzzland/ityfuzz-sui-fork.git", optional = true }
sui-types = { git = "https://github.com/fuzzland/ityfuzz-sui-fork.git", optional = true }

# template engine
handlebars = "4.4"

# for cheatcode middleware
foundry-cheatcodes = { git = "https://github.com/foundry-rs/foundry.git", rev = "617dfc28" }
alloy-sol-types = "0.6"
alloy-dyn-abi = { version = "0.6", features = ["arbitrary", "eip712"] }
alloy-primitives = "0.6"
alloy-json-abi = "0.6"
# error handling
anyhow = "1.0"
thiserror = "1.0"
# logging
tracing = "0.1"
tracing-subscriber = "0.3"
colored = "2.0"
evmole = "0.3.8"
semver = "1.0.22"

