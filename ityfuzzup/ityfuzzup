#!/usr/bin/env bash
set -e

BASE_DIR=${XDG_CONFIG_HOME:-$HOME}
ITYFUZZ_DIR=${ITYFUZZ_DIR:-"$BASE_DIR/.ityfuzz"}
ITYFUZZ_BIN_DIR="$ITYFUZZ_DIR/bin"
BINS=(ityfuzz)

export RUSTFLAGS="-C target-cpu=native -A warnings"

main() {
  need_cmd git
  need_cmd curl

  while [[ $1 ]]; do
    case $1 in
      --)               shift; break;;

      -r|--repo)        shift; ITYFUZZ_REPO=$1;;
      -b|--branch)      shift; ITYFUZZ_BRANCH=$1;;
      -v|--version)     shift; ITYFUZZ_VERSION=$1;;
      -p|--path)        shift; ITYFUZZ_LOCAL_REPO=$1;;
      -P|--pr)          shift; ITYFUZZ_PR=$1;;
      -C|--commit)      shift; ITYFUZZ_COMMIT=$1;;
      -h|--help)
        usage
        exit 0
        ;;
      *)
        warn "unknown option: $1"
        usage
        exit 1
    esac; shift
  done

  # Print the banner after successfully parsing args
  banner

  if [ -n "$ITYFUZZ_PR" ]; then
    if [ -z "$ITYFUZZ_BRANCH" ]; then
      ITYFUZZ_BRANCH="refs/pull/$ITYFUZZ_PR/head"
    else
      err "can't use --pr and --branch at the same time"
    fi
  fi

  # Installs ITYFUZZ from a local repository if --path parameter is provided
  if [[ -n "$ITYFUZZ_LOCAL_REPO" ]]; then
    need_cmd cargo

    # Ignore branches/versions as we do not want to modify local git state
    if [ -n "$ITYFUZZ_REPO" ] || [ -n "$ITYFUZZ_BRANCH" ] || [ -n "$ITYFUZZ_VERSION" ]; then
      warn "--branch, --version, and --repo arguments are ignored during local install"
    fi

    # Enter local repo and build
    say "installing from $ITYFUZZ_LOCAL_REPO"
    cd "$ITYFUZZ_LOCAL_REPO"
    ensure cargo build --bins --release --features "cmp dataflow evm print_txn_corpus full_trace" --no-default-features # need 4 speed

    for bin in "${BINS[@]}"; do
      # Remove prior installations if they exist
      rm -f "$ITYFUZZ_BIN_DIR/$bin"
      # Symlink from local repo binaries to bin dir
      ensure ln -s "$PWD/target/release/$bin" "$ITYFUZZ_BIN_DIR/$bin"
    done

    say "done"
    exit 0
  fi

  ITYFUZZ_REPO=${ITYFUZZ_REPO:-fuzzland/ityfuzz}

  # Install by downloading binaries
  if [[ "$ITYFUZZ_REPO" == "fuzzland/ityfuzz" && -z "$ITYFUZZ_BRANCH" && -z "$ITYFUZZ_COMMIT" ]]; then
    ITYFUZZ_VERSION=${ITYFUZZ_VERSION:-nightly}
    ITYFUZZ_TAG=$ITYFUZZ_VERSION

    # Normalize versions (handle channels, versions without v prefix
    if [[ "$ITYFUZZ_VERSION" == "nightly" ]]; then
      # Locate real nightly tag
      SHA=$(ensure curl -sSf "https://api.github.com/repos/$ITYFUZZ_REPO/git/refs/tags/nightly" \
        | grep -Eo '"sha"[^,]*' \
        | grep -Eo '[^:]*$' \
        | tr -d '"' \
        | tr -d ' ' \
        | cut -d ':' -f2 )
      if [ -n "$SHA" ]; then
        ITYFUZZ_TAG="nightly-${SHA}"
      fi
    elif [[ "$ITYFUZZ_VERSION" == nightly* ]]; then
      ITYFUZZ_VERSION="nightly"
    elif [[ "$ITYFUZZ_VERSION" == [[:digit:]]* ]]; then
      # Add v prefix
      ITYFUZZ_VERSION="v${ITYFUZZ_VERSION}"
      ITYFUZZ_TAG="${ITYFUZZ_VERSION}"
    fi

    say "installing ityfuzz (version ${ITYFUZZ_VERSION}, tag ${ITYFUZZ_TAG})"

    PLATFORM="$(uname -s)"
    EXT="tar.gz"
    case $PLATFORM in
      Linux)
        PLATFORM="linux"
        ;;
      Darwin)
        PLATFORM="darwin"
        ;;
      MINGW*)
        EXT="zip"
        PLATFORM="win32"
        ;;
      *)
        err "unsupported platform: $PLATFORM"
        ;;
    esac

    ARCHITECTURE="$(uname -m)"
    if [ "${ARCHITECTURE}" = "x86_64" ]; then
      # Redirect stderr to /dev/null to avoid printing errors if non Rosetta.
      if [ "$(sysctl -n sysctl.proc_translated 2>/dev/null)" = "1" ]; then
        ARCHITECTURE="arm64" # Rosetta.
      else
        ARCHITECTURE="amd64" # Intel.
      fi
    elif [ "${ARCHITECTURE}" = "arm64" ] ||[ "${ARCHITECTURE}" = "aarch64" ] ; then
      ARCHITECTURE="arm64" # Arm.
    else
      ARCHITECTURE="amd64" # Amd.
    fi

    # Compute the URL of the release tarball in the ityfuzz repository.
    RELEASE_URL="https://github.com/${ITYFUZZ_REPO}/releases/download/${ITYFUZZ_TAG}/"
    BIN_ARCHIVE_URL="${RELEASE_URL}ityfuzz_${ITYFUZZ_VERSION}_${PLATFORM}_${ARCHITECTURE}.$EXT"

    # Download and extract the binaries archive
    say "downloading latest ityfuzz binaries"
    if [ "$PLATFORM" = "win32" ]; then
      tmp="$(mktemp -d 2>/dev/null || echo ".")/ityfuzz.zip"
      ensure download "$BIN_ARCHIVE_URL" "$tmp"
      ensure unzip "$tmp" -d "$ITYFUZZ_BIN_DIR"
      rm -f "$tmp"
    else
      mkdir -p "$ITYFUZZ_BIN_DIR"
      ensure download "$BIN_ARCHIVE_URL" | ensure tar -xzC "$ITYFUZZ_BIN_DIR"
    fi

    for bin in "${BINS[@]}"; do
      bin_path="$ITYFUZZ_BIN_DIR/$bin"

      # Print installed msg
      say "installed - $(ensure "$bin_path" --version)"

      # Check if the default path of the binary is not in ITYFUZZ_BIN_DIR
      which_path="$(which "$bin")"
      if [ "$which_path" != "$bin_path" ]; then
        warn ""
        cat 1>&2 <<EOF
There are multiple binaries with the name '$bin' present in your 'PATH'.
This may be the result of installing '$bin' using another method,
like Cargo or other package managers.
You may need to run 'rm $which_path' or move '$ITYFUZZ_BIN_DIR'
in your 'PATH' to allow the newly installed version to take precedence!

EOF
      fi
    done

    say "done!"

  # Install by cloning the repo with the provided branch/tag
  else
    need_cmd cargo
    ITYFUZZ_BRANCH=${ITYFUZZ_BRANCH:-master}
    REPO_PATH="$ITYFUZZ_DIR/$ITYFUZZ_REPO"

    # If repo path does not exist, grab the author from the repo, make a directory in .ityfuzz, cd to it and clone.
    if [ ! -d "$REPO_PATH" ]; then
      AUTHOR="$(echo "$ITYFUZZ_REPO" | cut -d'/' -f1 -)"
      ensure mkdir -p "$ITYFUZZ_DIR/$AUTHOR"
      cd "$ITYFUZZ_DIR/$AUTHOR"
      ensure git clone "https://github.com/$ITYFUZZ_REPO"
    fi

    # Force checkout, discarding any local changes
    cd "$REPO_PATH"
    ensure git fetch origin "${ITYFUZZ_BRANCH}:remotes/origin/${ITYFUZZ_BRANCH}"
    ensure git checkout "origin/${ITYFUZZ_BRANCH}"

    # If set, checkout specific commit from branch
    if [ -n "$ITYFUZZ_COMMIT" ]; then
      say "installing at commit $ITYFUZZ_COMMIT"
      ensure git checkout "$ITYFUZZ_COMMIT"
    fi

    # Build the repo and install the binaries locally to the .ityfuzz bin directory.
    ensure cargo build --bins --release --features "cmp dataflow evm print_txn_corpus full_trace" --no-default-features
    for bin in "${BINS[@]}"; do
      for try_path in target/release/$bin target/release/$bin.exe; do
        if [ -f "$try_path" ]; then
          [ -e "$ITYFUZZ_BIN_DIR/$bin" ] && warn "overwriting existing $bin in $ITYFUZZ_BIN_DIR"
          mv -f "$try_path" "$ITYFUZZ_BIN_DIR"
        fi
      done
    done

    say "done"
  fi

  $ITYFUZZ_BIN_DIR/ityfuzz --help > /dev/null || {
    warn "failed to run ityfuzz"
    err "please consider installing 'libssl-dev' or other missing dependencies"
  }
}

usage() {
  cat 1>&2 <<EOF
The installer for ityfuzz.

Update or revert to a specific ITYFUZZ version with ease.

USAGE:
    ITYFUZZ <OPTIONS>

OPTIONS:
    -h, --help      Print help information
    -v, --version   Install a specific version
    -b, --branch    Install a specific branch
    -P, --pr        Install a specific Pull Request
    -C, --commit    Install a specific commit
    -r, --repo      Install from a remote GitHub repo (uses default branch if no other options are set)
    -p, --path      Install a local repository
EOF
}

say() {
  printf "ityfuzz: %s\n" "$1"
}

warn() {
  say "warning: ${1}" >&2
}

err() {
  say "$1" >&2
  exit 1
}

need_cmd() {
  if ! check_cmd "$1"; then
    err "need '$1' (command not found)"
  fi
}

check_cmd() {
  command -v "$1" &>/dev/null
}

# Run a command that should never fail. If the command fails execution
# will immediately terminate with an error showing the failing
# command.
ensure() {
  if ! "$@"; then err "command failed: $*"; fi
}

# Downloads $1 into $2 or stdout
download() {
  if [ "$2" ]; then
    # output into $2
    if check_cmd curl; then
      curl -#o "$2" -L "$1"
    else
      wget --show-progress -qO "$2" "$1"
    fi
  else
    # output to stdout
    if check_cmd curl; then
      curl -#L "$1"
    else
      wget --show-progress -qO- "$1"
    fi
  fi
}

# Banner Function for ITYFUZZ
banner() {
  printf '
IIIIIIIIII         tttt                                   FFFFFFFFFFFFFFFFFFFFFF
I::::::::I      ttt:::t                                   F::::::::::::::::::::F
I::::::::I      t:::::t                                   F::::::::::::::::::::F
II::::::II      t:::::t                                   FF::::::FFFFFFFFF::::F
  I::::I  ttttttt:::::ttttttt    yyyyyyy           yyyyyyy  F:::::F       FFFFFFuuuuuu    uuuuuu  zzzzzzzzzzzzzzzzzzzzzzzzzzzzzzzzzz
  I::::I  t:::::::::::::::::t     y:::::y         y:::::y   F:::::F             u::::u    u::::u  z:::::::::::::::zz:::::::::::::::z
  I::::I  t:::::::::::::::::t      y:::::y       y:::::y    F::::::FFFFFFFFFF   u::::u    u::::u  z::::::::::::::z z::::::::::::::z
  I::::I  tttttt:::::::tttttt       y:::::y     y:::::y     F:::::::::::::::F   u::::u    u::::u  zzzzzzzz::::::z  zzzzzzzz::::::z
  I::::I        t:::::t              y:::::y   y:::::y      F:::::::::::::::F   u::::u    u::::u        z::::::z         z::::::z
  I::::I        t:::::t               y:::::y y:::::y       F::::::FFFFFFFFFF   u::::u    u::::u       z::::::z         z::::::z
  I::::I        t:::::t                y:::::y:::::y        F:::::F             u::::u    u::::u      z::::::z         z::::::z
  I::::I        t:::::t    tttttt       y:::::::::y         F:::::F             u:::::uuuu:::::u     z::::::z         z::::::z
II::::::II      t::::::tttt:::::t        y:::::::y        FF:::::::FF           u:::::::::::::::uu  z::::::zzzzzzzz  z::::::zzzzzzzz
I::::::::I      tt::::::::::::::t         y:::::y         F::::::::FF            u:::::::::::::::u z::::::::::::::z z::::::::::::::z
I::::::::I        tt:::::::::::tt        y:::::y          F::::::::FF             uu::::::::uu:::uz:::::::::::::::zz:::::::::::::::z
IIIIIIIIII          ttttttttttt         y:::::y           FFFFFFFFFFF               uuuuuuuu  uuuuzzzzzzzzzzzzzzzzzzzzzzzzzzzzzzzzzz
                                       y:::::y
                                      y:::::y
                                     y:::::y
                                    y:::::y
                                   yyyyyyy
'
}


main "$@" || exit 1
